{"Device": "GW2A-18C", "Files": [{"Path": "G:/Gowin/workspace/TangPrimer-20K-example-main/Litex/sipeed_tang_primer_20k/src/VexRiscv.v", "Type": "verilog"}, {"Path": "G:/Gowin/workspace/TangPrimer-20K-example-main/Litex/sipeed_tang_primer_20k/src/sipeed_tang_primer_20k.v", "Type": "verilog"}], "IncludePath": [], "LoopLimit": 2000, "ResultFile": "G:/Gowin/workspace/TangPrimer-20K-example-main/Litex/sipeed_tang_primer_20k/impl/temp/rtl_parser.result", "Top": "", "VerilogStd": "verilog_2001", "VhdlStd": "vhdl_93"}