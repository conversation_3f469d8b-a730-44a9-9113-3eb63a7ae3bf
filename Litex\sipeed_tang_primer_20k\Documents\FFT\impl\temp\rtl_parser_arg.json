{"Device": "GW2A-18C", "Files": [{"Path": "G:/Gowin/workspace/TangPrimer-20K-example-main/Litex/sipeed_tang_primer_20k/Documents/FFT/src/FFT_Top/fft.v", "Type": "verilog"}, {"Path": "G:/Gowin/workspace/TangPrimer-20K-example-main/Litex/sipeed_tang_primer_20k/Documents/FFT/src/counter.v", "Type": "verilog"}, {"Path": "G:/Gowin/workspace/TangPrimer-20K-example-main/Litex/sipeed_tang_primer_20k/Documents/FFT/src/deUstb.v", "Type": "verilog"}, {"Path": "G:/Gowin/workspace/TangPrimer-20K-example-main/Litex/sipeed_tang_primer_20k/Documents/FFT/src/downCnt.v", "Type": "verilog"}, {"Path": "G:/Gowin/workspace/TangPrimer-20K-example-main/Litex/sipeed_tang_primer_20k/Documents/FFT/src/gowin_rpll.v", "Type": "verilog"}, {"Path": "G:/Gowin/workspace/TangPrimer-20K-example-main/Litex/sipeed_tang_primer_20k/Documents/FFT/src/testfft.v", "Type": "verilog"}, {"Path": "G:/Gowin/workspace/TangPrimer-20K-example-main/Litex/sipeed_tang_primer_20k/Documents/FFT/src/testfft_top.v", "Type": "verilog"}], "IncludePath": [], "LoopLimit": 2000, "ResultFile": "G:/Gowin/workspace/TangPrimer-20K-example-main/Litex/sipeed_tang_primer_20k/Documents/FFT/impl/temp/rtl_parser.result", "Top": "testfft_top", "VerilogStd": "sysv", "VhdlStd": "vhdl_93"}