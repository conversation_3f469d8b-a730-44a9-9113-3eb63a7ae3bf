<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>Timing Analysis Report</title>
<style type="text/css">
@import url(../temp/style.css);
body { font-family: Verdana, Arial, sans-serif; font-size: 12px; }
div#content { width: 100%; margin: }
hr { margin-top: 30px; margin-bottom: 30px; }
h1, h3 { text-align: center; }
h1 {margin-top: 50px; }
table, th, td {white-space:pre; border: 1px solid #aaa; }
table { border-collapse:collapse; margin-top: 10px; margin-bottom: 20px; width: 100%; }
th, td { padding: 5px 5px 5px 5px; }
th { color: #fff; font-weight: bold; background-color: #0084ff; }
table.summary_table td.label { width: 24%; min-width: 200px; background-color: #dee8f4; }
table.detail_table th.label {  min-width: 8%; width: 8%; }
</style>
</head>
<body>
<div id="content">
<h1><a name="Message">Timing Messages</a></h1>
<table class="summary_table">
<tr>
<td class="label">Report Title</td>
<td>Timing Analysis Report</td>
</tr>
<tr>
<td class="label">Design File</td>
<td>G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\Documents\FFT\impl\gwsynthesis\FFT.vg</td>
</tr>
<tr>
<td class="label">Physical Constraints File</td>
<td>G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\Documents\FFT\src\FFT.cst</td>
</tr>
<tr>
<td class="label">Timing Constraint File</td>
<td>G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\Documents\FFT\src\FFT.sdc</td>
</tr>
<tr>
<td class="label">Tool Version</td>
<td>V1.9.11.03 Education</td>
</tr>
<tr>
<td class="label">Part Number</td>
<td>GW2A-LV18PG256C8/I7</td>
</tr>
<tr>
<td class="label">Device</td>
<td>GW2A-18</td>
</tr>
<tr>
<td class="label">Device Version</td>
<td>C</td>
</tr>
<tr>
<td class="label">Created Time</td>
<td>Wed Jul 30 11:38:53 2025
</td>
</tr>
<tr>
<td class="label">Legal Announcement</td>
<td>Copyright (C)2014-2025 Gowin Semiconductor Corporation. All rights reserved.</td>
</tr>
</table>
<h1><a name="Summary">Timing Summaries</a></h1>
<h2><a name="STA_Tool_Run_Summary">STA Tool Run Summary:</a></h2>
<table class="summary_table">
<tr>
<td class="label">Setup Delay Model</td>
<td>Slow 0.95V 85C C8/I7</td>
</tr>
<tr>
<td class="label">Hold Delay Model</td>
<td>Fast 1.05V 0C C8/I7</td>
</tr>
<tr>
<td class="label">Numbers of Paths Analyzed</td>
<td>3659</td>
</tr>
<tr>
<td class="label">Numbers of Endpoints Analyzed</td>
<td>2527</td>
</tr>
<tr>
<td class="label">Numbers of Falling Endpoints</td>
<td>0</td>
</tr>
<tr>
<td class="label">Numbers of Setup Violated Endpoints</td>
<td>3</td>
</tr>
<tr>
<td class="label">Numbers of Hold Violated Endpoints</td>
<td>1</td>
</tr>
</table>
<h2><a name="Clock_Report">Clock Summary:</a></h2>
<table class="detail_table">
<tr>
<th class="label">NO.</th>
<th class="label">Clock Name</th>
<th class="label">Type</th>
<th class="label">Period</th>
<th class="label">Frequency(MHz)</th>
<th class="label">Rise</th>
<th class="label">Fall</th>
<th class="label">Source</th>
<th class="label">Master</th>
<th class="label">Objects</th>
</tr>
<tr>
<td>1</td>
<td>clk_in</td>
<td>Base</td>
<td>20.000</td>
<td>50.000
<td>0.000</td>
<td>10.000</td>
<td></td>
<td></td>
<td>clk </td>
</tr>
<tr>
<td>2</td>
<td>myclk</td>
<td>Base</td>
<td>10.000</td>
<td>100.000
<td>0.000</td>
<td>5.000</td>
<td></td>
<td></td>
<td>sysClk </td>
</tr>
<tr>
<td>3</td>
<td>tck_pad_i</td>
<td>Base</td>
<td>50.000</td>
<td>20.000
<td>0.000</td>
<td>25.000</td>
<td></td>
<td></td>
<td>tck_ibuf/I </td>
</tr>
<tr>
<td>4</td>
<td>pll_fft/rpll_inst/CLKOUT.default_gen_clk</td>
<td>Generated</td>
<td>100.000</td>
<td>10.000
<td>0.000</td>
<td>50.000</td>
<td>clk_ibuf/I</td>
<td>clk_in</td>
<td>pll_fft/rpll_inst/CLKOUT </td>
</tr>
<tr>
<td>5</td>
<td>pll_fft/rpll_inst/CLKOUTP.default_gen_clk</td>
<td>Generated</td>
<td>100.000</td>
<td>10.000
<td>0.000</td>
<td>50.000</td>
<td>clk_ibuf/I</td>
<td>clk_in</td>
<td>pll_fft/rpll_inst/CLKOUTP </td>
</tr>
<tr>
<td>6</td>
<td>pll_fft/rpll_inst/CLKOUTD.default_gen_clk</td>
<td>Generated</td>
<td>200.000</td>
<td>5.000
<td>0.000</td>
<td>100.000</td>
<td>clk_ibuf/I</td>
<td>clk_in</td>
<td>pll_fft/rpll_inst/CLKOUTD </td>
</tr>
<tr>
<td>7</td>
<td>pll_fft/rpll_inst/CLKOUTD3.default_gen_clk</td>
<td>Generated</td>
<td>300.000</td>
<td>3.333
<td>0.000</td>
<td>150.000</td>
<td>clk_ibuf/I</td>
<td>clk_in</td>
<td>pll_fft/rpll_inst/CLKOUTD3 </td>
</tr>
</table>
<h2><a name="Max_Frequency_Report">Max Frequency Summary:</a></h2>
<table>
<tr>
<th>NO.</th>
<th>Clock Name</th>
<th>Constraint</th>
<th>Actual Fmax</th>
<th>Logic Level</th>
<th>Entity</th>
</tr>
<tr>
<td>1</td>
<td>clk_in</td>
<td>50.000(MHz)</td>
<td>278.043(MHz)</td>
<td>4</td>
<td>TOP</td>
</tr>
<tr>
<td>2</td>
<td>myclk</td>
<td>100.000(MHz)</td>
<td>853.227(MHz)</td>
<td>2</td>
<td>TOP</td>
</tr>
<tr>
<td>3</td>
<td>tck_pad_i</td>
<td>20.000(MHz)</td>
<td>131.548(MHz)</td>
<td>6</td>
<td>TOP</td>
</tr>
<tr>
<td>4</td>
<td>pll_fft/rpll_inst/CLKOUT.default_gen_clk</td>
<td>10.000(MHz)</td>
<td>100.083(MHz)</td>
<td>3</td>
<td>TOP</td>
</tr>
</table>
<h4>No timing paths to get frequency of pll_fft/rpll_inst/CLKOUTP.default_gen_clk!</h4>
<h4>No timing paths to get frequency of pll_fft/rpll_inst/CLKOUTD.default_gen_clk!</h4>
<h4>No timing paths to get frequency of pll_fft/rpll_inst/CLKOUTD3.default_gen_clk!</h4>
<h2><a name="Total_Negative_Slack_Report">Total Negative Slack Summary:</a></h2>
<table class="detail_table">
<tr>
<th class="label">Clock Name</th>
<th class="label">Analysis Type</th>
<th class="label">Endpoints TNS</th>
<th class="label">Number of Endpoints</th>
</tr>
<tr>
<td>clk_in</td>
<td>Setup</td>
<td>0.000</td>
<td>0</td>
</tr>
<tr>
<td>clk_in</td>
<td>Hold</td>
<td>0.000</td>
<td>0</td>
</tr>
<tr>
<td>myclk</td>
<td>Setup</td>
<td>0.000</td>
<td>0</td>
</tr>
<tr>
<td>myclk</td>
<td>Hold</td>
<td>0.000</td>
<td>0</td>
</tr>
<tr>
<td>tck_pad_i</td>
<td>Setup</td>
<td>0.000</td>
<td>0</td>
</tr>
<tr>
<td>tck_pad_i</td>
<td>Hold</td>
<td>0.000</td>
<td>0</td>
</tr>
<tr>
<td>pll_fft/rpll_inst/CLKOUT.default_gen_clk</td>
<td>Setup</td>
<td>0.000</td>
<td>0</td>
</tr>
<tr>
<td>pll_fft/rpll_inst/CLKOUT.default_gen_clk</td>
<td>Hold</td>
<td>0.000</td>
<td>0</td>
</tr>
<tr>
<td>pll_fft/rpll_inst/CLKOUTP.default_gen_clk</td>
<td>Setup</td>
<td>0.000</td>
<td>0</td>
</tr>
<tr>
<td>pll_fft/rpll_inst/CLKOUTP.default_gen_clk</td>
<td>Hold</td>
<td>0.000</td>
<td>0</td>
</tr>
<tr>
<td>pll_fft/rpll_inst/CLKOUTD.default_gen_clk</td>
<td>Setup</td>
<td>0.000</td>
<td>0</td>
</tr>
<tr>
<td>pll_fft/rpll_inst/CLKOUTD.default_gen_clk</td>
<td>Hold</td>
<td>0.000</td>
<td>0</td>
</tr>
<tr>
<td>pll_fft/rpll_inst/CLKOUTD3.default_gen_clk</td>
<td>Setup</td>
<td>0.000</td>
<td>0</td>
</tr>
<tr>
<td>pll_fft/rpll_inst/CLKOUTD3.default_gen_clk</td>
<td>Hold</td>
<td>0.000</td>
<td>0</td>
</tr>
</table>
<h1><a name="Detail">Timing Details</a></h1>
<h2><a name="All_Path_Slack_Table">Path Slacks Table:</a></h2>
<h3><a name="Setup_Slack_Table">Setup Paths Table</a></h3>
<h4>Report Command:report_timing -setup -from_clock [get_clocks {clk_in}] -to_clock [get_clocks {clk_in}] -max_paths 25 -max_common_paths 1</h4>
<table class="detail_table">
<tr>
<th class="label">Path Number</th>
<th class="label">Path Slack</th>
<th class="label">From Node</th>
<th class="label">To Node</th>
<th class="label">From Clock</th>
<th class="label">To Clock</th>
<th class="label">Relation</th>
<th class="label">Clock Skew</th>
<th class="label">Data Delay</th>
</tr>
<tr>
<td>1</td>
<td>16.403</td>
<td>cntInst2/cnt_r_5_s0/Q</td>
<td>cntInst2/cnt_r_24_s0/RESET</td>
<td>clk_in:[R]</td>
<td>clk_in:[R]</td>
<td>20.000</td>
<td>0.000</td>
<td>3.562</td>
</tr>
<tr>
<td>2</td>
<td>16.403</td>
<td>cntInst2/cnt_r_5_s0/Q</td>
<td>cntInst2/cnt_r_25_s0/RESET</td>
<td>clk_in:[R]</td>
<td>clk_in:[R]</td>
<td>20.000</td>
<td>0.000</td>
<td>3.562</td>
</tr>
<tr>
<td>3</td>
<td>16.403</td>
<td>cntInst2/cnt_r_5_s0/Q</td>
<td>cntInst2/cnt_r_26_s0/RESET</td>
<td>clk_in:[R]</td>
<td>clk_in:[R]</td>
<td>20.000</td>
<td>0.000</td>
<td>3.562</td>
</tr>
<tr>
<td>4</td>
<td>16.403</td>
<td>cntInst2/cnt_r_5_s0/Q</td>
<td>cntInst2/cnt_r_27_s0/RESET</td>
<td>clk_in:[R]</td>
<td>clk_in:[R]</td>
<td>20.000</td>
<td>0.000</td>
<td>3.562</td>
</tr>
<tr>
<td>5</td>
<td>16.403</td>
<td>cntInst2/cnt_r_5_s0/Q</td>
<td>cntInst2/cnt_r_28_s0/RESET</td>
<td>clk_in:[R]</td>
<td>clk_in:[R]</td>
<td>20.000</td>
<td>0.000</td>
<td>3.562</td>
</tr>
<tr>
<td>6</td>
<td>16.403</td>
<td>cntInst2/cnt_r_5_s0/Q</td>
<td>cntInst2/cnt_r_29_s0/RESET</td>
<td>clk_in:[R]</td>
<td>clk_in:[R]</td>
<td>20.000</td>
<td>0.000</td>
<td>3.562</td>
</tr>
<tr>
<td>7</td>
<td>16.411</td>
<td>cntInst2/cnt_r_5_s0/Q</td>
<td>cntInst2/cnt_r_31_s0/RESET</td>
<td>clk_in:[R]</td>
<td>clk_in:[R]</td>
<td>20.000</td>
<td>0.000</td>
<td>3.554</td>
</tr>
<tr>
<td>8</td>
<td>16.411</td>
<td>cntInst2/cnt_r_5_s0/Q</td>
<td>cntInst2/cnt_r_30_s0/RESET</td>
<td>clk_in:[R]</td>
<td>clk_in:[R]</td>
<td>20.000</td>
<td>0.000</td>
<td>3.554</td>
</tr>
<tr>
<td>9</td>
<td>16.594</td>
<td>cntInst2/cnt_r_5_s0/Q</td>
<td>cntInst2/cnt_r_18_s0/RESET</td>
<td>clk_in:[R]</td>
<td>clk_in:[R]</td>
<td>20.000</td>
<td>0.000</td>
<td>3.371</td>
</tr>
<tr>
<td>10</td>
<td>16.594</td>
<td>cntInst2/cnt_r_5_s0/Q</td>
<td>cntInst2/cnt_r_19_s0/RESET</td>
<td>clk_in:[R]</td>
<td>clk_in:[R]</td>
<td>20.000</td>
<td>0.000</td>
<td>3.371</td>
</tr>
<tr>
<td>11</td>
<td>16.594</td>
<td>cntInst2/cnt_r_5_s0/Q</td>
<td>cntInst2/cnt_r_20_s0/RESET</td>
<td>clk_in:[R]</td>
<td>clk_in:[R]</td>
<td>20.000</td>
<td>0.000</td>
<td>3.371</td>
</tr>
<tr>
<td>12</td>
<td>16.594</td>
<td>cntInst2/cnt_r_5_s0/Q</td>
<td>cntInst2/cnt_r_21_s0/RESET</td>
<td>clk_in:[R]</td>
<td>clk_in:[R]</td>
<td>20.000</td>
<td>0.000</td>
<td>3.371</td>
</tr>
<tr>
<td>13</td>
<td>16.594</td>
<td>cntInst2/cnt_r_5_s0/Q</td>
<td>cntInst2/cnt_r_22_s0/RESET</td>
<td>clk_in:[R]</td>
<td>clk_in:[R]</td>
<td>20.000</td>
<td>0.000</td>
<td>3.371</td>
</tr>
<tr>
<td>14</td>
<td>16.594</td>
<td>cntInst2/cnt_r_5_s0/Q</td>
<td>cntInst2/cnt_r_23_s0/RESET</td>
<td>clk_in:[R]</td>
<td>clk_in:[R]</td>
<td>20.000</td>
<td>0.000</td>
<td>3.371</td>
</tr>
<tr>
<td>15</td>
<td>16.598</td>
<td>cntInst2/cnt_r_5_s0/Q</td>
<td>cntInst2/cnt_r_12_s0/RESET</td>
<td>clk_in:[R]</td>
<td>clk_in:[R]</td>
<td>20.000</td>
<td>0.000</td>
<td>3.367</td>
</tr>
<tr>
<td>16</td>
<td>16.598</td>
<td>cntInst2/cnt_r_5_s0/Q</td>
<td>cntInst2/cnt_r_13_s0/RESET</td>
<td>clk_in:[R]</td>
<td>clk_in:[R]</td>
<td>20.000</td>
<td>0.000</td>
<td>3.367</td>
</tr>
<tr>
<td>17</td>
<td>16.598</td>
<td>cntInst2/cnt_r_5_s0/Q</td>
<td>cntInst2/cnt_r_14_s0/RESET</td>
<td>clk_in:[R]</td>
<td>clk_in:[R]</td>
<td>20.000</td>
<td>0.000</td>
<td>3.367</td>
</tr>
<tr>
<td>18</td>
<td>16.598</td>
<td>cntInst2/cnt_r_5_s0/Q</td>
<td>cntInst2/cnt_r_15_s0/RESET</td>
<td>clk_in:[R]</td>
<td>clk_in:[R]</td>
<td>20.000</td>
<td>0.000</td>
<td>3.367</td>
</tr>
<tr>
<td>19</td>
<td>16.598</td>
<td>cntInst2/cnt_r_5_s0/Q</td>
<td>cntInst2/cnt_r_16_s0/RESET</td>
<td>clk_in:[R]</td>
<td>clk_in:[R]</td>
<td>20.000</td>
<td>0.000</td>
<td>3.367</td>
</tr>
<tr>
<td>20</td>
<td>16.598</td>
<td>cntInst2/cnt_r_5_s0/Q</td>
<td>cntInst2/cnt_r_17_s0/RESET</td>
<td>clk_in:[R]</td>
<td>clk_in:[R]</td>
<td>20.000</td>
<td>0.000</td>
<td>3.367</td>
</tr>
<tr>
<td>21</td>
<td>16.605</td>
<td>cntInst2/cnt_r_5_s0/Q</td>
<td>cntInst2/cnt_r_0_s0/RESET</td>
<td>clk_in:[R]</td>
<td>clk_in:[R]</td>
<td>20.000</td>
<td>0.000</td>
<td>3.360</td>
</tr>
<tr>
<td>22</td>
<td>16.789</td>
<td>cntInst2/cnt_r_5_s0/Q</td>
<td>cntInst2/cnt_r_1_s0/RESET</td>
<td>clk_in:[R]</td>
<td>clk_in:[R]</td>
<td>20.000</td>
<td>0.000</td>
<td>3.176</td>
</tr>
<tr>
<td>23</td>
<td>16.789</td>
<td>cntInst2/cnt_r_5_s0/Q</td>
<td>cntInst2/cnt_r_2_s0/RESET</td>
<td>clk_in:[R]</td>
<td>clk_in:[R]</td>
<td>20.000</td>
<td>0.000</td>
<td>3.176</td>
</tr>
<tr>
<td>24</td>
<td>16.789</td>
<td>cntInst2/cnt_r_5_s0/Q</td>
<td>cntInst2/cnt_r_3_s0/RESET</td>
<td>clk_in:[R]</td>
<td>clk_in:[R]</td>
<td>20.000</td>
<td>0.000</td>
<td>3.176</td>
</tr>
<tr>
<td>25</td>
<td>16.789</td>
<td>cntInst2/cnt_r_5_s0/Q</td>
<td>cntInst2/cnt_r_4_s0/RESET</td>
<td>clk_in:[R]</td>
<td>clk_in:[R]</td>
<td>20.000</td>
<td>0.000</td>
<td>3.176</td>
</tr>
</table>
<h3><a name="Hold_Slack_Table">Hold Paths Table</a></h3>
<h4>Report Command:report_timing -hold -from_clock [get_clocks {clk_in}] -to_clock [get_clocks {clk_in}] -max_paths 25 -max_common_paths 1</h4>
<table class="detail_table">
<tr>
<th class="label">Path Number</th>
<th class="label">Path Slack</th>
<th class="label">From Node</th>
<th class="label">To Node</th>
<th class="label">From Clock</th>
<th class="label">To Clock</th>
<th class="label">Relation</th>
<th class="label">Clock Skew</th>
<th class="label">Data Delay</th>
</tr>
<tr>
<td>1</td>
<td>0.425</td>
<td>cntInst2/cnt_r_2_s0/Q</td>
<td>cntInst2/cnt_r_2_s0/D</td>
<td>clk_in:[R]</td>
<td>clk_in:[R]</td>
<td>0.000</td>
<td>0.000</td>
<td>0.436</td>
</tr>
<tr>
<td>2</td>
<td>0.425</td>
<td>cntInst2/cnt_r_6_s0/Q</td>
<td>cntInst2/cnt_r_6_s0/D</td>
<td>clk_in:[R]</td>
<td>clk_in:[R]</td>
<td>0.000</td>
<td>0.000</td>
<td>0.436</td>
</tr>
<tr>
<td>3</td>
<td>0.425</td>
<td>cntInst2/cnt_r_8_s0/Q</td>
<td>cntInst2/cnt_r_8_s0/D</td>
<td>clk_in:[R]</td>
<td>clk_in:[R]</td>
<td>0.000</td>
<td>0.000</td>
<td>0.436</td>
</tr>
<tr>
<td>4</td>
<td>0.425</td>
<td>cntInst2/cnt_r_12_s0/Q</td>
<td>cntInst2/cnt_r_12_s0/D</td>
<td>clk_in:[R]</td>
<td>clk_in:[R]</td>
<td>0.000</td>
<td>0.000</td>
<td>0.436</td>
</tr>
<tr>
<td>5</td>
<td>0.425</td>
<td>cntInst2/cnt_r_14_s0/Q</td>
<td>cntInst2/cnt_r_14_s0/D</td>
<td>clk_in:[R]</td>
<td>clk_in:[R]</td>
<td>0.000</td>
<td>0.000</td>
<td>0.436</td>
</tr>
<tr>
<td>6</td>
<td>0.425</td>
<td>cntInst2/cnt_r_18_s0/Q</td>
<td>cntInst2/cnt_r_18_s0/D</td>
<td>clk_in:[R]</td>
<td>clk_in:[R]</td>
<td>0.000</td>
<td>0.000</td>
<td>0.436</td>
</tr>
<tr>
<td>7</td>
<td>0.425</td>
<td>cntInst2/cnt_r_20_s0/Q</td>
<td>cntInst2/cnt_r_20_s0/D</td>
<td>clk_in:[R]</td>
<td>clk_in:[R]</td>
<td>0.000</td>
<td>0.000</td>
<td>0.436</td>
</tr>
<tr>
<td>8</td>
<td>0.425</td>
<td>cntInst2/cnt_r_24_s0/Q</td>
<td>cntInst2/cnt_r_24_s0/D</td>
<td>clk_in:[R]</td>
<td>clk_in:[R]</td>
<td>0.000</td>
<td>0.000</td>
<td>0.436</td>
</tr>
<tr>
<td>9</td>
<td>0.425</td>
<td>cntInst2/cnt_r_26_s0/Q</td>
<td>cntInst2/cnt_r_26_s0/D</td>
<td>clk_in:[R]</td>
<td>clk_in:[R]</td>
<td>0.000</td>
<td>0.000</td>
<td>0.436</td>
</tr>
<tr>
<td>10</td>
<td>0.425</td>
<td>cntInst2/cnt_r_30_s0/Q</td>
<td>cntInst2/cnt_r_30_s0/D</td>
<td>clk_in:[R]</td>
<td>clk_in:[R]</td>
<td>0.000</td>
<td>0.000</td>
<td>0.436</td>
</tr>
<tr>
<td>11</td>
<td>0.427</td>
<td>cntInst2/cnt_r_0_s0/Q</td>
<td>cntInst2/cnt_r_0_s0/D</td>
<td>clk_in:[R]</td>
<td>clk_in:[R]</td>
<td>0.000</td>
<td>0.000</td>
<td>0.438</td>
</tr>
<tr>
<td>12</td>
<td>0.542</td>
<td>cntInst2/cnt_r_31_s0/Q</td>
<td>cntInst2/cnt_r_31_s0/D</td>
<td>clk_in:[R]</td>
<td>clk_in:[R]</td>
<td>0.000</td>
<td>0.000</td>
<td>0.553</td>
</tr>
<tr>
<td>13</td>
<td>0.542</td>
<td>cntInst2/cnt_r_4_s0/Q</td>
<td>cntInst2/cnt_r_4_s0/D</td>
<td>clk_in:[R]</td>
<td>clk_in:[R]</td>
<td>0.000</td>
<td>0.000</td>
<td>0.553</td>
</tr>
<tr>
<td>14</td>
<td>0.542</td>
<td>cntInst2/cnt_r_9_s0/Q</td>
<td>cntInst2/cnt_r_9_s0/D</td>
<td>clk_in:[R]</td>
<td>clk_in:[R]</td>
<td>0.000</td>
<td>0.000</td>
<td>0.553</td>
</tr>
<tr>
<td>15</td>
<td>0.542</td>
<td>cntInst2/cnt_r_10_s0/Q</td>
<td>cntInst2/cnt_r_10_s0/D</td>
<td>clk_in:[R]</td>
<td>clk_in:[R]</td>
<td>0.000</td>
<td>0.000</td>
<td>0.553</td>
</tr>
<tr>
<td>16</td>
<td>0.542</td>
<td>cntInst2/cnt_r_13_s0/Q</td>
<td>cntInst2/cnt_r_13_s0/D</td>
<td>clk_in:[R]</td>
<td>clk_in:[R]</td>
<td>0.000</td>
<td>0.000</td>
<td>0.553</td>
</tr>
<tr>
<td>17</td>
<td>0.542</td>
<td>cntInst2/cnt_r_16_s0/Q</td>
<td>cntInst2/cnt_r_16_s0/D</td>
<td>clk_in:[R]</td>
<td>clk_in:[R]</td>
<td>0.000</td>
<td>0.000</td>
<td>0.553</td>
</tr>
<tr>
<td>18</td>
<td>0.542</td>
<td>cntInst2/cnt_r_17_s0/Q</td>
<td>cntInst2/cnt_r_17_s0/D</td>
<td>clk_in:[R]</td>
<td>clk_in:[R]</td>
<td>0.000</td>
<td>0.000</td>
<td>0.553</td>
</tr>
<tr>
<td>19</td>
<td>0.542</td>
<td>cntInst2/cnt_r_29_s0/Q</td>
<td>cntInst2/cnt_r_29_s0/D</td>
<td>clk_in:[R]</td>
<td>clk_in:[R]</td>
<td>0.000</td>
<td>0.000</td>
<td>0.553</td>
</tr>
<tr>
<td>20</td>
<td>0.544</td>
<td>cntInst2/cnt_r_7_s0/Q</td>
<td>cntInst2/cnt_r_7_s0/D</td>
<td>clk_in:[R]</td>
<td>clk_in:[R]</td>
<td>0.000</td>
<td>0.000</td>
<td>0.555</td>
</tr>
<tr>
<td>21</td>
<td>0.546</td>
<td>cntInst2/cnt_r_3_s0/Q</td>
<td>cntInst2/cnt_r_3_s0/D</td>
<td>clk_in:[R]</td>
<td>clk_in:[R]</td>
<td>0.000</td>
<td>0.000</td>
<td>0.557</td>
</tr>
<tr>
<td>22</td>
<td>0.546</td>
<td>cntInst2/cnt_r_11_s0/Q</td>
<td>cntInst2/cnt_r_11_s0/D</td>
<td>clk_in:[R]</td>
<td>clk_in:[R]</td>
<td>0.000</td>
<td>0.000</td>
<td>0.557</td>
</tr>
<tr>
<td>23</td>
<td>0.546</td>
<td>cntInst2/cnt_r_19_s0/Q</td>
<td>cntInst2/cnt_r_19_s0/D</td>
<td>clk_in:[R]</td>
<td>clk_in:[R]</td>
<td>0.000</td>
<td>0.000</td>
<td>0.557</td>
</tr>
<tr>
<td>24</td>
<td>0.546</td>
<td>cntInst2/cnt_r_22_s0/Q</td>
<td>cntInst2/cnt_r_22_s0/D</td>
<td>clk_in:[R]</td>
<td>clk_in:[R]</td>
<td>0.000</td>
<td>0.000</td>
<td>0.557</td>
</tr>
<tr>
<td>25</td>
<td>0.546</td>
<td>cntInst2/cnt_r_23_s0/Q</td>
<td>cntInst2/cnt_r_23_s0/D</td>
<td>clk_in:[R]</td>
<td>clk_in:[R]</td>
<td>0.000</td>
<td>0.000</td>
<td>0.557</td>
</tr>
</table>
<h3><a name="Recovery_Slack_Table">Recovery Paths Table</a></h3>
<h4>Report Command:report_timing -recovery -from_clock [get_clocks {clk_in}] -to_clock [get_clocks {clk_in}] -max_paths 25 -max_common_paths 1</h4>
<h4>Nothing to report!</h4>
<h3><a name="Removal_Slack_Table">Removal Paths Table</a></h3>
<h4>Report Command:report_timing -removal -from_clock [get_clocks {clk_in}] -to_clock [get_clocks {clk_in}] -max_paths 25 -max_common_paths 1</h4>
<h4>Nothing to report!</h4>
<h2><a name="MIN_PULSE_WIDTH_TABLE">Minimum Pulse Width Table:</a></h2>
<table class="detail_table">
<tr>
<th class="label">Number</th>
<th class="label">Slack</th>
<th class="label">Actual Width</th>
<th class="label">Required Width</th>
<th class="label">Type</th>
<th class="label">Clock</th>
<th class="label">Objects</th>
</tr>
<h4>Report Command:report_min_pulse_width -nworst 10 -detail</h4>
<tr>
<td>1</td>
<td>2.966</td>
<td>3.966</td>
<td>1.000</td>
<td>Low Pulse Width</td>
<td>myclk</td>
<td>outCrt/cnt_r_1_s0</td>
</tr>
<tr>
<td>2</td>
<td>2.966</td>
<td>3.966</td>
<td>1.000</td>
<td>Low Pulse Width</td>
<td>myclk</td>
<td>outCrt/cnt_r_0_s0</td>
</tr>
<tr>
<td>3</td>
<td>2.966</td>
<td>3.966</td>
<td>1.000</td>
<td>Low Pulse Width</td>
<td>myclk</td>
<td>outCrt/cnt_r_2_s0</td>
</tr>
<tr>
<td>4</td>
<td>3.122</td>
<td>4.122</td>
<td>1.000</td>
<td>High Pulse Width</td>
<td>myclk</td>
<td>outCrt/cnt_r_1_s0</td>
</tr>
<tr>
<td>5</td>
<td>3.122</td>
<td>4.122</td>
<td>1.000</td>
<td>High Pulse Width</td>
<td>myclk</td>
<td>outCrt/cnt_r_0_s0</td>
</tr>
<tr>
<td>6</td>
<td>3.122</td>
<td>4.122</td>
<td>1.000</td>
<td>High Pulse Width</td>
<td>myclk</td>
<td>outCrt/cnt_r_2_s0</td>
</tr>
<tr>
<td>7</td>
<td>6.152</td>
<td>7.152</td>
<td>1.000</td>
<td>High Pulse Width</td>
<td>clk_in</td>
<td>cntInst2/cnt_r_29_s0</td>
</tr>
<tr>
<td>8</td>
<td>6.152</td>
<td>7.152</td>
<td>1.000</td>
<td>High Pulse Width</td>
<td>clk_in</td>
<td>cntInst2/cnt_r_27_s0</td>
</tr>
<tr>
<td>9</td>
<td>6.152</td>
<td>7.152</td>
<td>1.000</td>
<td>High Pulse Width</td>
<td>clk_in</td>
<td>cntInst2/cnt_r_23_s0</td>
</tr>
<tr>
<td>10</td>
<td>6.152</td>
<td>7.152</td>
<td>1.000</td>
<td>High Pulse Width</td>
<td>clk_in</td>
<td>cntInst2/cnt_r_15_s0</td>
</tr>
</table>
<h2><a name="Timing_Report_by_Analysis_Type">Timing Report By Analysis Type:</a></h2>
<h3><a name="Setup_Analysis">Setup Analysis Report</a></h3>
<h4>Report Command:report_timing -setup -from_clock [get_clocks {clk_in}] -to_clock [get_clocks {clk_in}] -max_paths 25 -max_common_paths 1</h4>
<h3>Path1</h3>
<p><b>Path Summary:</b></p>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>16.403</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>10.063</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>26.466</td>
</tr>
<tr>
<td class="label">From</td>
<td>cntInst2/cnt_r_5_s0</td>
</tr>
<tr>
<td class="label">To</td>
<td>cntInst2/cnt_r_24_s0</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>clk_in:[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>clk_in:[R]</td>
</tr>
</table>
<p><b>Data Arrival Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>4.230</td>
<td>4.230</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>6.501</td>
<td>2.271</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C27[2][B]</td>
<td>cntInst2/cnt_r_5_s0/CLK</td>
</tr>
<tr>
<td>6.732</td>
<td>0.231</td>
<td>tC2Q</td>
<td>RR</td>
<td>2</td>
<td>R22C27[2][B]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_5_s0/Q</td>
</tr>
<tr>
<td>6.906</td>
<td>0.174</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C27[3][A]</td>
<td>cntInst2/n5_s5/I2</td>
</tr>
<tr>
<td>7.461</td>
<td>0.555</td>
<td>tINS</td>
<td>RF</td>
<td>1</td>
<td>R22C27[3][A]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s5/F</td>
</tr>
<tr>
<td>7.874</td>
<td>0.413</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C29[3][B]</td>
<td>cntInst2/n5_s1/I2</td>
</tr>
<tr>
<td>8.429</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>2</td>
<td>R22C29[3][B]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s1/F</td>
</tr>
<tr>
<td>8.848</td>
<td>0.419</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C26[1][A]</td>
<td>cntInst2/n5_s11/I0</td>
</tr>
<tr>
<td>9.310</td>
<td>0.462</td>
<td>tINS</td>
<td>FR</td>
<td>32</td>
<td>R22C26[1][A]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s11/F</td>
</tr>
<tr>
<td>10.063</td>
<td>0.753</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C31[0][A]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_24_s0/RESET</td>
</tr>
</table>
<p><b>Data Required Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>20.000</td>
<td>20.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>20.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>20.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>24.230</td>
<td>4.230</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>26.501</td>
<td>2.271</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C31[0][A]</td>
<td>cntInst2/cnt_r_24_s0/CLK</td>
</tr>
<tr>
<td>26.466</td>
<td>-0.035</td>
<td>tSu</td>
<td></td>
<td>1</td>
<td>R22C31[0][A]</td>
<td>cntInst2/cnt_r_24_s0</td>
</tr>
</table>
<p><b>Path Statistics:</b></p>
<table class="summary_table">
<tr>
<td class="label">Clock Skew</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Setup Relationship </td>
<td>20.000</td>
</tr>
<tr>
<td class="label">Logic Level</td>
<td>4</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay</td>
<td>cell: 4.230, 65.063%; route: 2.271, 34.937%</td>
</tr>
<tr>
<td class="label">Arrival Data Path Delay</td>
<td>cell: 1.572, 44.138%; route: 1.759, 49.376%; tC2Q: 0.231, 6.486%</td>
</tr>
<tr>
<td class="label">Required Clock Path Delay</td>
<td>cell: 4.230, 65.063%; route: 2.271, 34.937%</td>
</tr>
</table>
<h3>Path2</h3>
<p><b>Path Summary:</b></p>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>16.403</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>10.063</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>26.466</td>
</tr>
<tr>
<td class="label">From</td>
<td>cntInst2/cnt_r_5_s0</td>
</tr>
<tr>
<td class="label">To</td>
<td>cntInst2/cnt_r_25_s0</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>clk_in:[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>clk_in:[R]</td>
</tr>
</table>
<p><b>Data Arrival Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>4.230</td>
<td>4.230</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>6.501</td>
<td>2.271</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C27[2][B]</td>
<td>cntInst2/cnt_r_5_s0/CLK</td>
</tr>
<tr>
<td>6.732</td>
<td>0.231</td>
<td>tC2Q</td>
<td>RR</td>
<td>2</td>
<td>R22C27[2][B]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_5_s0/Q</td>
</tr>
<tr>
<td>6.906</td>
<td>0.174</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C27[3][A]</td>
<td>cntInst2/n5_s5/I2</td>
</tr>
<tr>
<td>7.461</td>
<td>0.555</td>
<td>tINS</td>
<td>RF</td>
<td>1</td>
<td>R22C27[3][A]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s5/F</td>
</tr>
<tr>
<td>7.874</td>
<td>0.413</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C29[3][B]</td>
<td>cntInst2/n5_s1/I2</td>
</tr>
<tr>
<td>8.429</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>2</td>
<td>R22C29[3][B]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s1/F</td>
</tr>
<tr>
<td>8.848</td>
<td>0.419</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C26[1][A]</td>
<td>cntInst2/n5_s11/I0</td>
</tr>
<tr>
<td>9.310</td>
<td>0.462</td>
<td>tINS</td>
<td>FR</td>
<td>32</td>
<td>R22C26[1][A]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s11/F</td>
</tr>
<tr>
<td>10.063</td>
<td>0.753</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C31[0][B]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_25_s0/RESET</td>
</tr>
</table>
<p><b>Data Required Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>20.000</td>
<td>20.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>20.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>20.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>24.230</td>
<td>4.230</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>26.501</td>
<td>2.271</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C31[0][B]</td>
<td>cntInst2/cnt_r_25_s0/CLK</td>
</tr>
<tr>
<td>26.466</td>
<td>-0.035</td>
<td>tSu</td>
<td></td>
<td>1</td>
<td>R22C31[0][B]</td>
<td>cntInst2/cnt_r_25_s0</td>
</tr>
</table>
<p><b>Path Statistics:</b></p>
<table class="summary_table">
<tr>
<td class="label">Clock Skew</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Setup Relationship </td>
<td>20.000</td>
</tr>
<tr>
<td class="label">Logic Level</td>
<td>4</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay</td>
<td>cell: 4.230, 65.063%; route: 2.271, 34.937%</td>
</tr>
<tr>
<td class="label">Arrival Data Path Delay</td>
<td>cell: 1.572, 44.138%; route: 1.759, 49.376%; tC2Q: 0.231, 6.486%</td>
</tr>
<tr>
<td class="label">Required Clock Path Delay</td>
<td>cell: 4.230, 65.063%; route: 2.271, 34.937%</td>
</tr>
</table>
<h3>Path3</h3>
<p><b>Path Summary:</b></p>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>16.403</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>10.063</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>26.466</td>
</tr>
<tr>
<td class="label">From</td>
<td>cntInst2/cnt_r_5_s0</td>
</tr>
<tr>
<td class="label">To</td>
<td>cntInst2/cnt_r_26_s0</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>clk_in:[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>clk_in:[R]</td>
</tr>
</table>
<p><b>Data Arrival Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>4.230</td>
<td>4.230</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>6.501</td>
<td>2.271</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C27[2][B]</td>
<td>cntInst2/cnt_r_5_s0/CLK</td>
</tr>
<tr>
<td>6.732</td>
<td>0.231</td>
<td>tC2Q</td>
<td>RR</td>
<td>2</td>
<td>R22C27[2][B]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_5_s0/Q</td>
</tr>
<tr>
<td>6.906</td>
<td>0.174</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C27[3][A]</td>
<td>cntInst2/n5_s5/I2</td>
</tr>
<tr>
<td>7.461</td>
<td>0.555</td>
<td>tINS</td>
<td>RF</td>
<td>1</td>
<td>R22C27[3][A]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s5/F</td>
</tr>
<tr>
<td>7.874</td>
<td>0.413</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C29[3][B]</td>
<td>cntInst2/n5_s1/I2</td>
</tr>
<tr>
<td>8.429</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>2</td>
<td>R22C29[3][B]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s1/F</td>
</tr>
<tr>
<td>8.848</td>
<td>0.419</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C26[1][A]</td>
<td>cntInst2/n5_s11/I0</td>
</tr>
<tr>
<td>9.310</td>
<td>0.462</td>
<td>tINS</td>
<td>FR</td>
<td>32</td>
<td>R22C26[1][A]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s11/F</td>
</tr>
<tr>
<td>10.063</td>
<td>0.753</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C31[1][A]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_26_s0/RESET</td>
</tr>
</table>
<p><b>Data Required Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>20.000</td>
<td>20.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>20.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>20.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>24.230</td>
<td>4.230</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>26.501</td>
<td>2.271</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C31[1][A]</td>
<td>cntInst2/cnt_r_26_s0/CLK</td>
</tr>
<tr>
<td>26.466</td>
<td>-0.035</td>
<td>tSu</td>
<td></td>
<td>1</td>
<td>R22C31[1][A]</td>
<td>cntInst2/cnt_r_26_s0</td>
</tr>
</table>
<p><b>Path Statistics:</b></p>
<table class="summary_table">
<tr>
<td class="label">Clock Skew</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Setup Relationship </td>
<td>20.000</td>
</tr>
<tr>
<td class="label">Logic Level</td>
<td>4</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay</td>
<td>cell: 4.230, 65.063%; route: 2.271, 34.937%</td>
</tr>
<tr>
<td class="label">Arrival Data Path Delay</td>
<td>cell: 1.572, 44.138%; route: 1.759, 49.376%; tC2Q: 0.231, 6.486%</td>
</tr>
<tr>
<td class="label">Required Clock Path Delay</td>
<td>cell: 4.230, 65.063%; route: 2.271, 34.937%</td>
</tr>
</table>
<h3>Path4</h3>
<p><b>Path Summary:</b></p>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>16.403</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>10.063</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>26.466</td>
</tr>
<tr>
<td class="label">From</td>
<td>cntInst2/cnt_r_5_s0</td>
</tr>
<tr>
<td class="label">To</td>
<td>cntInst2/cnt_r_27_s0</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>clk_in:[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>clk_in:[R]</td>
</tr>
</table>
<p><b>Data Arrival Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>4.230</td>
<td>4.230</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>6.501</td>
<td>2.271</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C27[2][B]</td>
<td>cntInst2/cnt_r_5_s0/CLK</td>
</tr>
<tr>
<td>6.732</td>
<td>0.231</td>
<td>tC2Q</td>
<td>RR</td>
<td>2</td>
<td>R22C27[2][B]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_5_s0/Q</td>
</tr>
<tr>
<td>6.906</td>
<td>0.174</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C27[3][A]</td>
<td>cntInst2/n5_s5/I2</td>
</tr>
<tr>
<td>7.461</td>
<td>0.555</td>
<td>tINS</td>
<td>RF</td>
<td>1</td>
<td>R22C27[3][A]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s5/F</td>
</tr>
<tr>
<td>7.874</td>
<td>0.413</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C29[3][B]</td>
<td>cntInst2/n5_s1/I2</td>
</tr>
<tr>
<td>8.429</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>2</td>
<td>R22C29[3][B]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s1/F</td>
</tr>
<tr>
<td>8.848</td>
<td>0.419</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C26[1][A]</td>
<td>cntInst2/n5_s11/I0</td>
</tr>
<tr>
<td>9.310</td>
<td>0.462</td>
<td>tINS</td>
<td>FR</td>
<td>32</td>
<td>R22C26[1][A]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s11/F</td>
</tr>
<tr>
<td>10.063</td>
<td>0.753</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C31[1][B]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_27_s0/RESET</td>
</tr>
</table>
<p><b>Data Required Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>20.000</td>
<td>20.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>20.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>20.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>24.230</td>
<td>4.230</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>26.501</td>
<td>2.271</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C31[1][B]</td>
<td>cntInst2/cnt_r_27_s0/CLK</td>
</tr>
<tr>
<td>26.466</td>
<td>-0.035</td>
<td>tSu</td>
<td></td>
<td>1</td>
<td>R22C31[1][B]</td>
<td>cntInst2/cnt_r_27_s0</td>
</tr>
</table>
<p><b>Path Statistics:</b></p>
<table class="summary_table">
<tr>
<td class="label">Clock Skew</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Setup Relationship </td>
<td>20.000</td>
</tr>
<tr>
<td class="label">Logic Level</td>
<td>4</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay</td>
<td>cell: 4.230, 65.063%; route: 2.271, 34.937%</td>
</tr>
<tr>
<td class="label">Arrival Data Path Delay</td>
<td>cell: 1.572, 44.138%; route: 1.759, 49.376%; tC2Q: 0.231, 6.486%</td>
</tr>
<tr>
<td class="label">Required Clock Path Delay</td>
<td>cell: 4.230, 65.063%; route: 2.271, 34.937%</td>
</tr>
</table>
<h3>Path5</h3>
<p><b>Path Summary:</b></p>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>16.403</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>10.063</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>26.466</td>
</tr>
<tr>
<td class="label">From</td>
<td>cntInst2/cnt_r_5_s0</td>
</tr>
<tr>
<td class="label">To</td>
<td>cntInst2/cnt_r_28_s0</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>clk_in:[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>clk_in:[R]</td>
</tr>
</table>
<p><b>Data Arrival Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>4.230</td>
<td>4.230</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>6.501</td>
<td>2.271</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C27[2][B]</td>
<td>cntInst2/cnt_r_5_s0/CLK</td>
</tr>
<tr>
<td>6.732</td>
<td>0.231</td>
<td>tC2Q</td>
<td>RR</td>
<td>2</td>
<td>R22C27[2][B]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_5_s0/Q</td>
</tr>
<tr>
<td>6.906</td>
<td>0.174</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C27[3][A]</td>
<td>cntInst2/n5_s5/I2</td>
</tr>
<tr>
<td>7.461</td>
<td>0.555</td>
<td>tINS</td>
<td>RF</td>
<td>1</td>
<td>R22C27[3][A]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s5/F</td>
</tr>
<tr>
<td>7.874</td>
<td>0.413</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C29[3][B]</td>
<td>cntInst2/n5_s1/I2</td>
</tr>
<tr>
<td>8.429</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>2</td>
<td>R22C29[3][B]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s1/F</td>
</tr>
<tr>
<td>8.848</td>
<td>0.419</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C26[1][A]</td>
<td>cntInst2/n5_s11/I0</td>
</tr>
<tr>
<td>9.310</td>
<td>0.462</td>
<td>tINS</td>
<td>FR</td>
<td>32</td>
<td>R22C26[1][A]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s11/F</td>
</tr>
<tr>
<td>10.063</td>
<td>0.753</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C31[2][A]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_28_s0/RESET</td>
</tr>
</table>
<p><b>Data Required Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>20.000</td>
<td>20.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>20.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>20.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>24.230</td>
<td>4.230</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>26.501</td>
<td>2.271</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C31[2][A]</td>
<td>cntInst2/cnt_r_28_s0/CLK</td>
</tr>
<tr>
<td>26.466</td>
<td>-0.035</td>
<td>tSu</td>
<td></td>
<td>1</td>
<td>R22C31[2][A]</td>
<td>cntInst2/cnt_r_28_s0</td>
</tr>
</table>
<p><b>Path Statistics:</b></p>
<table class="summary_table">
<tr>
<td class="label">Clock Skew</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Setup Relationship </td>
<td>20.000</td>
</tr>
<tr>
<td class="label">Logic Level</td>
<td>4</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay</td>
<td>cell: 4.230, 65.063%; route: 2.271, 34.937%</td>
</tr>
<tr>
<td class="label">Arrival Data Path Delay</td>
<td>cell: 1.572, 44.138%; route: 1.759, 49.376%; tC2Q: 0.231, 6.486%</td>
</tr>
<tr>
<td class="label">Required Clock Path Delay</td>
<td>cell: 4.230, 65.063%; route: 2.271, 34.937%</td>
</tr>
</table>
<h3>Path6</h3>
<p><b>Path Summary:</b></p>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>16.403</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>10.063</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>26.466</td>
</tr>
<tr>
<td class="label">From</td>
<td>cntInst2/cnt_r_5_s0</td>
</tr>
<tr>
<td class="label">To</td>
<td>cntInst2/cnt_r_29_s0</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>clk_in:[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>clk_in:[R]</td>
</tr>
</table>
<p><b>Data Arrival Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>4.230</td>
<td>4.230</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>6.501</td>
<td>2.271</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C27[2][B]</td>
<td>cntInst2/cnt_r_5_s0/CLK</td>
</tr>
<tr>
<td>6.732</td>
<td>0.231</td>
<td>tC2Q</td>
<td>RR</td>
<td>2</td>
<td>R22C27[2][B]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_5_s0/Q</td>
</tr>
<tr>
<td>6.906</td>
<td>0.174</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C27[3][A]</td>
<td>cntInst2/n5_s5/I2</td>
</tr>
<tr>
<td>7.461</td>
<td>0.555</td>
<td>tINS</td>
<td>RF</td>
<td>1</td>
<td>R22C27[3][A]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s5/F</td>
</tr>
<tr>
<td>7.874</td>
<td>0.413</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C29[3][B]</td>
<td>cntInst2/n5_s1/I2</td>
</tr>
<tr>
<td>8.429</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>2</td>
<td>R22C29[3][B]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s1/F</td>
</tr>
<tr>
<td>8.848</td>
<td>0.419</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C26[1][A]</td>
<td>cntInst2/n5_s11/I0</td>
</tr>
<tr>
<td>9.310</td>
<td>0.462</td>
<td>tINS</td>
<td>FR</td>
<td>32</td>
<td>R22C26[1][A]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s11/F</td>
</tr>
<tr>
<td>10.063</td>
<td>0.753</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C31[2][B]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_29_s0/RESET</td>
</tr>
</table>
<p><b>Data Required Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>20.000</td>
<td>20.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>20.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>20.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>24.230</td>
<td>4.230</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>26.501</td>
<td>2.271</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C31[2][B]</td>
<td>cntInst2/cnt_r_29_s0/CLK</td>
</tr>
<tr>
<td>26.466</td>
<td>-0.035</td>
<td>tSu</td>
<td></td>
<td>1</td>
<td>R22C31[2][B]</td>
<td>cntInst2/cnt_r_29_s0</td>
</tr>
</table>
<p><b>Path Statistics:</b></p>
<table class="summary_table">
<tr>
<td class="label">Clock Skew</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Setup Relationship </td>
<td>20.000</td>
</tr>
<tr>
<td class="label">Logic Level</td>
<td>4</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay</td>
<td>cell: 4.230, 65.063%; route: 2.271, 34.937%</td>
</tr>
<tr>
<td class="label">Arrival Data Path Delay</td>
<td>cell: 1.572, 44.138%; route: 1.759, 49.376%; tC2Q: 0.231, 6.486%</td>
</tr>
<tr>
<td class="label">Required Clock Path Delay</td>
<td>cell: 4.230, 65.063%; route: 2.271, 34.937%</td>
</tr>
</table>
<h3>Path7</h3>
<p><b>Path Summary:</b></p>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>16.411</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>10.055</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>26.466</td>
</tr>
<tr>
<td class="label">From</td>
<td>cntInst2/cnt_r_5_s0</td>
</tr>
<tr>
<td class="label">To</td>
<td>cntInst2/cnt_r_31_s0</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>clk_in:[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>clk_in:[R]</td>
</tr>
</table>
<p><b>Data Arrival Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>4.230</td>
<td>4.230</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>6.501</td>
<td>2.271</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C27[2][B]</td>
<td>cntInst2/cnt_r_5_s0/CLK</td>
</tr>
<tr>
<td>6.732</td>
<td>0.231</td>
<td>tC2Q</td>
<td>RR</td>
<td>2</td>
<td>R22C27[2][B]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_5_s0/Q</td>
</tr>
<tr>
<td>6.906</td>
<td>0.174</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C27[3][A]</td>
<td>cntInst2/n5_s5/I2</td>
</tr>
<tr>
<td>7.461</td>
<td>0.555</td>
<td>tINS</td>
<td>RF</td>
<td>1</td>
<td>R22C27[3][A]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s5/F</td>
</tr>
<tr>
<td>7.874</td>
<td>0.413</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C29[3][B]</td>
<td>cntInst2/n5_s1/I2</td>
</tr>
<tr>
<td>8.429</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>2</td>
<td>R22C29[3][B]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s1/F</td>
</tr>
<tr>
<td>8.848</td>
<td>0.419</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C26[1][A]</td>
<td>cntInst2/n5_s11/I0</td>
</tr>
<tr>
<td>9.310</td>
<td>0.462</td>
<td>tINS</td>
<td>FR</td>
<td>32</td>
<td>R22C26[1][A]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s11/F</td>
</tr>
<tr>
<td>10.055</td>
<td>0.746</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C32[0][B]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_31_s0/RESET</td>
</tr>
</table>
<p><b>Data Required Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>20.000</td>
<td>20.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>20.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>20.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>24.230</td>
<td>4.230</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>26.501</td>
<td>2.271</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C32[0][B]</td>
<td>cntInst2/cnt_r_31_s0/CLK</td>
</tr>
<tr>
<td>26.466</td>
<td>-0.035</td>
<td>tSu</td>
<td></td>
<td>1</td>
<td>R22C32[0][B]</td>
<td>cntInst2/cnt_r_31_s0</td>
</tr>
</table>
<p><b>Path Statistics:</b></p>
<table class="summary_table">
<tr>
<td class="label">Clock Skew</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Setup Relationship </td>
<td>20.000</td>
</tr>
<tr>
<td class="label">Logic Level</td>
<td>4</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay</td>
<td>cell: 4.230, 65.063%; route: 2.271, 34.937%</td>
</tr>
<tr>
<td class="label">Arrival Data Path Delay</td>
<td>cell: 1.572, 44.229%; route: 1.751, 49.272%; tC2Q: 0.231, 6.499%</td>
</tr>
<tr>
<td class="label">Required Clock Path Delay</td>
<td>cell: 4.230, 65.063%; route: 2.271, 34.937%</td>
</tr>
</table>
<h3>Path8</h3>
<p><b>Path Summary:</b></p>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>16.411</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>10.055</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>26.466</td>
</tr>
<tr>
<td class="label">From</td>
<td>cntInst2/cnt_r_5_s0</td>
</tr>
<tr>
<td class="label">To</td>
<td>cntInst2/cnt_r_30_s0</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>clk_in:[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>clk_in:[R]</td>
</tr>
</table>
<p><b>Data Arrival Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>4.230</td>
<td>4.230</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>6.501</td>
<td>2.271</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C27[2][B]</td>
<td>cntInst2/cnt_r_5_s0/CLK</td>
</tr>
<tr>
<td>6.732</td>
<td>0.231</td>
<td>tC2Q</td>
<td>RR</td>
<td>2</td>
<td>R22C27[2][B]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_5_s0/Q</td>
</tr>
<tr>
<td>6.906</td>
<td>0.174</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C27[3][A]</td>
<td>cntInst2/n5_s5/I2</td>
</tr>
<tr>
<td>7.461</td>
<td>0.555</td>
<td>tINS</td>
<td>RF</td>
<td>1</td>
<td>R22C27[3][A]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s5/F</td>
</tr>
<tr>
<td>7.874</td>
<td>0.413</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C29[3][B]</td>
<td>cntInst2/n5_s1/I2</td>
</tr>
<tr>
<td>8.429</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>2</td>
<td>R22C29[3][B]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s1/F</td>
</tr>
<tr>
<td>8.848</td>
<td>0.419</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C26[1][A]</td>
<td>cntInst2/n5_s11/I0</td>
</tr>
<tr>
<td>9.310</td>
<td>0.462</td>
<td>tINS</td>
<td>FR</td>
<td>32</td>
<td>R22C26[1][A]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s11/F</td>
</tr>
<tr>
<td>10.055</td>
<td>0.746</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C32[0][A]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_30_s0/RESET</td>
</tr>
</table>
<p><b>Data Required Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>20.000</td>
<td>20.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>20.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>20.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>24.230</td>
<td>4.230</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>26.501</td>
<td>2.271</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C32[0][A]</td>
<td>cntInst2/cnt_r_30_s0/CLK</td>
</tr>
<tr>
<td>26.466</td>
<td>-0.035</td>
<td>tSu</td>
<td></td>
<td>1</td>
<td>R22C32[0][A]</td>
<td>cntInst2/cnt_r_30_s0</td>
</tr>
</table>
<p><b>Path Statistics:</b></p>
<table class="summary_table">
<tr>
<td class="label">Clock Skew</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Setup Relationship </td>
<td>20.000</td>
</tr>
<tr>
<td class="label">Logic Level</td>
<td>4</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay</td>
<td>cell: 4.230, 65.063%; route: 2.271, 34.937%</td>
</tr>
<tr>
<td class="label">Arrival Data Path Delay</td>
<td>cell: 1.572, 44.229%; route: 1.751, 49.272%; tC2Q: 0.231, 6.499%</td>
</tr>
<tr>
<td class="label">Required Clock Path Delay</td>
<td>cell: 4.230, 65.063%; route: 2.271, 34.937%</td>
</tr>
</table>
<h3>Path9</h3>
<p><b>Path Summary:</b></p>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>16.594</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>9.872</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>26.466</td>
</tr>
<tr>
<td class="label">From</td>
<td>cntInst2/cnt_r_5_s0</td>
</tr>
<tr>
<td class="label">To</td>
<td>cntInst2/cnt_r_18_s0</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>clk_in:[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>clk_in:[R]</td>
</tr>
</table>
<p><b>Data Arrival Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>4.230</td>
<td>4.230</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>6.501</td>
<td>2.271</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C27[2][B]</td>
<td>cntInst2/cnt_r_5_s0/CLK</td>
</tr>
<tr>
<td>6.732</td>
<td>0.231</td>
<td>tC2Q</td>
<td>RR</td>
<td>2</td>
<td>R22C27[2][B]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_5_s0/Q</td>
</tr>
<tr>
<td>6.906</td>
<td>0.174</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C27[3][A]</td>
<td>cntInst2/n5_s5/I2</td>
</tr>
<tr>
<td>7.461</td>
<td>0.555</td>
<td>tINS</td>
<td>RF</td>
<td>1</td>
<td>R22C27[3][A]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s5/F</td>
</tr>
<tr>
<td>7.874</td>
<td>0.413</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C29[3][B]</td>
<td>cntInst2/n5_s1/I2</td>
</tr>
<tr>
<td>8.429</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>2</td>
<td>R22C29[3][B]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s1/F</td>
</tr>
<tr>
<td>8.848</td>
<td>0.419</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C26[1][A]</td>
<td>cntInst2/n5_s11/I0</td>
</tr>
<tr>
<td>9.310</td>
<td>0.462</td>
<td>tINS</td>
<td>FR</td>
<td>32</td>
<td>R22C26[1][A]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s11/F</td>
</tr>
<tr>
<td>9.872</td>
<td>0.562</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C30[0][A]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_18_s0/RESET</td>
</tr>
</table>
<p><b>Data Required Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>20.000</td>
<td>20.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>20.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>20.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>24.230</td>
<td>4.230</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>26.501</td>
<td>2.271</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C30[0][A]</td>
<td>cntInst2/cnt_r_18_s0/CLK</td>
</tr>
<tr>
<td>26.466</td>
<td>-0.035</td>
<td>tSu</td>
<td></td>
<td>1</td>
<td>R22C30[0][A]</td>
<td>cntInst2/cnt_r_18_s0</td>
</tr>
</table>
<p><b>Path Statistics:</b></p>
<table class="summary_table">
<tr>
<td class="label">Clock Skew</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Setup Relationship </td>
<td>20.000</td>
</tr>
<tr>
<td class="label">Logic Level</td>
<td>4</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay</td>
<td>cell: 4.230, 65.063%; route: 2.271, 34.937%</td>
</tr>
<tr>
<td class="label">Arrival Data Path Delay</td>
<td>cell: 1.572, 46.639%; route: 1.568, 46.507%; tC2Q: 0.231, 6.853%</td>
</tr>
<tr>
<td class="label">Required Clock Path Delay</td>
<td>cell: 4.230, 65.063%; route: 2.271, 34.937%</td>
</tr>
</table>
<h3>Path10</h3>
<p><b>Path Summary:</b></p>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>16.594</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>9.872</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>26.466</td>
</tr>
<tr>
<td class="label">From</td>
<td>cntInst2/cnt_r_5_s0</td>
</tr>
<tr>
<td class="label">To</td>
<td>cntInst2/cnt_r_19_s0</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>clk_in:[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>clk_in:[R]</td>
</tr>
</table>
<p><b>Data Arrival Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>4.230</td>
<td>4.230</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>6.501</td>
<td>2.271</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C27[2][B]</td>
<td>cntInst2/cnt_r_5_s0/CLK</td>
</tr>
<tr>
<td>6.732</td>
<td>0.231</td>
<td>tC2Q</td>
<td>RR</td>
<td>2</td>
<td>R22C27[2][B]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_5_s0/Q</td>
</tr>
<tr>
<td>6.906</td>
<td>0.174</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C27[3][A]</td>
<td>cntInst2/n5_s5/I2</td>
</tr>
<tr>
<td>7.461</td>
<td>0.555</td>
<td>tINS</td>
<td>RF</td>
<td>1</td>
<td>R22C27[3][A]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s5/F</td>
</tr>
<tr>
<td>7.874</td>
<td>0.413</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C29[3][B]</td>
<td>cntInst2/n5_s1/I2</td>
</tr>
<tr>
<td>8.429</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>2</td>
<td>R22C29[3][B]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s1/F</td>
</tr>
<tr>
<td>8.848</td>
<td>0.419</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C26[1][A]</td>
<td>cntInst2/n5_s11/I0</td>
</tr>
<tr>
<td>9.310</td>
<td>0.462</td>
<td>tINS</td>
<td>FR</td>
<td>32</td>
<td>R22C26[1][A]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s11/F</td>
</tr>
<tr>
<td>9.872</td>
<td>0.562</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C30[0][B]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_19_s0/RESET</td>
</tr>
</table>
<p><b>Data Required Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>20.000</td>
<td>20.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>20.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>20.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>24.230</td>
<td>4.230</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>26.501</td>
<td>2.271</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C30[0][B]</td>
<td>cntInst2/cnt_r_19_s0/CLK</td>
</tr>
<tr>
<td>26.466</td>
<td>-0.035</td>
<td>tSu</td>
<td></td>
<td>1</td>
<td>R22C30[0][B]</td>
<td>cntInst2/cnt_r_19_s0</td>
</tr>
</table>
<p><b>Path Statistics:</b></p>
<table class="summary_table">
<tr>
<td class="label">Clock Skew</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Setup Relationship </td>
<td>20.000</td>
</tr>
<tr>
<td class="label">Logic Level</td>
<td>4</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay</td>
<td>cell: 4.230, 65.063%; route: 2.271, 34.937%</td>
</tr>
<tr>
<td class="label">Arrival Data Path Delay</td>
<td>cell: 1.572, 46.639%; route: 1.568, 46.507%; tC2Q: 0.231, 6.853%</td>
</tr>
<tr>
<td class="label">Required Clock Path Delay</td>
<td>cell: 4.230, 65.063%; route: 2.271, 34.937%</td>
</tr>
</table>
<h3>Path11</h3>
<p><b>Path Summary:</b></p>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>16.594</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>9.872</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>26.466</td>
</tr>
<tr>
<td class="label">From</td>
<td>cntInst2/cnt_r_5_s0</td>
</tr>
<tr>
<td class="label">To</td>
<td>cntInst2/cnt_r_20_s0</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>clk_in:[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>clk_in:[R]</td>
</tr>
</table>
<p><b>Data Arrival Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>4.230</td>
<td>4.230</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>6.501</td>
<td>2.271</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C27[2][B]</td>
<td>cntInst2/cnt_r_5_s0/CLK</td>
</tr>
<tr>
<td>6.732</td>
<td>0.231</td>
<td>tC2Q</td>
<td>RR</td>
<td>2</td>
<td>R22C27[2][B]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_5_s0/Q</td>
</tr>
<tr>
<td>6.906</td>
<td>0.174</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C27[3][A]</td>
<td>cntInst2/n5_s5/I2</td>
</tr>
<tr>
<td>7.461</td>
<td>0.555</td>
<td>tINS</td>
<td>RF</td>
<td>1</td>
<td>R22C27[3][A]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s5/F</td>
</tr>
<tr>
<td>7.874</td>
<td>0.413</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C29[3][B]</td>
<td>cntInst2/n5_s1/I2</td>
</tr>
<tr>
<td>8.429</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>2</td>
<td>R22C29[3][B]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s1/F</td>
</tr>
<tr>
<td>8.848</td>
<td>0.419</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C26[1][A]</td>
<td>cntInst2/n5_s11/I0</td>
</tr>
<tr>
<td>9.310</td>
<td>0.462</td>
<td>tINS</td>
<td>FR</td>
<td>32</td>
<td>R22C26[1][A]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s11/F</td>
</tr>
<tr>
<td>9.872</td>
<td>0.562</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C30[1][A]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_20_s0/RESET</td>
</tr>
</table>
<p><b>Data Required Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>20.000</td>
<td>20.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>20.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>20.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>24.230</td>
<td>4.230</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>26.501</td>
<td>2.271</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C30[1][A]</td>
<td>cntInst2/cnt_r_20_s0/CLK</td>
</tr>
<tr>
<td>26.466</td>
<td>-0.035</td>
<td>tSu</td>
<td></td>
<td>1</td>
<td>R22C30[1][A]</td>
<td>cntInst2/cnt_r_20_s0</td>
</tr>
</table>
<p><b>Path Statistics:</b></p>
<table class="summary_table">
<tr>
<td class="label">Clock Skew</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Setup Relationship </td>
<td>20.000</td>
</tr>
<tr>
<td class="label">Logic Level</td>
<td>4</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay</td>
<td>cell: 4.230, 65.063%; route: 2.271, 34.937%</td>
</tr>
<tr>
<td class="label">Arrival Data Path Delay</td>
<td>cell: 1.572, 46.639%; route: 1.568, 46.507%; tC2Q: 0.231, 6.853%</td>
</tr>
<tr>
<td class="label">Required Clock Path Delay</td>
<td>cell: 4.230, 65.063%; route: 2.271, 34.937%</td>
</tr>
</table>
<h3>Path12</h3>
<p><b>Path Summary:</b></p>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>16.594</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>9.872</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>26.466</td>
</tr>
<tr>
<td class="label">From</td>
<td>cntInst2/cnt_r_5_s0</td>
</tr>
<tr>
<td class="label">To</td>
<td>cntInst2/cnt_r_21_s0</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>clk_in:[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>clk_in:[R]</td>
</tr>
</table>
<p><b>Data Arrival Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>4.230</td>
<td>4.230</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>6.501</td>
<td>2.271</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C27[2][B]</td>
<td>cntInst2/cnt_r_5_s0/CLK</td>
</tr>
<tr>
<td>6.732</td>
<td>0.231</td>
<td>tC2Q</td>
<td>RR</td>
<td>2</td>
<td>R22C27[2][B]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_5_s0/Q</td>
</tr>
<tr>
<td>6.906</td>
<td>0.174</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C27[3][A]</td>
<td>cntInst2/n5_s5/I2</td>
</tr>
<tr>
<td>7.461</td>
<td>0.555</td>
<td>tINS</td>
<td>RF</td>
<td>1</td>
<td>R22C27[3][A]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s5/F</td>
</tr>
<tr>
<td>7.874</td>
<td>0.413</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C29[3][B]</td>
<td>cntInst2/n5_s1/I2</td>
</tr>
<tr>
<td>8.429</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>2</td>
<td>R22C29[3][B]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s1/F</td>
</tr>
<tr>
<td>8.848</td>
<td>0.419</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C26[1][A]</td>
<td>cntInst2/n5_s11/I0</td>
</tr>
<tr>
<td>9.310</td>
<td>0.462</td>
<td>tINS</td>
<td>FR</td>
<td>32</td>
<td>R22C26[1][A]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s11/F</td>
</tr>
<tr>
<td>9.872</td>
<td>0.562</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C30[1][B]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_21_s0/RESET</td>
</tr>
</table>
<p><b>Data Required Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>20.000</td>
<td>20.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>20.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>20.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>24.230</td>
<td>4.230</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>26.501</td>
<td>2.271</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C30[1][B]</td>
<td>cntInst2/cnt_r_21_s0/CLK</td>
</tr>
<tr>
<td>26.466</td>
<td>-0.035</td>
<td>tSu</td>
<td></td>
<td>1</td>
<td>R22C30[1][B]</td>
<td>cntInst2/cnt_r_21_s0</td>
</tr>
</table>
<p><b>Path Statistics:</b></p>
<table class="summary_table">
<tr>
<td class="label">Clock Skew</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Setup Relationship </td>
<td>20.000</td>
</tr>
<tr>
<td class="label">Logic Level</td>
<td>4</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay</td>
<td>cell: 4.230, 65.063%; route: 2.271, 34.937%</td>
</tr>
<tr>
<td class="label">Arrival Data Path Delay</td>
<td>cell: 1.572, 46.639%; route: 1.568, 46.507%; tC2Q: 0.231, 6.853%</td>
</tr>
<tr>
<td class="label">Required Clock Path Delay</td>
<td>cell: 4.230, 65.063%; route: 2.271, 34.937%</td>
</tr>
</table>
<h3>Path13</h3>
<p><b>Path Summary:</b></p>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>16.594</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>9.872</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>26.466</td>
</tr>
<tr>
<td class="label">From</td>
<td>cntInst2/cnt_r_5_s0</td>
</tr>
<tr>
<td class="label">To</td>
<td>cntInst2/cnt_r_22_s0</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>clk_in:[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>clk_in:[R]</td>
</tr>
</table>
<p><b>Data Arrival Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>4.230</td>
<td>4.230</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>6.501</td>
<td>2.271</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C27[2][B]</td>
<td>cntInst2/cnt_r_5_s0/CLK</td>
</tr>
<tr>
<td>6.732</td>
<td>0.231</td>
<td>tC2Q</td>
<td>RR</td>
<td>2</td>
<td>R22C27[2][B]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_5_s0/Q</td>
</tr>
<tr>
<td>6.906</td>
<td>0.174</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C27[3][A]</td>
<td>cntInst2/n5_s5/I2</td>
</tr>
<tr>
<td>7.461</td>
<td>0.555</td>
<td>tINS</td>
<td>RF</td>
<td>1</td>
<td>R22C27[3][A]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s5/F</td>
</tr>
<tr>
<td>7.874</td>
<td>0.413</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C29[3][B]</td>
<td>cntInst2/n5_s1/I2</td>
</tr>
<tr>
<td>8.429</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>2</td>
<td>R22C29[3][B]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s1/F</td>
</tr>
<tr>
<td>8.848</td>
<td>0.419</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C26[1][A]</td>
<td>cntInst2/n5_s11/I0</td>
</tr>
<tr>
<td>9.310</td>
<td>0.462</td>
<td>tINS</td>
<td>FR</td>
<td>32</td>
<td>R22C26[1][A]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s11/F</td>
</tr>
<tr>
<td>9.872</td>
<td>0.562</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C30[2][A]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_22_s0/RESET</td>
</tr>
</table>
<p><b>Data Required Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>20.000</td>
<td>20.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>20.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>20.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>24.230</td>
<td>4.230</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>26.501</td>
<td>2.271</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C30[2][A]</td>
<td>cntInst2/cnt_r_22_s0/CLK</td>
</tr>
<tr>
<td>26.466</td>
<td>-0.035</td>
<td>tSu</td>
<td></td>
<td>1</td>
<td>R22C30[2][A]</td>
<td>cntInst2/cnt_r_22_s0</td>
</tr>
</table>
<p><b>Path Statistics:</b></p>
<table class="summary_table">
<tr>
<td class="label">Clock Skew</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Setup Relationship </td>
<td>20.000</td>
</tr>
<tr>
<td class="label">Logic Level</td>
<td>4</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay</td>
<td>cell: 4.230, 65.063%; route: 2.271, 34.937%</td>
</tr>
<tr>
<td class="label">Arrival Data Path Delay</td>
<td>cell: 1.572, 46.639%; route: 1.568, 46.507%; tC2Q: 0.231, 6.853%</td>
</tr>
<tr>
<td class="label">Required Clock Path Delay</td>
<td>cell: 4.230, 65.063%; route: 2.271, 34.937%</td>
</tr>
</table>
<h3>Path14</h3>
<p><b>Path Summary:</b></p>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>16.594</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>9.872</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>26.466</td>
</tr>
<tr>
<td class="label">From</td>
<td>cntInst2/cnt_r_5_s0</td>
</tr>
<tr>
<td class="label">To</td>
<td>cntInst2/cnt_r_23_s0</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>clk_in:[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>clk_in:[R]</td>
</tr>
</table>
<p><b>Data Arrival Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>4.230</td>
<td>4.230</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>6.501</td>
<td>2.271</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C27[2][B]</td>
<td>cntInst2/cnt_r_5_s0/CLK</td>
</tr>
<tr>
<td>6.732</td>
<td>0.231</td>
<td>tC2Q</td>
<td>RR</td>
<td>2</td>
<td>R22C27[2][B]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_5_s0/Q</td>
</tr>
<tr>
<td>6.906</td>
<td>0.174</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C27[3][A]</td>
<td>cntInst2/n5_s5/I2</td>
</tr>
<tr>
<td>7.461</td>
<td>0.555</td>
<td>tINS</td>
<td>RF</td>
<td>1</td>
<td>R22C27[3][A]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s5/F</td>
</tr>
<tr>
<td>7.874</td>
<td>0.413</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C29[3][B]</td>
<td>cntInst2/n5_s1/I2</td>
</tr>
<tr>
<td>8.429</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>2</td>
<td>R22C29[3][B]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s1/F</td>
</tr>
<tr>
<td>8.848</td>
<td>0.419</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C26[1][A]</td>
<td>cntInst2/n5_s11/I0</td>
</tr>
<tr>
<td>9.310</td>
<td>0.462</td>
<td>tINS</td>
<td>FR</td>
<td>32</td>
<td>R22C26[1][A]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s11/F</td>
</tr>
<tr>
<td>9.872</td>
<td>0.562</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C30[2][B]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_23_s0/RESET</td>
</tr>
</table>
<p><b>Data Required Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>20.000</td>
<td>20.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>20.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>20.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>24.230</td>
<td>4.230</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>26.501</td>
<td>2.271</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C30[2][B]</td>
<td>cntInst2/cnt_r_23_s0/CLK</td>
</tr>
<tr>
<td>26.466</td>
<td>-0.035</td>
<td>tSu</td>
<td></td>
<td>1</td>
<td>R22C30[2][B]</td>
<td>cntInst2/cnt_r_23_s0</td>
</tr>
</table>
<p><b>Path Statistics:</b></p>
<table class="summary_table">
<tr>
<td class="label">Clock Skew</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Setup Relationship </td>
<td>20.000</td>
</tr>
<tr>
<td class="label">Logic Level</td>
<td>4</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay</td>
<td>cell: 4.230, 65.063%; route: 2.271, 34.937%</td>
</tr>
<tr>
<td class="label">Arrival Data Path Delay</td>
<td>cell: 1.572, 46.639%; route: 1.568, 46.507%; tC2Q: 0.231, 6.853%</td>
</tr>
<tr>
<td class="label">Required Clock Path Delay</td>
<td>cell: 4.230, 65.063%; route: 2.271, 34.937%</td>
</tr>
</table>
<h3>Path15</h3>
<p><b>Path Summary:</b></p>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>16.598</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>9.868</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>26.466</td>
</tr>
<tr>
<td class="label">From</td>
<td>cntInst2/cnt_r_5_s0</td>
</tr>
<tr>
<td class="label">To</td>
<td>cntInst2/cnt_r_12_s0</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>clk_in:[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>clk_in:[R]</td>
</tr>
</table>
<p><b>Data Arrival Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>4.230</td>
<td>4.230</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>6.501</td>
<td>2.271</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C27[2][B]</td>
<td>cntInst2/cnt_r_5_s0/CLK</td>
</tr>
<tr>
<td>6.732</td>
<td>0.231</td>
<td>tC2Q</td>
<td>RR</td>
<td>2</td>
<td>R22C27[2][B]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_5_s0/Q</td>
</tr>
<tr>
<td>6.906</td>
<td>0.174</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C27[3][A]</td>
<td>cntInst2/n5_s5/I2</td>
</tr>
<tr>
<td>7.461</td>
<td>0.555</td>
<td>tINS</td>
<td>RF</td>
<td>1</td>
<td>R22C27[3][A]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s5/F</td>
</tr>
<tr>
<td>7.874</td>
<td>0.413</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C29[3][B]</td>
<td>cntInst2/n5_s1/I2</td>
</tr>
<tr>
<td>8.429</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>2</td>
<td>R22C29[3][B]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s1/F</td>
</tr>
<tr>
<td>8.848</td>
<td>0.419</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C26[1][A]</td>
<td>cntInst2/n5_s11/I0</td>
</tr>
<tr>
<td>9.310</td>
<td>0.462</td>
<td>tINS</td>
<td>FR</td>
<td>32</td>
<td>R22C26[1][A]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s11/F</td>
</tr>
<tr>
<td>9.868</td>
<td>0.558</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C29[0][A]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_12_s0/RESET</td>
</tr>
</table>
<p><b>Data Required Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>20.000</td>
<td>20.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>20.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>20.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>24.230</td>
<td>4.230</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>26.501</td>
<td>2.271</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C29[0][A]</td>
<td>cntInst2/cnt_r_12_s0/CLK</td>
</tr>
<tr>
<td>26.466</td>
<td>-0.035</td>
<td>tSu</td>
<td></td>
<td>1</td>
<td>R22C29[0][A]</td>
<td>cntInst2/cnt_r_12_s0</td>
</tr>
</table>
<p><b>Path Statistics:</b></p>
<table class="summary_table">
<tr>
<td class="label">Clock Skew</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Setup Relationship </td>
<td>20.000</td>
</tr>
<tr>
<td class="label">Logic Level</td>
<td>4</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay</td>
<td>cell: 4.230, 65.063%; route: 2.271, 34.937%</td>
</tr>
<tr>
<td class="label">Arrival Data Path Delay</td>
<td>cell: 1.572, 46.690%; route: 1.564, 46.449%; tC2Q: 0.231, 6.861%</td>
</tr>
<tr>
<td class="label">Required Clock Path Delay</td>
<td>cell: 4.230, 65.063%; route: 2.271, 34.937%</td>
</tr>
</table>
<h3>Path16</h3>
<p><b>Path Summary:</b></p>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>16.598</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>9.868</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>26.466</td>
</tr>
<tr>
<td class="label">From</td>
<td>cntInst2/cnt_r_5_s0</td>
</tr>
<tr>
<td class="label">To</td>
<td>cntInst2/cnt_r_13_s0</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>clk_in:[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>clk_in:[R]</td>
</tr>
</table>
<p><b>Data Arrival Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>4.230</td>
<td>4.230</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>6.501</td>
<td>2.271</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C27[2][B]</td>
<td>cntInst2/cnt_r_5_s0/CLK</td>
</tr>
<tr>
<td>6.732</td>
<td>0.231</td>
<td>tC2Q</td>
<td>RR</td>
<td>2</td>
<td>R22C27[2][B]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_5_s0/Q</td>
</tr>
<tr>
<td>6.906</td>
<td>0.174</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C27[3][A]</td>
<td>cntInst2/n5_s5/I2</td>
</tr>
<tr>
<td>7.461</td>
<td>0.555</td>
<td>tINS</td>
<td>RF</td>
<td>1</td>
<td>R22C27[3][A]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s5/F</td>
</tr>
<tr>
<td>7.874</td>
<td>0.413</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C29[3][B]</td>
<td>cntInst2/n5_s1/I2</td>
</tr>
<tr>
<td>8.429</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>2</td>
<td>R22C29[3][B]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s1/F</td>
</tr>
<tr>
<td>8.848</td>
<td>0.419</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C26[1][A]</td>
<td>cntInst2/n5_s11/I0</td>
</tr>
<tr>
<td>9.310</td>
<td>0.462</td>
<td>tINS</td>
<td>FR</td>
<td>32</td>
<td>R22C26[1][A]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s11/F</td>
</tr>
<tr>
<td>9.868</td>
<td>0.558</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C29[0][B]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_13_s0/RESET</td>
</tr>
</table>
<p><b>Data Required Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>20.000</td>
<td>20.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>20.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>20.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>24.230</td>
<td>4.230</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>26.501</td>
<td>2.271</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C29[0][B]</td>
<td>cntInst2/cnt_r_13_s0/CLK</td>
</tr>
<tr>
<td>26.466</td>
<td>-0.035</td>
<td>tSu</td>
<td></td>
<td>1</td>
<td>R22C29[0][B]</td>
<td>cntInst2/cnt_r_13_s0</td>
</tr>
</table>
<p><b>Path Statistics:</b></p>
<table class="summary_table">
<tr>
<td class="label">Clock Skew</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Setup Relationship </td>
<td>20.000</td>
</tr>
<tr>
<td class="label">Logic Level</td>
<td>4</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay</td>
<td>cell: 4.230, 65.063%; route: 2.271, 34.937%</td>
</tr>
<tr>
<td class="label">Arrival Data Path Delay</td>
<td>cell: 1.572, 46.690%; route: 1.564, 46.449%; tC2Q: 0.231, 6.861%</td>
</tr>
<tr>
<td class="label">Required Clock Path Delay</td>
<td>cell: 4.230, 65.063%; route: 2.271, 34.937%</td>
</tr>
</table>
<h3>Path17</h3>
<p><b>Path Summary:</b></p>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>16.598</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>9.868</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>26.466</td>
</tr>
<tr>
<td class="label">From</td>
<td>cntInst2/cnt_r_5_s0</td>
</tr>
<tr>
<td class="label">To</td>
<td>cntInst2/cnt_r_14_s0</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>clk_in:[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>clk_in:[R]</td>
</tr>
</table>
<p><b>Data Arrival Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>4.230</td>
<td>4.230</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>6.501</td>
<td>2.271</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C27[2][B]</td>
<td>cntInst2/cnt_r_5_s0/CLK</td>
</tr>
<tr>
<td>6.732</td>
<td>0.231</td>
<td>tC2Q</td>
<td>RR</td>
<td>2</td>
<td>R22C27[2][B]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_5_s0/Q</td>
</tr>
<tr>
<td>6.906</td>
<td>0.174</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C27[3][A]</td>
<td>cntInst2/n5_s5/I2</td>
</tr>
<tr>
<td>7.461</td>
<td>0.555</td>
<td>tINS</td>
<td>RF</td>
<td>1</td>
<td>R22C27[3][A]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s5/F</td>
</tr>
<tr>
<td>7.874</td>
<td>0.413</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C29[3][B]</td>
<td>cntInst2/n5_s1/I2</td>
</tr>
<tr>
<td>8.429</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>2</td>
<td>R22C29[3][B]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s1/F</td>
</tr>
<tr>
<td>8.848</td>
<td>0.419</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C26[1][A]</td>
<td>cntInst2/n5_s11/I0</td>
</tr>
<tr>
<td>9.310</td>
<td>0.462</td>
<td>tINS</td>
<td>FR</td>
<td>32</td>
<td>R22C26[1][A]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s11/F</td>
</tr>
<tr>
<td>9.868</td>
<td>0.558</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C29[1][A]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_14_s0/RESET</td>
</tr>
</table>
<p><b>Data Required Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>20.000</td>
<td>20.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>20.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>20.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>24.230</td>
<td>4.230</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>26.501</td>
<td>2.271</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C29[1][A]</td>
<td>cntInst2/cnt_r_14_s0/CLK</td>
</tr>
<tr>
<td>26.466</td>
<td>-0.035</td>
<td>tSu</td>
<td></td>
<td>1</td>
<td>R22C29[1][A]</td>
<td>cntInst2/cnt_r_14_s0</td>
</tr>
</table>
<p><b>Path Statistics:</b></p>
<table class="summary_table">
<tr>
<td class="label">Clock Skew</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Setup Relationship </td>
<td>20.000</td>
</tr>
<tr>
<td class="label">Logic Level</td>
<td>4</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay</td>
<td>cell: 4.230, 65.063%; route: 2.271, 34.937%</td>
</tr>
<tr>
<td class="label">Arrival Data Path Delay</td>
<td>cell: 1.572, 46.690%; route: 1.564, 46.449%; tC2Q: 0.231, 6.861%</td>
</tr>
<tr>
<td class="label">Required Clock Path Delay</td>
<td>cell: 4.230, 65.063%; route: 2.271, 34.937%</td>
</tr>
</table>
<h3>Path18</h3>
<p><b>Path Summary:</b></p>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>16.598</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>9.868</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>26.466</td>
</tr>
<tr>
<td class="label">From</td>
<td>cntInst2/cnt_r_5_s0</td>
</tr>
<tr>
<td class="label">To</td>
<td>cntInst2/cnt_r_15_s0</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>clk_in:[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>clk_in:[R]</td>
</tr>
</table>
<p><b>Data Arrival Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>4.230</td>
<td>4.230</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>6.501</td>
<td>2.271</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C27[2][B]</td>
<td>cntInst2/cnt_r_5_s0/CLK</td>
</tr>
<tr>
<td>6.732</td>
<td>0.231</td>
<td>tC2Q</td>
<td>RR</td>
<td>2</td>
<td>R22C27[2][B]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_5_s0/Q</td>
</tr>
<tr>
<td>6.906</td>
<td>0.174</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C27[3][A]</td>
<td>cntInst2/n5_s5/I2</td>
</tr>
<tr>
<td>7.461</td>
<td>0.555</td>
<td>tINS</td>
<td>RF</td>
<td>1</td>
<td>R22C27[3][A]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s5/F</td>
</tr>
<tr>
<td>7.874</td>
<td>0.413</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C29[3][B]</td>
<td>cntInst2/n5_s1/I2</td>
</tr>
<tr>
<td>8.429</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>2</td>
<td>R22C29[3][B]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s1/F</td>
</tr>
<tr>
<td>8.848</td>
<td>0.419</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C26[1][A]</td>
<td>cntInst2/n5_s11/I0</td>
</tr>
<tr>
<td>9.310</td>
<td>0.462</td>
<td>tINS</td>
<td>FR</td>
<td>32</td>
<td>R22C26[1][A]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s11/F</td>
</tr>
<tr>
<td>9.868</td>
<td>0.558</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C29[1][B]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_15_s0/RESET</td>
</tr>
</table>
<p><b>Data Required Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>20.000</td>
<td>20.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>20.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>20.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>24.230</td>
<td>4.230</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>26.501</td>
<td>2.271</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C29[1][B]</td>
<td>cntInst2/cnt_r_15_s0/CLK</td>
</tr>
<tr>
<td>26.466</td>
<td>-0.035</td>
<td>tSu</td>
<td></td>
<td>1</td>
<td>R22C29[1][B]</td>
<td>cntInst2/cnt_r_15_s0</td>
</tr>
</table>
<p><b>Path Statistics:</b></p>
<table class="summary_table">
<tr>
<td class="label">Clock Skew</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Setup Relationship </td>
<td>20.000</td>
</tr>
<tr>
<td class="label">Logic Level</td>
<td>4</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay</td>
<td>cell: 4.230, 65.063%; route: 2.271, 34.937%</td>
</tr>
<tr>
<td class="label">Arrival Data Path Delay</td>
<td>cell: 1.572, 46.690%; route: 1.564, 46.449%; tC2Q: 0.231, 6.861%</td>
</tr>
<tr>
<td class="label">Required Clock Path Delay</td>
<td>cell: 4.230, 65.063%; route: 2.271, 34.937%</td>
</tr>
</table>
<h3>Path19</h3>
<p><b>Path Summary:</b></p>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>16.598</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>9.868</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>26.466</td>
</tr>
<tr>
<td class="label">From</td>
<td>cntInst2/cnt_r_5_s0</td>
</tr>
<tr>
<td class="label">To</td>
<td>cntInst2/cnt_r_16_s0</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>clk_in:[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>clk_in:[R]</td>
</tr>
</table>
<p><b>Data Arrival Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>4.230</td>
<td>4.230</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>6.501</td>
<td>2.271</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C27[2][B]</td>
<td>cntInst2/cnt_r_5_s0/CLK</td>
</tr>
<tr>
<td>6.732</td>
<td>0.231</td>
<td>tC2Q</td>
<td>RR</td>
<td>2</td>
<td>R22C27[2][B]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_5_s0/Q</td>
</tr>
<tr>
<td>6.906</td>
<td>0.174</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C27[3][A]</td>
<td>cntInst2/n5_s5/I2</td>
</tr>
<tr>
<td>7.461</td>
<td>0.555</td>
<td>tINS</td>
<td>RF</td>
<td>1</td>
<td>R22C27[3][A]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s5/F</td>
</tr>
<tr>
<td>7.874</td>
<td>0.413</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C29[3][B]</td>
<td>cntInst2/n5_s1/I2</td>
</tr>
<tr>
<td>8.429</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>2</td>
<td>R22C29[3][B]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s1/F</td>
</tr>
<tr>
<td>8.848</td>
<td>0.419</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C26[1][A]</td>
<td>cntInst2/n5_s11/I0</td>
</tr>
<tr>
<td>9.310</td>
<td>0.462</td>
<td>tINS</td>
<td>FR</td>
<td>32</td>
<td>R22C26[1][A]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s11/F</td>
</tr>
<tr>
<td>9.868</td>
<td>0.558</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C29[2][A]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_16_s0/RESET</td>
</tr>
</table>
<p><b>Data Required Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>20.000</td>
<td>20.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>20.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>20.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>24.230</td>
<td>4.230</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>26.501</td>
<td>2.271</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C29[2][A]</td>
<td>cntInst2/cnt_r_16_s0/CLK</td>
</tr>
<tr>
<td>26.466</td>
<td>-0.035</td>
<td>tSu</td>
<td></td>
<td>1</td>
<td>R22C29[2][A]</td>
<td>cntInst2/cnt_r_16_s0</td>
</tr>
</table>
<p><b>Path Statistics:</b></p>
<table class="summary_table">
<tr>
<td class="label">Clock Skew</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Setup Relationship </td>
<td>20.000</td>
</tr>
<tr>
<td class="label">Logic Level</td>
<td>4</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay</td>
<td>cell: 4.230, 65.063%; route: 2.271, 34.937%</td>
</tr>
<tr>
<td class="label">Arrival Data Path Delay</td>
<td>cell: 1.572, 46.690%; route: 1.564, 46.449%; tC2Q: 0.231, 6.861%</td>
</tr>
<tr>
<td class="label">Required Clock Path Delay</td>
<td>cell: 4.230, 65.063%; route: 2.271, 34.937%</td>
</tr>
</table>
<h3>Path20</h3>
<p><b>Path Summary:</b></p>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>16.598</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>9.868</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>26.466</td>
</tr>
<tr>
<td class="label">From</td>
<td>cntInst2/cnt_r_5_s0</td>
</tr>
<tr>
<td class="label">To</td>
<td>cntInst2/cnt_r_17_s0</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>clk_in:[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>clk_in:[R]</td>
</tr>
</table>
<p><b>Data Arrival Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>4.230</td>
<td>4.230</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>6.501</td>
<td>2.271</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C27[2][B]</td>
<td>cntInst2/cnt_r_5_s0/CLK</td>
</tr>
<tr>
<td>6.732</td>
<td>0.231</td>
<td>tC2Q</td>
<td>RR</td>
<td>2</td>
<td>R22C27[2][B]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_5_s0/Q</td>
</tr>
<tr>
<td>6.906</td>
<td>0.174</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C27[3][A]</td>
<td>cntInst2/n5_s5/I2</td>
</tr>
<tr>
<td>7.461</td>
<td>0.555</td>
<td>tINS</td>
<td>RF</td>
<td>1</td>
<td>R22C27[3][A]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s5/F</td>
</tr>
<tr>
<td>7.874</td>
<td>0.413</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C29[3][B]</td>
<td>cntInst2/n5_s1/I2</td>
</tr>
<tr>
<td>8.429</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>2</td>
<td>R22C29[3][B]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s1/F</td>
</tr>
<tr>
<td>8.848</td>
<td>0.419</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C26[1][A]</td>
<td>cntInst2/n5_s11/I0</td>
</tr>
<tr>
<td>9.310</td>
<td>0.462</td>
<td>tINS</td>
<td>FR</td>
<td>32</td>
<td>R22C26[1][A]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s11/F</td>
</tr>
<tr>
<td>9.868</td>
<td>0.558</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C29[2][B]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_17_s0/RESET</td>
</tr>
</table>
<p><b>Data Required Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>20.000</td>
<td>20.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>20.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>20.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>24.230</td>
<td>4.230</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>26.501</td>
<td>2.271</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C29[2][B]</td>
<td>cntInst2/cnt_r_17_s0/CLK</td>
</tr>
<tr>
<td>26.466</td>
<td>-0.035</td>
<td>tSu</td>
<td></td>
<td>1</td>
<td>R22C29[2][B]</td>
<td>cntInst2/cnt_r_17_s0</td>
</tr>
</table>
<p><b>Path Statistics:</b></p>
<table class="summary_table">
<tr>
<td class="label">Clock Skew</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Setup Relationship </td>
<td>20.000</td>
</tr>
<tr>
<td class="label">Logic Level</td>
<td>4</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay</td>
<td>cell: 4.230, 65.063%; route: 2.271, 34.937%</td>
</tr>
<tr>
<td class="label">Arrival Data Path Delay</td>
<td>cell: 1.572, 46.690%; route: 1.564, 46.449%; tC2Q: 0.231, 6.861%</td>
</tr>
<tr>
<td class="label">Required Clock Path Delay</td>
<td>cell: 4.230, 65.063%; route: 2.271, 34.937%</td>
</tr>
</table>
<h3>Path21</h3>
<p><b>Path Summary:</b></p>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>16.605</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>9.861</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>26.466</td>
</tr>
<tr>
<td class="label">From</td>
<td>cntInst2/cnt_r_5_s0</td>
</tr>
<tr>
<td class="label">To</td>
<td>cntInst2/cnt_r_0_s0</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>clk_in:[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>clk_in:[R]</td>
</tr>
</table>
<p><b>Data Arrival Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>4.230</td>
<td>4.230</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>6.501</td>
<td>2.271</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C27[2][B]</td>
<td>cntInst2/cnt_r_5_s0/CLK</td>
</tr>
<tr>
<td>6.732</td>
<td>0.231</td>
<td>tC2Q</td>
<td>RR</td>
<td>2</td>
<td>R22C27[2][B]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_5_s0/Q</td>
</tr>
<tr>
<td>6.906</td>
<td>0.174</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C27[3][A]</td>
<td>cntInst2/n5_s5/I2</td>
</tr>
<tr>
<td>7.461</td>
<td>0.555</td>
<td>tINS</td>
<td>RF</td>
<td>1</td>
<td>R22C27[3][A]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s5/F</td>
</tr>
<tr>
<td>7.874</td>
<td>0.413</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C29[3][B]</td>
<td>cntInst2/n5_s1/I2</td>
</tr>
<tr>
<td>8.429</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>2</td>
<td>R22C29[3][B]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s1/F</td>
</tr>
<tr>
<td>8.848</td>
<td>0.419</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C26[1][A]</td>
<td>cntInst2/n5_s11/I0</td>
</tr>
<tr>
<td>9.310</td>
<td>0.462</td>
<td>tINS</td>
<td>FR</td>
<td>32</td>
<td>R22C26[1][A]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s11/F</td>
</tr>
<tr>
<td>9.861</td>
<td>0.551</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R23C27[0][A]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_0_s0/RESET</td>
</tr>
</table>
<p><b>Data Required Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>20.000</td>
<td>20.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>20.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>20.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>24.230</td>
<td>4.230</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>26.501</td>
<td>2.271</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R23C27[0][A]</td>
<td>cntInst2/cnt_r_0_s0/CLK</td>
</tr>
<tr>
<td>26.466</td>
<td>-0.035</td>
<td>tSu</td>
<td></td>
<td>1</td>
<td>R23C27[0][A]</td>
<td>cntInst2/cnt_r_0_s0</td>
</tr>
</table>
<p><b>Path Statistics:</b></p>
<table class="summary_table">
<tr>
<td class="label">Clock Skew</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Setup Relationship </td>
<td>20.000</td>
</tr>
<tr>
<td class="label">Logic Level</td>
<td>4</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay</td>
<td>cell: 4.230, 65.063%; route: 2.271, 34.937%</td>
</tr>
<tr>
<td class="label">Arrival Data Path Delay</td>
<td>cell: 1.572, 46.792%; route: 1.557, 46.332%; tC2Q: 0.231, 6.876%</td>
</tr>
<tr>
<td class="label">Required Clock Path Delay</td>
<td>cell: 4.230, 65.063%; route: 2.271, 34.937%</td>
</tr>
</table>
<h3>Path22</h3>
<p><b>Path Summary:</b></p>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>16.789</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>9.677</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>26.466</td>
</tr>
<tr>
<td class="label">From</td>
<td>cntInst2/cnt_r_5_s0</td>
</tr>
<tr>
<td class="label">To</td>
<td>cntInst2/cnt_r_1_s0</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>clk_in:[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>clk_in:[R]</td>
</tr>
</table>
<p><b>Data Arrival Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>4.230</td>
<td>4.230</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>6.501</td>
<td>2.271</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C27[2][B]</td>
<td>cntInst2/cnt_r_5_s0/CLK</td>
</tr>
<tr>
<td>6.732</td>
<td>0.231</td>
<td>tC2Q</td>
<td>RR</td>
<td>2</td>
<td>R22C27[2][B]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_5_s0/Q</td>
</tr>
<tr>
<td>6.906</td>
<td>0.174</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C27[3][A]</td>
<td>cntInst2/n5_s5/I2</td>
</tr>
<tr>
<td>7.461</td>
<td>0.555</td>
<td>tINS</td>
<td>RF</td>
<td>1</td>
<td>R22C27[3][A]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s5/F</td>
</tr>
<tr>
<td>7.874</td>
<td>0.413</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C29[3][B]</td>
<td>cntInst2/n5_s1/I2</td>
</tr>
<tr>
<td>8.429</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>2</td>
<td>R22C29[3][B]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s1/F</td>
</tr>
<tr>
<td>8.848</td>
<td>0.419</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C26[1][A]</td>
<td>cntInst2/n5_s11/I0</td>
</tr>
<tr>
<td>9.310</td>
<td>0.462</td>
<td>tINS</td>
<td>FR</td>
<td>32</td>
<td>R22C26[1][A]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s11/F</td>
</tr>
<tr>
<td>9.677</td>
<td>0.367</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C27[0][B]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_1_s0/RESET</td>
</tr>
</table>
<p><b>Data Required Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>20.000</td>
<td>20.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>20.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>20.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>24.230</td>
<td>4.230</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>26.501</td>
<td>2.271</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C27[0][B]</td>
<td>cntInst2/cnt_r_1_s0/CLK</td>
</tr>
<tr>
<td>26.466</td>
<td>-0.035</td>
<td>tSu</td>
<td></td>
<td>1</td>
<td>R22C27[0][B]</td>
<td>cntInst2/cnt_r_1_s0</td>
</tr>
</table>
<p><b>Path Statistics:</b></p>
<table class="summary_table">
<tr>
<td class="label">Clock Skew</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Setup Relationship </td>
<td>20.000</td>
</tr>
<tr>
<td class="label">Logic Level</td>
<td>4</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay</td>
<td>cell: 4.230, 65.063%; route: 2.271, 34.937%</td>
</tr>
<tr>
<td class="label">Arrival Data Path Delay</td>
<td>cell: 1.572, 49.498%; route: 1.373, 43.229%; tC2Q: 0.231, 7.274%</td>
</tr>
<tr>
<td class="label">Required Clock Path Delay</td>
<td>cell: 4.230, 65.063%; route: 2.271, 34.937%</td>
</tr>
</table>
<h3>Path23</h3>
<p><b>Path Summary:</b></p>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>16.789</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>9.677</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>26.466</td>
</tr>
<tr>
<td class="label">From</td>
<td>cntInst2/cnt_r_5_s0</td>
</tr>
<tr>
<td class="label">To</td>
<td>cntInst2/cnt_r_2_s0</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>clk_in:[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>clk_in:[R]</td>
</tr>
</table>
<p><b>Data Arrival Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>4.230</td>
<td>4.230</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>6.501</td>
<td>2.271</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C27[2][B]</td>
<td>cntInst2/cnt_r_5_s0/CLK</td>
</tr>
<tr>
<td>6.732</td>
<td>0.231</td>
<td>tC2Q</td>
<td>RR</td>
<td>2</td>
<td>R22C27[2][B]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_5_s0/Q</td>
</tr>
<tr>
<td>6.906</td>
<td>0.174</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C27[3][A]</td>
<td>cntInst2/n5_s5/I2</td>
</tr>
<tr>
<td>7.461</td>
<td>0.555</td>
<td>tINS</td>
<td>RF</td>
<td>1</td>
<td>R22C27[3][A]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s5/F</td>
</tr>
<tr>
<td>7.874</td>
<td>0.413</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C29[3][B]</td>
<td>cntInst2/n5_s1/I2</td>
</tr>
<tr>
<td>8.429</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>2</td>
<td>R22C29[3][B]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s1/F</td>
</tr>
<tr>
<td>8.848</td>
<td>0.419</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C26[1][A]</td>
<td>cntInst2/n5_s11/I0</td>
</tr>
<tr>
<td>9.310</td>
<td>0.462</td>
<td>tINS</td>
<td>FR</td>
<td>32</td>
<td>R22C26[1][A]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s11/F</td>
</tr>
<tr>
<td>9.677</td>
<td>0.367</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C27[1][A]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_2_s0/RESET</td>
</tr>
</table>
<p><b>Data Required Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>20.000</td>
<td>20.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>20.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>20.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>24.230</td>
<td>4.230</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>26.501</td>
<td>2.271</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C27[1][A]</td>
<td>cntInst2/cnt_r_2_s0/CLK</td>
</tr>
<tr>
<td>26.466</td>
<td>-0.035</td>
<td>tSu</td>
<td></td>
<td>1</td>
<td>R22C27[1][A]</td>
<td>cntInst2/cnt_r_2_s0</td>
</tr>
</table>
<p><b>Path Statistics:</b></p>
<table class="summary_table">
<tr>
<td class="label">Clock Skew</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Setup Relationship </td>
<td>20.000</td>
</tr>
<tr>
<td class="label">Logic Level</td>
<td>4</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay</td>
<td>cell: 4.230, 65.063%; route: 2.271, 34.937%</td>
</tr>
<tr>
<td class="label">Arrival Data Path Delay</td>
<td>cell: 1.572, 49.498%; route: 1.373, 43.229%; tC2Q: 0.231, 7.274%</td>
</tr>
<tr>
<td class="label">Required Clock Path Delay</td>
<td>cell: 4.230, 65.063%; route: 2.271, 34.937%</td>
</tr>
</table>
<h3>Path24</h3>
<p><b>Path Summary:</b></p>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>16.789</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>9.677</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>26.466</td>
</tr>
<tr>
<td class="label">From</td>
<td>cntInst2/cnt_r_5_s0</td>
</tr>
<tr>
<td class="label">To</td>
<td>cntInst2/cnt_r_3_s0</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>clk_in:[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>clk_in:[R]</td>
</tr>
</table>
<p><b>Data Arrival Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>4.230</td>
<td>4.230</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>6.501</td>
<td>2.271</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C27[2][B]</td>
<td>cntInst2/cnt_r_5_s0/CLK</td>
</tr>
<tr>
<td>6.732</td>
<td>0.231</td>
<td>tC2Q</td>
<td>RR</td>
<td>2</td>
<td>R22C27[2][B]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_5_s0/Q</td>
</tr>
<tr>
<td>6.906</td>
<td>0.174</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C27[3][A]</td>
<td>cntInst2/n5_s5/I2</td>
</tr>
<tr>
<td>7.461</td>
<td>0.555</td>
<td>tINS</td>
<td>RF</td>
<td>1</td>
<td>R22C27[3][A]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s5/F</td>
</tr>
<tr>
<td>7.874</td>
<td>0.413</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C29[3][B]</td>
<td>cntInst2/n5_s1/I2</td>
</tr>
<tr>
<td>8.429</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>2</td>
<td>R22C29[3][B]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s1/F</td>
</tr>
<tr>
<td>8.848</td>
<td>0.419</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C26[1][A]</td>
<td>cntInst2/n5_s11/I0</td>
</tr>
<tr>
<td>9.310</td>
<td>0.462</td>
<td>tINS</td>
<td>FR</td>
<td>32</td>
<td>R22C26[1][A]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s11/F</td>
</tr>
<tr>
<td>9.677</td>
<td>0.367</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C27[1][B]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_3_s0/RESET</td>
</tr>
</table>
<p><b>Data Required Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>20.000</td>
<td>20.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>20.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>20.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>24.230</td>
<td>4.230</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>26.501</td>
<td>2.271</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C27[1][B]</td>
<td>cntInst2/cnt_r_3_s0/CLK</td>
</tr>
<tr>
<td>26.466</td>
<td>-0.035</td>
<td>tSu</td>
<td></td>
<td>1</td>
<td>R22C27[1][B]</td>
<td>cntInst2/cnt_r_3_s0</td>
</tr>
</table>
<p><b>Path Statistics:</b></p>
<table class="summary_table">
<tr>
<td class="label">Clock Skew</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Setup Relationship </td>
<td>20.000</td>
</tr>
<tr>
<td class="label">Logic Level</td>
<td>4</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay</td>
<td>cell: 4.230, 65.063%; route: 2.271, 34.937%</td>
</tr>
<tr>
<td class="label">Arrival Data Path Delay</td>
<td>cell: 1.572, 49.498%; route: 1.373, 43.229%; tC2Q: 0.231, 7.274%</td>
</tr>
<tr>
<td class="label">Required Clock Path Delay</td>
<td>cell: 4.230, 65.063%; route: 2.271, 34.937%</td>
</tr>
</table>
<h3>Path25</h3>
<p><b>Path Summary:</b></p>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>16.789</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>9.677</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>26.466</td>
</tr>
<tr>
<td class="label">From</td>
<td>cntInst2/cnt_r_5_s0</td>
</tr>
<tr>
<td class="label">To</td>
<td>cntInst2/cnt_r_4_s0</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>clk_in:[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>clk_in:[R]</td>
</tr>
</table>
<p><b>Data Arrival Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>4.230</td>
<td>4.230</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>6.501</td>
<td>2.271</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C27[2][B]</td>
<td>cntInst2/cnt_r_5_s0/CLK</td>
</tr>
<tr>
<td>6.732</td>
<td>0.231</td>
<td>tC2Q</td>
<td>RR</td>
<td>2</td>
<td>R22C27[2][B]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_5_s0/Q</td>
</tr>
<tr>
<td>6.906</td>
<td>0.174</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C27[3][A]</td>
<td>cntInst2/n5_s5/I2</td>
</tr>
<tr>
<td>7.461</td>
<td>0.555</td>
<td>tINS</td>
<td>RF</td>
<td>1</td>
<td>R22C27[3][A]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s5/F</td>
</tr>
<tr>
<td>7.874</td>
<td>0.413</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C29[3][B]</td>
<td>cntInst2/n5_s1/I2</td>
</tr>
<tr>
<td>8.429</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>2</td>
<td>R22C29[3][B]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s1/F</td>
</tr>
<tr>
<td>8.848</td>
<td>0.419</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C26[1][A]</td>
<td>cntInst2/n5_s11/I0</td>
</tr>
<tr>
<td>9.310</td>
<td>0.462</td>
<td>tINS</td>
<td>FR</td>
<td>32</td>
<td>R22C26[1][A]</td>
<td style=" background: #97FFFF;">cntInst2/n5_s11/F</td>
</tr>
<tr>
<td>9.677</td>
<td>0.367</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C27[2][A]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_4_s0/RESET</td>
</tr>
</table>
<p><b>Data Required Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>20.000</td>
<td>20.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>20.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>20.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>24.230</td>
<td>4.230</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>26.501</td>
<td>2.271</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C27[2][A]</td>
<td>cntInst2/cnt_r_4_s0/CLK</td>
</tr>
<tr>
<td>26.466</td>
<td>-0.035</td>
<td>tSu</td>
<td></td>
<td>1</td>
<td>R22C27[2][A]</td>
<td>cntInst2/cnt_r_4_s0</td>
</tr>
</table>
<p><b>Path Statistics:</b></p>
<table class="summary_table">
<tr>
<td class="label">Clock Skew</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Setup Relationship </td>
<td>20.000</td>
</tr>
<tr>
<td class="label">Logic Level</td>
<td>4</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay</td>
<td>cell: 4.230, 65.063%; route: 2.271, 34.937%</td>
</tr>
<tr>
<td class="label">Arrival Data Path Delay</td>
<td>cell: 1.572, 49.498%; route: 1.373, 43.229%; tC2Q: 0.231, 7.274%</td>
</tr>
<tr>
<td class="label">Required Clock Path Delay</td>
<td>cell: 4.230, 65.063%; route: 2.271, 34.937%</td>
</tr>
</table>
<h3><a name="Hold_Analysis">Hold Analysis Report</a></h3>
<h4>Report Command:report_timing -hold -from_clock [get_clocks {clk_in}] -to_clock [get_clocks {clk_in}] -max_paths 25 -max_common_paths 1</h4>
<h3>Path1</h3>
<p><b>Path Summary:</b></p>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>0.425</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>5.074</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>4.649</td>
</tr>
<tr>
<td class="label">From</td>
<td>cntInst2/cnt_r_2_s0</td>
</tr>
<tr>
<td class="label">To</td>
<td>cntInst2/cnt_r_2_s0</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>clk_in:[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>clk_in:[R]</td>
</tr>
</table>
<p><b>Data Arrival Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>3.126</td>
<td>3.126</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>4.638</td>
<td>1.511</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C27[1][A]</td>
<td>cntInst2/cnt_r_2_s0/CLK</td>
</tr>
<tr>
<td>4.840</td>
<td>0.202</td>
<td>tC2Q</td>
<td>RR</td>
<td>2</td>
<td>R22C27[1][A]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_2_s0/Q</td>
</tr>
<tr>
<td>4.842</td>
<td>0.002</td>
<td>tNET</td>
<td>RR</td>
<td>2</td>
<td>R22C27[1][A]</td>
<td>cntInst2/n37_s/I1</td>
</tr>
<tr>
<td>5.074</td>
<td>0.232</td>
<td>tINS</td>
<td>RF</td>
<td>1</td>
<td>R22C27[1][A]</td>
<td style=" background: #97FFFF;">cntInst2/n37_s/SUM</td>
</tr>
<tr>
<td>5.074</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C27[1][A]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_2_s0/D</td>
</tr>
</table>
<p><b>Data Required Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>3.126</td>
<td>3.126</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>4.638</td>
<td>1.511</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C27[1][A]</td>
<td>cntInst2/cnt_r_2_s0/CLK</td>
</tr>
<tr>
<td>4.649</td>
<td>0.011</td>
<td>tHld</td>
<td></td>
<td>1</td>
<td>R22C27[1][A]</td>
<td>cntInst2/cnt_r_2_s0</td>
</tr>
</table>
<p><b>Path Statistics:</b></p>
<table class="summary_table">
<tr>
<td class="label">Clock Skew</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Hold Relationship </td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Logic Level</td>
<td>2</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay</td>
<td>cell: 3.126, 67.413%; route: 1.511, 32.587%</td>
</tr>
<tr>
<td class="label">Arrival Data Path Delay</td>
<td>cell: 0.232, 53.157%; route: 0.002, 0.560%; tC2Q: 0.202, 46.283%</td>
</tr>
<tr>
<td class="label">Required Clock Path Delay</td>
<td>cell: 3.126, 67.413%; route: 1.511, 32.587%</td>
</tr>
</table>
<h3>Path2</h3>
<p><b>Path Summary:</b></p>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>0.425</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>5.074</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>4.649</td>
</tr>
<tr>
<td class="label">From</td>
<td>cntInst2/cnt_r_6_s0</td>
</tr>
<tr>
<td class="label">To</td>
<td>cntInst2/cnt_r_6_s0</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>clk_in:[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>clk_in:[R]</td>
</tr>
</table>
<p><b>Data Arrival Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>3.126</td>
<td>3.126</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>4.638</td>
<td>1.511</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C28[0][A]</td>
<td>cntInst2/cnt_r_6_s0/CLK</td>
</tr>
<tr>
<td>4.840</td>
<td>0.202</td>
<td>tC2Q</td>
<td>RR</td>
<td>2</td>
<td>R22C28[0][A]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_6_s0/Q</td>
</tr>
<tr>
<td>4.842</td>
<td>0.002</td>
<td>tNET</td>
<td>RR</td>
<td>2</td>
<td>R22C28[0][A]</td>
<td>cntInst2/n33_s/I1</td>
</tr>
<tr>
<td>5.074</td>
<td>0.232</td>
<td>tINS</td>
<td>RF</td>
<td>1</td>
<td>R22C28[0][A]</td>
<td style=" background: #97FFFF;">cntInst2/n33_s/SUM</td>
</tr>
<tr>
<td>5.074</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C28[0][A]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_6_s0/D</td>
</tr>
</table>
<p><b>Data Required Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>3.126</td>
<td>3.126</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>4.638</td>
<td>1.511</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C28[0][A]</td>
<td>cntInst2/cnt_r_6_s0/CLK</td>
</tr>
<tr>
<td>4.649</td>
<td>0.011</td>
<td>tHld</td>
<td></td>
<td>1</td>
<td>R22C28[0][A]</td>
<td>cntInst2/cnt_r_6_s0</td>
</tr>
</table>
<p><b>Path Statistics:</b></p>
<table class="summary_table">
<tr>
<td class="label">Clock Skew</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Hold Relationship </td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Logic Level</td>
<td>2</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay</td>
<td>cell: 3.126, 67.413%; route: 1.511, 32.587%</td>
</tr>
<tr>
<td class="label">Arrival Data Path Delay</td>
<td>cell: 0.232, 53.157%; route: 0.002, 0.560%; tC2Q: 0.202, 46.283%</td>
</tr>
<tr>
<td class="label">Required Clock Path Delay</td>
<td>cell: 3.126, 67.413%; route: 1.511, 32.587%</td>
</tr>
</table>
<h3>Path3</h3>
<p><b>Path Summary:</b></p>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>0.425</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>5.074</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>4.649</td>
</tr>
<tr>
<td class="label">From</td>
<td>cntInst2/cnt_r_8_s0</td>
</tr>
<tr>
<td class="label">To</td>
<td>cntInst2/cnt_r_8_s0</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>clk_in:[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>clk_in:[R]</td>
</tr>
</table>
<p><b>Data Arrival Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>3.126</td>
<td>3.126</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>4.638</td>
<td>1.511</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C28[1][A]</td>
<td>cntInst2/cnt_r_8_s0/CLK</td>
</tr>
<tr>
<td>4.840</td>
<td>0.202</td>
<td>tC2Q</td>
<td>RR</td>
<td>2</td>
<td>R22C28[1][A]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_8_s0/Q</td>
</tr>
<tr>
<td>4.842</td>
<td>0.002</td>
<td>tNET</td>
<td>RR</td>
<td>2</td>
<td>R22C28[1][A]</td>
<td>cntInst2/n31_s/I1</td>
</tr>
<tr>
<td>5.074</td>
<td>0.232</td>
<td>tINS</td>
<td>RF</td>
<td>1</td>
<td>R22C28[1][A]</td>
<td style=" background: #97FFFF;">cntInst2/n31_s/SUM</td>
</tr>
<tr>
<td>5.074</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C28[1][A]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_8_s0/D</td>
</tr>
</table>
<p><b>Data Required Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>3.126</td>
<td>3.126</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>4.638</td>
<td>1.511</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C28[1][A]</td>
<td>cntInst2/cnt_r_8_s0/CLK</td>
</tr>
<tr>
<td>4.649</td>
<td>0.011</td>
<td>tHld</td>
<td></td>
<td>1</td>
<td>R22C28[1][A]</td>
<td>cntInst2/cnt_r_8_s0</td>
</tr>
</table>
<p><b>Path Statistics:</b></p>
<table class="summary_table">
<tr>
<td class="label">Clock Skew</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Hold Relationship </td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Logic Level</td>
<td>2</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay</td>
<td>cell: 3.126, 67.413%; route: 1.511, 32.587%</td>
</tr>
<tr>
<td class="label">Arrival Data Path Delay</td>
<td>cell: 0.232, 53.157%; route: 0.002, 0.560%; tC2Q: 0.202, 46.283%</td>
</tr>
<tr>
<td class="label">Required Clock Path Delay</td>
<td>cell: 3.126, 67.413%; route: 1.511, 32.587%</td>
</tr>
</table>
<h3>Path4</h3>
<p><b>Path Summary:</b></p>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>0.425</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>5.074</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>4.649</td>
</tr>
<tr>
<td class="label">From</td>
<td>cntInst2/cnt_r_12_s0</td>
</tr>
<tr>
<td class="label">To</td>
<td>cntInst2/cnt_r_12_s0</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>clk_in:[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>clk_in:[R]</td>
</tr>
</table>
<p><b>Data Arrival Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>3.126</td>
<td>3.126</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>4.638</td>
<td>1.511</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C29[0][A]</td>
<td>cntInst2/cnt_r_12_s0/CLK</td>
</tr>
<tr>
<td>4.840</td>
<td>0.202</td>
<td>tC2Q</td>
<td>RR</td>
<td>2</td>
<td>R22C29[0][A]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_12_s0/Q</td>
</tr>
<tr>
<td>4.842</td>
<td>0.002</td>
<td>tNET</td>
<td>RR</td>
<td>2</td>
<td>R22C29[0][A]</td>
<td>cntInst2/n27_s/I1</td>
</tr>
<tr>
<td>5.074</td>
<td>0.232</td>
<td>tINS</td>
<td>RF</td>
<td>1</td>
<td>R22C29[0][A]</td>
<td style=" background: #97FFFF;">cntInst2/n27_s/SUM</td>
</tr>
<tr>
<td>5.074</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C29[0][A]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_12_s0/D</td>
</tr>
</table>
<p><b>Data Required Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>3.126</td>
<td>3.126</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>4.638</td>
<td>1.511</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C29[0][A]</td>
<td>cntInst2/cnt_r_12_s0/CLK</td>
</tr>
<tr>
<td>4.649</td>
<td>0.011</td>
<td>tHld</td>
<td></td>
<td>1</td>
<td>R22C29[0][A]</td>
<td>cntInst2/cnt_r_12_s0</td>
</tr>
</table>
<p><b>Path Statistics:</b></p>
<table class="summary_table">
<tr>
<td class="label">Clock Skew</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Hold Relationship </td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Logic Level</td>
<td>2</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay</td>
<td>cell: 3.126, 67.413%; route: 1.511, 32.587%</td>
</tr>
<tr>
<td class="label">Arrival Data Path Delay</td>
<td>cell: 0.232, 53.157%; route: 0.002, 0.560%; tC2Q: 0.202, 46.283%</td>
</tr>
<tr>
<td class="label">Required Clock Path Delay</td>
<td>cell: 3.126, 67.413%; route: 1.511, 32.587%</td>
</tr>
</table>
<h3>Path5</h3>
<p><b>Path Summary:</b></p>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>0.425</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>5.074</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>4.649</td>
</tr>
<tr>
<td class="label">From</td>
<td>cntInst2/cnt_r_14_s0</td>
</tr>
<tr>
<td class="label">To</td>
<td>cntInst2/cnt_r_14_s0</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>clk_in:[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>clk_in:[R]</td>
</tr>
</table>
<p><b>Data Arrival Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>3.126</td>
<td>3.126</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>4.638</td>
<td>1.511</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C29[1][A]</td>
<td>cntInst2/cnt_r_14_s0/CLK</td>
</tr>
<tr>
<td>4.840</td>
<td>0.202</td>
<td>tC2Q</td>
<td>RR</td>
<td>2</td>
<td>R22C29[1][A]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_14_s0/Q</td>
</tr>
<tr>
<td>4.842</td>
<td>0.002</td>
<td>tNET</td>
<td>RR</td>
<td>2</td>
<td>R22C29[1][A]</td>
<td>cntInst2/n25_s/I1</td>
</tr>
<tr>
<td>5.074</td>
<td>0.232</td>
<td>tINS</td>
<td>RF</td>
<td>1</td>
<td>R22C29[1][A]</td>
<td style=" background: #97FFFF;">cntInst2/n25_s/SUM</td>
</tr>
<tr>
<td>5.074</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C29[1][A]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_14_s0/D</td>
</tr>
</table>
<p><b>Data Required Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>3.126</td>
<td>3.126</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>4.638</td>
<td>1.511</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C29[1][A]</td>
<td>cntInst2/cnt_r_14_s0/CLK</td>
</tr>
<tr>
<td>4.649</td>
<td>0.011</td>
<td>tHld</td>
<td></td>
<td>1</td>
<td>R22C29[1][A]</td>
<td>cntInst2/cnt_r_14_s0</td>
</tr>
</table>
<p><b>Path Statistics:</b></p>
<table class="summary_table">
<tr>
<td class="label">Clock Skew</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Hold Relationship </td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Logic Level</td>
<td>2</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay</td>
<td>cell: 3.126, 67.413%; route: 1.511, 32.587%</td>
</tr>
<tr>
<td class="label">Arrival Data Path Delay</td>
<td>cell: 0.232, 53.157%; route: 0.002, 0.560%; tC2Q: 0.202, 46.283%</td>
</tr>
<tr>
<td class="label">Required Clock Path Delay</td>
<td>cell: 3.126, 67.413%; route: 1.511, 32.587%</td>
</tr>
</table>
<h3>Path6</h3>
<p><b>Path Summary:</b></p>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>0.425</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>5.074</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>4.649</td>
</tr>
<tr>
<td class="label">From</td>
<td>cntInst2/cnt_r_18_s0</td>
</tr>
<tr>
<td class="label">To</td>
<td>cntInst2/cnt_r_18_s0</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>clk_in:[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>clk_in:[R]</td>
</tr>
</table>
<p><b>Data Arrival Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>3.126</td>
<td>3.126</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>4.638</td>
<td>1.511</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C30[0][A]</td>
<td>cntInst2/cnt_r_18_s0/CLK</td>
</tr>
<tr>
<td>4.840</td>
<td>0.202</td>
<td>tC2Q</td>
<td>RR</td>
<td>2</td>
<td>R22C30[0][A]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_18_s0/Q</td>
</tr>
<tr>
<td>4.842</td>
<td>0.002</td>
<td>tNET</td>
<td>RR</td>
<td>2</td>
<td>R22C30[0][A]</td>
<td>cntInst2/n21_s/I1</td>
</tr>
<tr>
<td>5.074</td>
<td>0.232</td>
<td>tINS</td>
<td>RF</td>
<td>1</td>
<td>R22C30[0][A]</td>
<td style=" background: #97FFFF;">cntInst2/n21_s/SUM</td>
</tr>
<tr>
<td>5.074</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C30[0][A]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_18_s0/D</td>
</tr>
</table>
<p><b>Data Required Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>3.126</td>
<td>3.126</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>4.638</td>
<td>1.511</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C30[0][A]</td>
<td>cntInst2/cnt_r_18_s0/CLK</td>
</tr>
<tr>
<td>4.649</td>
<td>0.011</td>
<td>tHld</td>
<td></td>
<td>1</td>
<td>R22C30[0][A]</td>
<td>cntInst2/cnt_r_18_s0</td>
</tr>
</table>
<p><b>Path Statistics:</b></p>
<table class="summary_table">
<tr>
<td class="label">Clock Skew</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Hold Relationship </td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Logic Level</td>
<td>2</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay</td>
<td>cell: 3.126, 67.413%; route: 1.511, 32.587%</td>
</tr>
<tr>
<td class="label">Arrival Data Path Delay</td>
<td>cell: 0.232, 53.157%; route: 0.002, 0.560%; tC2Q: 0.202, 46.283%</td>
</tr>
<tr>
<td class="label">Required Clock Path Delay</td>
<td>cell: 3.126, 67.413%; route: 1.511, 32.587%</td>
</tr>
</table>
<h3>Path7</h3>
<p><b>Path Summary:</b></p>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>0.425</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>5.074</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>4.649</td>
</tr>
<tr>
<td class="label">From</td>
<td>cntInst2/cnt_r_20_s0</td>
</tr>
<tr>
<td class="label">To</td>
<td>cntInst2/cnt_r_20_s0</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>clk_in:[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>clk_in:[R]</td>
</tr>
</table>
<p><b>Data Arrival Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>3.126</td>
<td>3.126</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>4.638</td>
<td>1.511</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C30[1][A]</td>
<td>cntInst2/cnt_r_20_s0/CLK</td>
</tr>
<tr>
<td>4.840</td>
<td>0.202</td>
<td>tC2Q</td>
<td>RR</td>
<td>2</td>
<td>R22C30[1][A]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_20_s0/Q</td>
</tr>
<tr>
<td>4.842</td>
<td>0.002</td>
<td>tNET</td>
<td>RR</td>
<td>2</td>
<td>R22C30[1][A]</td>
<td>cntInst2/n19_s/I1</td>
</tr>
<tr>
<td>5.074</td>
<td>0.232</td>
<td>tINS</td>
<td>RF</td>
<td>1</td>
<td>R22C30[1][A]</td>
<td style=" background: #97FFFF;">cntInst2/n19_s/SUM</td>
</tr>
<tr>
<td>5.074</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C30[1][A]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_20_s0/D</td>
</tr>
</table>
<p><b>Data Required Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>3.126</td>
<td>3.126</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>4.638</td>
<td>1.511</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C30[1][A]</td>
<td>cntInst2/cnt_r_20_s0/CLK</td>
</tr>
<tr>
<td>4.649</td>
<td>0.011</td>
<td>tHld</td>
<td></td>
<td>1</td>
<td>R22C30[1][A]</td>
<td>cntInst2/cnt_r_20_s0</td>
</tr>
</table>
<p><b>Path Statistics:</b></p>
<table class="summary_table">
<tr>
<td class="label">Clock Skew</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Hold Relationship </td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Logic Level</td>
<td>2</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay</td>
<td>cell: 3.126, 67.413%; route: 1.511, 32.587%</td>
</tr>
<tr>
<td class="label">Arrival Data Path Delay</td>
<td>cell: 0.232, 53.157%; route: 0.002, 0.560%; tC2Q: 0.202, 46.283%</td>
</tr>
<tr>
<td class="label">Required Clock Path Delay</td>
<td>cell: 3.126, 67.413%; route: 1.511, 32.587%</td>
</tr>
</table>
<h3>Path8</h3>
<p><b>Path Summary:</b></p>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>0.425</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>5.074</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>4.649</td>
</tr>
<tr>
<td class="label">From</td>
<td>cntInst2/cnt_r_24_s0</td>
</tr>
<tr>
<td class="label">To</td>
<td>cntInst2/cnt_r_24_s0</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>clk_in:[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>clk_in:[R]</td>
</tr>
</table>
<p><b>Data Arrival Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>3.126</td>
<td>3.126</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>4.638</td>
<td>1.511</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C31[0][A]</td>
<td>cntInst2/cnt_r_24_s0/CLK</td>
</tr>
<tr>
<td>4.840</td>
<td>0.202</td>
<td>tC2Q</td>
<td>RR</td>
<td>2</td>
<td>R22C31[0][A]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_24_s0/Q</td>
</tr>
<tr>
<td>4.842</td>
<td>0.002</td>
<td>tNET</td>
<td>RR</td>
<td>2</td>
<td>R22C31[0][A]</td>
<td>cntInst2/n15_s/I1</td>
</tr>
<tr>
<td>5.074</td>
<td>0.232</td>
<td>tINS</td>
<td>RF</td>
<td>1</td>
<td>R22C31[0][A]</td>
<td style=" background: #97FFFF;">cntInst2/n15_s/SUM</td>
</tr>
<tr>
<td>5.074</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C31[0][A]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_24_s0/D</td>
</tr>
</table>
<p><b>Data Required Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>3.126</td>
<td>3.126</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>4.638</td>
<td>1.511</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C31[0][A]</td>
<td>cntInst2/cnt_r_24_s0/CLK</td>
</tr>
<tr>
<td>4.649</td>
<td>0.011</td>
<td>tHld</td>
<td></td>
<td>1</td>
<td>R22C31[0][A]</td>
<td>cntInst2/cnt_r_24_s0</td>
</tr>
</table>
<p><b>Path Statistics:</b></p>
<table class="summary_table">
<tr>
<td class="label">Clock Skew</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Hold Relationship </td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Logic Level</td>
<td>2</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay</td>
<td>cell: 3.126, 67.413%; route: 1.511, 32.587%</td>
</tr>
<tr>
<td class="label">Arrival Data Path Delay</td>
<td>cell: 0.232, 53.157%; route: 0.002, 0.560%; tC2Q: 0.202, 46.283%</td>
</tr>
<tr>
<td class="label">Required Clock Path Delay</td>
<td>cell: 3.126, 67.413%; route: 1.511, 32.587%</td>
</tr>
</table>
<h3>Path9</h3>
<p><b>Path Summary:</b></p>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>0.425</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>5.074</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>4.649</td>
</tr>
<tr>
<td class="label">From</td>
<td>cntInst2/cnt_r_26_s0</td>
</tr>
<tr>
<td class="label">To</td>
<td>cntInst2/cnt_r_26_s0</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>clk_in:[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>clk_in:[R]</td>
</tr>
</table>
<p><b>Data Arrival Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>3.126</td>
<td>3.126</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>4.638</td>
<td>1.511</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C31[1][A]</td>
<td>cntInst2/cnt_r_26_s0/CLK</td>
</tr>
<tr>
<td>4.840</td>
<td>0.202</td>
<td>tC2Q</td>
<td>RR</td>
<td>2</td>
<td>R22C31[1][A]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_26_s0/Q</td>
</tr>
<tr>
<td>4.842</td>
<td>0.002</td>
<td>tNET</td>
<td>RR</td>
<td>2</td>
<td>R22C31[1][A]</td>
<td>cntInst2/n13_s/I1</td>
</tr>
<tr>
<td>5.074</td>
<td>0.232</td>
<td>tINS</td>
<td>RF</td>
<td>1</td>
<td>R22C31[1][A]</td>
<td style=" background: #97FFFF;">cntInst2/n13_s/SUM</td>
</tr>
<tr>
<td>5.074</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C31[1][A]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_26_s0/D</td>
</tr>
</table>
<p><b>Data Required Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>3.126</td>
<td>3.126</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>4.638</td>
<td>1.511</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C31[1][A]</td>
<td>cntInst2/cnt_r_26_s0/CLK</td>
</tr>
<tr>
<td>4.649</td>
<td>0.011</td>
<td>tHld</td>
<td></td>
<td>1</td>
<td>R22C31[1][A]</td>
<td>cntInst2/cnt_r_26_s0</td>
</tr>
</table>
<p><b>Path Statistics:</b></p>
<table class="summary_table">
<tr>
<td class="label">Clock Skew</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Hold Relationship </td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Logic Level</td>
<td>2</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay</td>
<td>cell: 3.126, 67.413%; route: 1.511, 32.587%</td>
</tr>
<tr>
<td class="label">Arrival Data Path Delay</td>
<td>cell: 0.232, 53.157%; route: 0.002, 0.560%; tC2Q: 0.202, 46.283%</td>
</tr>
<tr>
<td class="label">Required Clock Path Delay</td>
<td>cell: 3.126, 67.413%; route: 1.511, 32.587%</td>
</tr>
</table>
<h3>Path10</h3>
<p><b>Path Summary:</b></p>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>0.425</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>5.074</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>4.649</td>
</tr>
<tr>
<td class="label">From</td>
<td>cntInst2/cnt_r_30_s0</td>
</tr>
<tr>
<td class="label">To</td>
<td>cntInst2/cnt_r_30_s0</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>clk_in:[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>clk_in:[R]</td>
</tr>
</table>
<p><b>Data Arrival Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>3.126</td>
<td>3.126</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>4.638</td>
<td>1.511</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C32[0][A]</td>
<td>cntInst2/cnt_r_30_s0/CLK</td>
</tr>
<tr>
<td>4.840</td>
<td>0.202</td>
<td>tC2Q</td>
<td>RR</td>
<td>2</td>
<td>R22C32[0][A]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_30_s0/Q</td>
</tr>
<tr>
<td>4.842</td>
<td>0.002</td>
<td>tNET</td>
<td>RR</td>
<td>2</td>
<td>R22C32[0][A]</td>
<td>cntInst2/n9_s/I1</td>
</tr>
<tr>
<td>5.074</td>
<td>0.232</td>
<td>tINS</td>
<td>RF</td>
<td>1</td>
<td>R22C32[0][A]</td>
<td style=" background: #97FFFF;">cntInst2/n9_s/SUM</td>
</tr>
<tr>
<td>5.074</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C32[0][A]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_30_s0/D</td>
</tr>
</table>
<p><b>Data Required Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>3.126</td>
<td>3.126</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>4.638</td>
<td>1.511</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C32[0][A]</td>
<td>cntInst2/cnt_r_30_s0/CLK</td>
</tr>
<tr>
<td>4.649</td>
<td>0.011</td>
<td>tHld</td>
<td></td>
<td>1</td>
<td>R22C32[0][A]</td>
<td>cntInst2/cnt_r_30_s0</td>
</tr>
</table>
<p><b>Path Statistics:</b></p>
<table class="summary_table">
<tr>
<td class="label">Clock Skew</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Hold Relationship </td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Logic Level</td>
<td>2</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay</td>
<td>cell: 3.126, 67.413%; route: 1.511, 32.587%</td>
</tr>
<tr>
<td class="label">Arrival Data Path Delay</td>
<td>cell: 0.232, 53.157%; route: 0.002, 0.560%; tC2Q: 0.202, 46.283%</td>
</tr>
<tr>
<td class="label">Required Clock Path Delay</td>
<td>cell: 3.126, 67.413%; route: 1.511, 32.587%</td>
</tr>
</table>
<h3>Path11</h3>
<p><b>Path Summary:</b></p>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>0.427</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>5.075</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>4.649</td>
</tr>
<tr>
<td class="label">From</td>
<td>cntInst2/cnt_r_0_s0</td>
</tr>
<tr>
<td class="label">To</td>
<td>cntInst2/cnt_r_0_s0</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>clk_in:[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>clk_in:[R]</td>
</tr>
</table>
<p><b>Data Arrival Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>3.126</td>
<td>3.126</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>4.638</td>
<td>1.511</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R23C27[0][A]</td>
<td>cntInst2/cnt_r_0_s0/CLK</td>
</tr>
<tr>
<td>4.840</td>
<td>0.202</td>
<td>tC2Q</td>
<td>RR</td>
<td>3</td>
<td>R23C27[0][A]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_0_s0/Q</td>
</tr>
<tr>
<td>4.843</td>
<td>0.004</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R23C27[0][A]</td>
<td>cntInst2/n39_s2/I0</td>
</tr>
<tr>
<td>5.075</td>
<td>0.232</td>
<td>tINS</td>
<td>RF</td>
<td>1</td>
<td>R23C27[0][A]</td>
<td style=" background: #97FFFF;">cntInst2/n39_s2/F</td>
</tr>
<tr>
<td>5.075</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R23C27[0][A]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_0_s0/D</td>
</tr>
</table>
<p><b>Data Required Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>3.126</td>
<td>3.126</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>4.638</td>
<td>1.511</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R23C27[0][A]</td>
<td>cntInst2/cnt_r_0_s0/CLK</td>
</tr>
<tr>
<td>4.649</td>
<td>0.011</td>
<td>tHld</td>
<td></td>
<td>1</td>
<td>R23C27[0][A]</td>
<td>cntInst2/cnt_r_0_s0</td>
</tr>
</table>
<p><b>Path Statistics:</b></p>
<table class="summary_table">
<tr>
<td class="label">Clock Skew</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Hold Relationship </td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Logic Level</td>
<td>2</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay</td>
<td>cell: 3.126, 67.413%; route: 1.511, 32.587%</td>
</tr>
<tr>
<td class="label">Arrival Data Path Delay</td>
<td>cell: 0.232, 53.008%; route: 0.004, 0.838%; tC2Q: 0.202, 46.154%</td>
</tr>
<tr>
<td class="label">Required Clock Path Delay</td>
<td>cell: 3.126, 67.413%; route: 1.511, 32.587%</td>
</tr>
</table>
<h3>Path12</h3>
<p><b>Path Summary:</b></p>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>0.542</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>5.191</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>4.649</td>
</tr>
<tr>
<td class="label">From</td>
<td>cntInst2/cnt_r_31_s0</td>
</tr>
<tr>
<td class="label">To</td>
<td>cntInst2/cnt_r_31_s0</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>clk_in:[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>clk_in:[R]</td>
</tr>
</table>
<p><b>Data Arrival Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>3.126</td>
<td>3.126</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>4.638</td>
<td>1.511</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C32[0][B]</td>
<td>cntInst2/cnt_r_31_s0/CLK</td>
</tr>
<tr>
<td>4.839</td>
<td>0.201</td>
<td>tC2Q</td>
<td>RF</td>
<td>2</td>
<td>R22C32[0][B]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_31_s0/Q</td>
</tr>
<tr>
<td>4.959</td>
<td>0.120</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>R22C32[0][B]</td>
<td>cntInst2/n8_s/I1</td>
</tr>
<tr>
<td>5.191</td>
<td>0.232</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>R22C32[0][B]</td>
<td style=" background: #97FFFF;">cntInst2/n8_s/SUM</td>
</tr>
<tr>
<td>5.191</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C32[0][B]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_31_s0/D</td>
</tr>
</table>
<p><b>Data Required Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>3.126</td>
<td>3.126</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>4.638</td>
<td>1.511</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C32[0][B]</td>
<td>cntInst2/cnt_r_31_s0/CLK</td>
</tr>
<tr>
<td>4.649</td>
<td>0.011</td>
<td>tHld</td>
<td></td>
<td>1</td>
<td>R22C32[0][B]</td>
<td>cntInst2/cnt_r_31_s0</td>
</tr>
</table>
<p><b>Path Statistics:</b></p>
<table class="summary_table">
<tr>
<td class="label">Clock Skew</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Hold Relationship </td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Logic Level</td>
<td>2</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay</td>
<td>cell: 3.126, 67.413%; route: 1.511, 32.587%</td>
</tr>
<tr>
<td class="label">Arrival Data Path Delay</td>
<td>cell: 0.232, 41.934%; route: 0.120, 21.736%; tC2Q: 0.201, 36.331%</td>
</tr>
<tr>
<td class="label">Required Clock Path Delay</td>
<td>cell: 3.126, 67.413%; route: 1.511, 32.587%</td>
</tr>
</table>
<h3>Path13</h3>
<p><b>Path Summary:</b></p>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>0.542</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>5.191</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>4.649</td>
</tr>
<tr>
<td class="label">From</td>
<td>cntInst2/cnt_r_4_s0</td>
</tr>
<tr>
<td class="label">To</td>
<td>cntInst2/cnt_r_4_s0</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>clk_in:[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>clk_in:[R]</td>
</tr>
</table>
<p><b>Data Arrival Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>3.126</td>
<td>3.126</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>4.638</td>
<td>1.511</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C27[2][A]</td>
<td>cntInst2/cnt_r_4_s0/CLK</td>
</tr>
<tr>
<td>4.839</td>
<td>0.201</td>
<td>tC2Q</td>
<td>RF</td>
<td>2</td>
<td>R22C27[2][A]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_4_s0/Q</td>
</tr>
<tr>
<td>4.959</td>
<td>0.120</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>R22C27[2][A]</td>
<td>cntInst2/n35_s/I1</td>
</tr>
<tr>
<td>5.191</td>
<td>0.232</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>R22C27[2][A]</td>
<td style=" background: #97FFFF;">cntInst2/n35_s/SUM</td>
</tr>
<tr>
<td>5.191</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C27[2][A]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_4_s0/D</td>
</tr>
</table>
<p><b>Data Required Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>3.126</td>
<td>3.126</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>4.638</td>
<td>1.511</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C27[2][A]</td>
<td>cntInst2/cnt_r_4_s0/CLK</td>
</tr>
<tr>
<td>4.649</td>
<td>0.011</td>
<td>tHld</td>
<td></td>
<td>1</td>
<td>R22C27[2][A]</td>
<td>cntInst2/cnt_r_4_s0</td>
</tr>
</table>
<p><b>Path Statistics:</b></p>
<table class="summary_table">
<tr>
<td class="label">Clock Skew</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Hold Relationship </td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Logic Level</td>
<td>2</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay</td>
<td>cell: 3.126, 67.413%; route: 1.511, 32.587%</td>
</tr>
<tr>
<td class="label">Arrival Data Path Delay</td>
<td>cell: 0.232, 41.934%; route: 0.120, 21.736%; tC2Q: 0.201, 36.331%</td>
</tr>
<tr>
<td class="label">Required Clock Path Delay</td>
<td>cell: 3.126, 67.413%; route: 1.511, 32.587%</td>
</tr>
</table>
<h3>Path14</h3>
<p><b>Path Summary:</b></p>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>0.542</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>5.191</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>4.649</td>
</tr>
<tr>
<td class="label">From</td>
<td>cntInst2/cnt_r_9_s0</td>
</tr>
<tr>
<td class="label">To</td>
<td>cntInst2/cnt_r_9_s0</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>clk_in:[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>clk_in:[R]</td>
</tr>
</table>
<p><b>Data Arrival Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>3.126</td>
<td>3.126</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>4.638</td>
<td>1.511</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C28[1][B]</td>
<td>cntInst2/cnt_r_9_s0/CLK</td>
</tr>
<tr>
<td>4.839</td>
<td>0.201</td>
<td>tC2Q</td>
<td>RF</td>
<td>2</td>
<td>R22C28[1][B]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_9_s0/Q</td>
</tr>
<tr>
<td>4.959</td>
<td>0.120</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>R22C28[1][B]</td>
<td>cntInst2/n30_s/I1</td>
</tr>
<tr>
<td>5.191</td>
<td>0.232</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>R22C28[1][B]</td>
<td style=" background: #97FFFF;">cntInst2/n30_s/SUM</td>
</tr>
<tr>
<td>5.191</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C28[1][B]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_9_s0/D</td>
</tr>
</table>
<p><b>Data Required Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>3.126</td>
<td>3.126</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>4.638</td>
<td>1.511</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C28[1][B]</td>
<td>cntInst2/cnt_r_9_s0/CLK</td>
</tr>
<tr>
<td>4.649</td>
<td>0.011</td>
<td>tHld</td>
<td></td>
<td>1</td>
<td>R22C28[1][B]</td>
<td>cntInst2/cnt_r_9_s0</td>
</tr>
</table>
<p><b>Path Statistics:</b></p>
<table class="summary_table">
<tr>
<td class="label">Clock Skew</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Hold Relationship </td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Logic Level</td>
<td>2</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay</td>
<td>cell: 3.126, 67.413%; route: 1.511, 32.587%</td>
</tr>
<tr>
<td class="label">Arrival Data Path Delay</td>
<td>cell: 0.232, 41.934%; route: 0.120, 21.736%; tC2Q: 0.201, 36.331%</td>
</tr>
<tr>
<td class="label">Required Clock Path Delay</td>
<td>cell: 3.126, 67.413%; route: 1.511, 32.587%</td>
</tr>
</table>
<h3>Path15</h3>
<p><b>Path Summary:</b></p>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>0.542</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>5.191</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>4.649</td>
</tr>
<tr>
<td class="label">From</td>
<td>cntInst2/cnt_r_10_s0</td>
</tr>
<tr>
<td class="label">To</td>
<td>cntInst2/cnt_r_10_s0</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>clk_in:[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>clk_in:[R]</td>
</tr>
</table>
<p><b>Data Arrival Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>3.126</td>
<td>3.126</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>4.638</td>
<td>1.511</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C28[2][A]</td>
<td>cntInst2/cnt_r_10_s0/CLK</td>
</tr>
<tr>
<td>4.839</td>
<td>0.201</td>
<td>tC2Q</td>
<td>RF</td>
<td>2</td>
<td>R22C28[2][A]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_10_s0/Q</td>
</tr>
<tr>
<td>4.959</td>
<td>0.120</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>R22C28[2][A]</td>
<td>cntInst2/n29_s/I1</td>
</tr>
<tr>
<td>5.191</td>
<td>0.232</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>R22C28[2][A]</td>
<td style=" background: #97FFFF;">cntInst2/n29_s/SUM</td>
</tr>
<tr>
<td>5.191</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C28[2][A]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_10_s0/D</td>
</tr>
</table>
<p><b>Data Required Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>3.126</td>
<td>3.126</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>4.638</td>
<td>1.511</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C28[2][A]</td>
<td>cntInst2/cnt_r_10_s0/CLK</td>
</tr>
<tr>
<td>4.649</td>
<td>0.011</td>
<td>tHld</td>
<td></td>
<td>1</td>
<td>R22C28[2][A]</td>
<td>cntInst2/cnt_r_10_s0</td>
</tr>
</table>
<p><b>Path Statistics:</b></p>
<table class="summary_table">
<tr>
<td class="label">Clock Skew</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Hold Relationship </td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Logic Level</td>
<td>2</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay</td>
<td>cell: 3.126, 67.413%; route: 1.511, 32.587%</td>
</tr>
<tr>
<td class="label">Arrival Data Path Delay</td>
<td>cell: 0.232, 41.934%; route: 0.120, 21.736%; tC2Q: 0.201, 36.331%</td>
</tr>
<tr>
<td class="label">Required Clock Path Delay</td>
<td>cell: 3.126, 67.413%; route: 1.511, 32.587%</td>
</tr>
</table>
<h3>Path16</h3>
<p><b>Path Summary:</b></p>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>0.542</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>5.191</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>4.649</td>
</tr>
<tr>
<td class="label">From</td>
<td>cntInst2/cnt_r_13_s0</td>
</tr>
<tr>
<td class="label">To</td>
<td>cntInst2/cnt_r_13_s0</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>clk_in:[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>clk_in:[R]</td>
</tr>
</table>
<p><b>Data Arrival Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>3.126</td>
<td>3.126</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>4.638</td>
<td>1.511</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C29[0][B]</td>
<td>cntInst2/cnt_r_13_s0/CLK</td>
</tr>
<tr>
<td>4.839</td>
<td>0.201</td>
<td>tC2Q</td>
<td>RF</td>
<td>2</td>
<td>R22C29[0][B]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_13_s0/Q</td>
</tr>
<tr>
<td>4.959</td>
<td>0.120</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>R22C29[0][B]</td>
<td>cntInst2/n26_s/I1</td>
</tr>
<tr>
<td>5.191</td>
<td>0.232</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>R22C29[0][B]</td>
<td style=" background: #97FFFF;">cntInst2/n26_s/SUM</td>
</tr>
<tr>
<td>5.191</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C29[0][B]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_13_s0/D</td>
</tr>
</table>
<p><b>Data Required Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>3.126</td>
<td>3.126</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>4.638</td>
<td>1.511</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C29[0][B]</td>
<td>cntInst2/cnt_r_13_s0/CLK</td>
</tr>
<tr>
<td>4.649</td>
<td>0.011</td>
<td>tHld</td>
<td></td>
<td>1</td>
<td>R22C29[0][B]</td>
<td>cntInst2/cnt_r_13_s0</td>
</tr>
</table>
<p><b>Path Statistics:</b></p>
<table class="summary_table">
<tr>
<td class="label">Clock Skew</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Hold Relationship </td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Logic Level</td>
<td>2</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay</td>
<td>cell: 3.126, 67.413%; route: 1.511, 32.587%</td>
</tr>
<tr>
<td class="label">Arrival Data Path Delay</td>
<td>cell: 0.232, 41.934%; route: 0.120, 21.736%; tC2Q: 0.201, 36.331%</td>
</tr>
<tr>
<td class="label">Required Clock Path Delay</td>
<td>cell: 3.126, 67.413%; route: 1.511, 32.587%</td>
</tr>
</table>
<h3>Path17</h3>
<p><b>Path Summary:</b></p>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>0.542</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>5.191</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>4.649</td>
</tr>
<tr>
<td class="label">From</td>
<td>cntInst2/cnt_r_16_s0</td>
</tr>
<tr>
<td class="label">To</td>
<td>cntInst2/cnt_r_16_s0</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>clk_in:[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>clk_in:[R]</td>
</tr>
</table>
<p><b>Data Arrival Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>3.126</td>
<td>3.126</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>4.638</td>
<td>1.511</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C29[2][A]</td>
<td>cntInst2/cnt_r_16_s0/CLK</td>
</tr>
<tr>
<td>4.839</td>
<td>0.201</td>
<td>tC2Q</td>
<td>RF</td>
<td>2</td>
<td>R22C29[2][A]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_16_s0/Q</td>
</tr>
<tr>
<td>4.959</td>
<td>0.120</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>R22C29[2][A]</td>
<td>cntInst2/n23_s/I1</td>
</tr>
<tr>
<td>5.191</td>
<td>0.232</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>R22C29[2][A]</td>
<td style=" background: #97FFFF;">cntInst2/n23_s/SUM</td>
</tr>
<tr>
<td>5.191</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C29[2][A]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_16_s0/D</td>
</tr>
</table>
<p><b>Data Required Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>3.126</td>
<td>3.126</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>4.638</td>
<td>1.511</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C29[2][A]</td>
<td>cntInst2/cnt_r_16_s0/CLK</td>
</tr>
<tr>
<td>4.649</td>
<td>0.011</td>
<td>tHld</td>
<td></td>
<td>1</td>
<td>R22C29[2][A]</td>
<td>cntInst2/cnt_r_16_s0</td>
</tr>
</table>
<p><b>Path Statistics:</b></p>
<table class="summary_table">
<tr>
<td class="label">Clock Skew</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Hold Relationship </td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Logic Level</td>
<td>2</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay</td>
<td>cell: 3.126, 67.413%; route: 1.511, 32.587%</td>
</tr>
<tr>
<td class="label">Arrival Data Path Delay</td>
<td>cell: 0.232, 41.934%; route: 0.120, 21.736%; tC2Q: 0.201, 36.331%</td>
</tr>
<tr>
<td class="label">Required Clock Path Delay</td>
<td>cell: 3.126, 67.413%; route: 1.511, 32.587%</td>
</tr>
</table>
<h3>Path18</h3>
<p><b>Path Summary:</b></p>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>0.542</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>5.191</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>4.649</td>
</tr>
<tr>
<td class="label">From</td>
<td>cntInst2/cnt_r_17_s0</td>
</tr>
<tr>
<td class="label">To</td>
<td>cntInst2/cnt_r_17_s0</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>clk_in:[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>clk_in:[R]</td>
</tr>
</table>
<p><b>Data Arrival Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>3.126</td>
<td>3.126</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>4.638</td>
<td>1.511</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C29[2][B]</td>
<td>cntInst2/cnt_r_17_s0/CLK</td>
</tr>
<tr>
<td>4.839</td>
<td>0.201</td>
<td>tC2Q</td>
<td>RF</td>
<td>2</td>
<td>R22C29[2][B]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_17_s0/Q</td>
</tr>
<tr>
<td>4.959</td>
<td>0.120</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>R22C29[2][B]</td>
<td>cntInst2/n22_s/I1</td>
</tr>
<tr>
<td>5.191</td>
<td>0.232</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>R22C29[2][B]</td>
<td style=" background: #97FFFF;">cntInst2/n22_s/SUM</td>
</tr>
<tr>
<td>5.191</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C29[2][B]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_17_s0/D</td>
</tr>
</table>
<p><b>Data Required Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>3.126</td>
<td>3.126</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>4.638</td>
<td>1.511</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C29[2][B]</td>
<td>cntInst2/cnt_r_17_s0/CLK</td>
</tr>
<tr>
<td>4.649</td>
<td>0.011</td>
<td>tHld</td>
<td></td>
<td>1</td>
<td>R22C29[2][B]</td>
<td>cntInst2/cnt_r_17_s0</td>
</tr>
</table>
<p><b>Path Statistics:</b></p>
<table class="summary_table">
<tr>
<td class="label">Clock Skew</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Hold Relationship </td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Logic Level</td>
<td>2</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay</td>
<td>cell: 3.126, 67.413%; route: 1.511, 32.587%</td>
</tr>
<tr>
<td class="label">Arrival Data Path Delay</td>
<td>cell: 0.232, 41.934%; route: 0.120, 21.736%; tC2Q: 0.201, 36.331%</td>
</tr>
<tr>
<td class="label">Required Clock Path Delay</td>
<td>cell: 3.126, 67.413%; route: 1.511, 32.587%</td>
</tr>
</table>
<h3>Path19</h3>
<p><b>Path Summary:</b></p>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>0.542</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>5.191</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>4.649</td>
</tr>
<tr>
<td class="label">From</td>
<td>cntInst2/cnt_r_29_s0</td>
</tr>
<tr>
<td class="label">To</td>
<td>cntInst2/cnt_r_29_s0</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>clk_in:[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>clk_in:[R]</td>
</tr>
</table>
<p><b>Data Arrival Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>3.126</td>
<td>3.126</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>4.638</td>
<td>1.511</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C31[2][B]</td>
<td>cntInst2/cnt_r_29_s0/CLK</td>
</tr>
<tr>
<td>4.839</td>
<td>0.201</td>
<td>tC2Q</td>
<td>RF</td>
<td>2</td>
<td>R22C31[2][B]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_29_s0/Q</td>
</tr>
<tr>
<td>4.959</td>
<td>0.120</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>R22C31[2][B]</td>
<td>cntInst2/n10_s/I1</td>
</tr>
<tr>
<td>5.191</td>
<td>0.232</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>R22C31[2][B]</td>
<td style=" background: #97FFFF;">cntInst2/n10_s/SUM</td>
</tr>
<tr>
<td>5.191</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C31[2][B]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_29_s0/D</td>
</tr>
</table>
<p><b>Data Required Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>3.126</td>
<td>3.126</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>4.638</td>
<td>1.511</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C31[2][B]</td>
<td>cntInst2/cnt_r_29_s0/CLK</td>
</tr>
<tr>
<td>4.649</td>
<td>0.011</td>
<td>tHld</td>
<td></td>
<td>1</td>
<td>R22C31[2][B]</td>
<td>cntInst2/cnt_r_29_s0</td>
</tr>
</table>
<p><b>Path Statistics:</b></p>
<table class="summary_table">
<tr>
<td class="label">Clock Skew</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Hold Relationship </td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Logic Level</td>
<td>2</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay</td>
<td>cell: 3.126, 67.413%; route: 1.511, 32.587%</td>
</tr>
<tr>
<td class="label">Arrival Data Path Delay</td>
<td>cell: 0.232, 41.934%; route: 0.120, 21.736%; tC2Q: 0.201, 36.331%</td>
</tr>
<tr>
<td class="label">Required Clock Path Delay</td>
<td>cell: 3.126, 67.413%; route: 1.511, 32.587%</td>
</tr>
</table>
<h3>Path20</h3>
<p><b>Path Summary:</b></p>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>0.544</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>5.193</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>4.649</td>
</tr>
<tr>
<td class="label">From</td>
<td>cntInst2/cnt_r_7_s0</td>
</tr>
<tr>
<td class="label">To</td>
<td>cntInst2/cnt_r_7_s0</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>clk_in:[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>clk_in:[R]</td>
</tr>
</table>
<p><b>Data Arrival Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>3.126</td>
<td>3.126</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>4.638</td>
<td>1.511</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C28[0][B]</td>
<td>cntInst2/cnt_r_7_s0/CLK</td>
</tr>
<tr>
<td>4.839</td>
<td>0.201</td>
<td>tC2Q</td>
<td>RF</td>
<td>2</td>
<td>R22C28[0][B]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_7_s0/Q</td>
</tr>
<tr>
<td>4.961</td>
<td>0.122</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>R22C28[0][B]</td>
<td>cntInst2/n32_s/I1</td>
</tr>
<tr>
<td>5.193</td>
<td>0.232</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>R22C28[0][B]</td>
<td style=" background: #97FFFF;">cntInst2/n32_s/SUM</td>
</tr>
<tr>
<td>5.193</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C28[0][B]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_7_s0/D</td>
</tr>
</table>
<p><b>Data Required Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>3.126</td>
<td>3.126</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>4.638</td>
<td>1.511</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C28[0][B]</td>
<td>cntInst2/cnt_r_7_s0/CLK</td>
</tr>
<tr>
<td>4.649</td>
<td>0.011</td>
<td>tHld</td>
<td></td>
<td>1</td>
<td>R22C28[0][B]</td>
<td>cntInst2/cnt_r_7_s0</td>
</tr>
</table>
<p><b>Path Statistics:</b></p>
<table class="summary_table">
<tr>
<td class="label">Clock Skew</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Hold Relationship </td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Logic Level</td>
<td>2</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay</td>
<td>cell: 3.126, 67.413%; route: 1.511, 32.587%</td>
</tr>
<tr>
<td class="label">Arrival Data Path Delay</td>
<td>cell: 0.232, 41.810%; route: 0.122, 21.967%; tC2Q: 0.201, 36.223%</td>
</tr>
<tr>
<td class="label">Required Clock Path Delay</td>
<td>cell: 3.126, 67.413%; route: 1.511, 32.587%</td>
</tr>
</table>
<h3>Path21</h3>
<p><b>Path Summary:</b></p>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>0.546</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>5.194</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>4.649</td>
</tr>
<tr>
<td class="label">From</td>
<td>cntInst2/cnt_r_3_s0</td>
</tr>
<tr>
<td class="label">To</td>
<td>cntInst2/cnt_r_3_s0</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>clk_in:[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>clk_in:[R]</td>
</tr>
</table>
<p><b>Data Arrival Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>3.126</td>
<td>3.126</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>4.638</td>
<td>1.511</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C27[1][B]</td>
<td>cntInst2/cnt_r_3_s0/CLK</td>
</tr>
<tr>
<td>4.839</td>
<td>0.201</td>
<td>tC2Q</td>
<td>RF</td>
<td>2</td>
<td>R22C27[1][B]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_3_s0/Q</td>
</tr>
<tr>
<td>4.962</td>
<td>0.124</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>R22C27[1][B]</td>
<td>cntInst2/n36_s/I1</td>
</tr>
<tr>
<td>5.194</td>
<td>0.232</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>R22C27[1][B]</td>
<td style=" background: #97FFFF;">cntInst2/n36_s/SUM</td>
</tr>
<tr>
<td>5.194</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C27[1][B]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_3_s0/D</td>
</tr>
</table>
<p><b>Data Required Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>3.126</td>
<td>3.126</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>4.638</td>
<td>1.511</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C27[1][B]</td>
<td>cntInst2/cnt_r_3_s0/CLK</td>
</tr>
<tr>
<td>4.649</td>
<td>0.011</td>
<td>tHld</td>
<td></td>
<td>1</td>
<td>R22C27[1][B]</td>
<td>cntInst2/cnt_r_3_s0</td>
</tr>
</table>
<p><b>Path Statistics:</b></p>
<table class="summary_table">
<tr>
<td class="label">Clock Skew</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Hold Relationship </td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Logic Level</td>
<td>2</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay</td>
<td>cell: 3.126, 67.413%; route: 1.511, 32.587%</td>
</tr>
<tr>
<td class="label">Arrival Data Path Delay</td>
<td>cell: 0.232, 41.676%; route: 0.124, 22.216%; tC2Q: 0.201, 36.107%</td>
</tr>
<tr>
<td class="label">Required Clock Path Delay</td>
<td>cell: 3.126, 67.413%; route: 1.511, 32.587%</td>
</tr>
</table>
<h3>Path22</h3>
<p><b>Path Summary:</b></p>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>0.546</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>5.194</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>4.649</td>
</tr>
<tr>
<td class="label">From</td>
<td>cntInst2/cnt_r_11_s0</td>
</tr>
<tr>
<td class="label">To</td>
<td>cntInst2/cnt_r_11_s0</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>clk_in:[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>clk_in:[R]</td>
</tr>
</table>
<p><b>Data Arrival Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>3.126</td>
<td>3.126</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>4.638</td>
<td>1.511</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C28[2][B]</td>
<td>cntInst2/cnt_r_11_s0/CLK</td>
</tr>
<tr>
<td>4.839</td>
<td>0.201</td>
<td>tC2Q</td>
<td>RF</td>
<td>2</td>
<td>R22C28[2][B]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_11_s0/Q</td>
</tr>
<tr>
<td>4.962</td>
<td>0.124</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>R22C28[2][B]</td>
<td>cntInst2/n28_s/I1</td>
</tr>
<tr>
<td>5.194</td>
<td>0.232</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>R22C28[2][B]</td>
<td style=" background: #97FFFF;">cntInst2/n28_s/SUM</td>
</tr>
<tr>
<td>5.194</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C28[2][B]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_11_s0/D</td>
</tr>
</table>
<p><b>Data Required Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>3.126</td>
<td>3.126</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>4.638</td>
<td>1.511</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C28[2][B]</td>
<td>cntInst2/cnt_r_11_s0/CLK</td>
</tr>
<tr>
<td>4.649</td>
<td>0.011</td>
<td>tHld</td>
<td></td>
<td>1</td>
<td>R22C28[2][B]</td>
<td>cntInst2/cnt_r_11_s0</td>
</tr>
</table>
<p><b>Path Statistics:</b></p>
<table class="summary_table">
<tr>
<td class="label">Clock Skew</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Hold Relationship </td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Logic Level</td>
<td>2</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay</td>
<td>cell: 3.126, 67.413%; route: 1.511, 32.587%</td>
</tr>
<tr>
<td class="label">Arrival Data Path Delay</td>
<td>cell: 0.232, 41.676%; route: 0.124, 22.216%; tC2Q: 0.201, 36.107%</td>
</tr>
<tr>
<td class="label">Required Clock Path Delay</td>
<td>cell: 3.126, 67.413%; route: 1.511, 32.587%</td>
</tr>
</table>
<h3>Path23</h3>
<p><b>Path Summary:</b></p>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>0.546</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>5.194</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>4.649</td>
</tr>
<tr>
<td class="label">From</td>
<td>cntInst2/cnt_r_19_s0</td>
</tr>
<tr>
<td class="label">To</td>
<td>cntInst2/cnt_r_19_s0</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>clk_in:[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>clk_in:[R]</td>
</tr>
</table>
<p><b>Data Arrival Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>3.126</td>
<td>3.126</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>4.638</td>
<td>1.511</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C30[0][B]</td>
<td>cntInst2/cnt_r_19_s0/CLK</td>
</tr>
<tr>
<td>4.839</td>
<td>0.201</td>
<td>tC2Q</td>
<td>RF</td>
<td>2</td>
<td>R22C30[0][B]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_19_s0/Q</td>
</tr>
<tr>
<td>4.962</td>
<td>0.124</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>R22C30[0][B]</td>
<td>cntInst2/n20_s/I1</td>
</tr>
<tr>
<td>5.194</td>
<td>0.232</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>R22C30[0][B]</td>
<td style=" background: #97FFFF;">cntInst2/n20_s/SUM</td>
</tr>
<tr>
<td>5.194</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C30[0][B]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_19_s0/D</td>
</tr>
</table>
<p><b>Data Required Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>3.126</td>
<td>3.126</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>4.638</td>
<td>1.511</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C30[0][B]</td>
<td>cntInst2/cnt_r_19_s0/CLK</td>
</tr>
<tr>
<td>4.649</td>
<td>0.011</td>
<td>tHld</td>
<td></td>
<td>1</td>
<td>R22C30[0][B]</td>
<td>cntInst2/cnt_r_19_s0</td>
</tr>
</table>
<p><b>Path Statistics:</b></p>
<table class="summary_table">
<tr>
<td class="label">Clock Skew</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Hold Relationship </td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Logic Level</td>
<td>2</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay</td>
<td>cell: 3.126, 67.413%; route: 1.511, 32.587%</td>
</tr>
<tr>
<td class="label">Arrival Data Path Delay</td>
<td>cell: 0.232, 41.676%; route: 0.124, 22.216%; tC2Q: 0.201, 36.107%</td>
</tr>
<tr>
<td class="label">Required Clock Path Delay</td>
<td>cell: 3.126, 67.413%; route: 1.511, 32.587%</td>
</tr>
</table>
<h3>Path24</h3>
<p><b>Path Summary:</b></p>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>0.546</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>5.194</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>4.649</td>
</tr>
<tr>
<td class="label">From</td>
<td>cntInst2/cnt_r_22_s0</td>
</tr>
<tr>
<td class="label">To</td>
<td>cntInst2/cnt_r_22_s0</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>clk_in:[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>clk_in:[R]</td>
</tr>
</table>
<p><b>Data Arrival Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>3.126</td>
<td>3.126</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>4.638</td>
<td>1.511</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C30[2][A]</td>
<td>cntInst2/cnt_r_22_s0/CLK</td>
</tr>
<tr>
<td>4.839</td>
<td>0.201</td>
<td>tC2Q</td>
<td>RF</td>
<td>2</td>
<td>R22C30[2][A]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_22_s0/Q</td>
</tr>
<tr>
<td>4.962</td>
<td>0.124</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>R22C30[2][A]</td>
<td>cntInst2/n17_s/I1</td>
</tr>
<tr>
<td>5.194</td>
<td>0.232</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>R22C30[2][A]</td>
<td style=" background: #97FFFF;">cntInst2/n17_s/SUM</td>
</tr>
<tr>
<td>5.194</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C30[2][A]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_22_s0/D</td>
</tr>
</table>
<p><b>Data Required Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>3.126</td>
<td>3.126</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>4.638</td>
<td>1.511</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C30[2][A]</td>
<td>cntInst2/cnt_r_22_s0/CLK</td>
</tr>
<tr>
<td>4.649</td>
<td>0.011</td>
<td>tHld</td>
<td></td>
<td>1</td>
<td>R22C30[2][A]</td>
<td>cntInst2/cnt_r_22_s0</td>
</tr>
</table>
<p><b>Path Statistics:</b></p>
<table class="summary_table">
<tr>
<td class="label">Clock Skew</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Hold Relationship </td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Logic Level</td>
<td>2</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay</td>
<td>cell: 3.126, 67.413%; route: 1.511, 32.587%</td>
</tr>
<tr>
<td class="label">Arrival Data Path Delay</td>
<td>cell: 0.232, 41.676%; route: 0.124, 22.216%; tC2Q: 0.201, 36.107%</td>
</tr>
<tr>
<td class="label">Required Clock Path Delay</td>
<td>cell: 3.126, 67.413%; route: 1.511, 32.587%</td>
</tr>
</table>
<h3>Path25</h3>
<p><b>Path Summary:</b></p>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>0.546</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>5.194</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>4.649</td>
</tr>
<tr>
<td class="label">From</td>
<td>cntInst2/cnt_r_23_s0</td>
</tr>
<tr>
<td class="label">To</td>
<td>cntInst2/cnt_r_23_s0</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>clk_in:[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>clk_in:[R]</td>
</tr>
</table>
<p><b>Data Arrival Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>3.126</td>
<td>3.126</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>4.638</td>
<td>1.511</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C30[2][B]</td>
<td>cntInst2/cnt_r_23_s0/CLK</td>
</tr>
<tr>
<td>4.839</td>
<td>0.201</td>
<td>tC2Q</td>
<td>RF</td>
<td>2</td>
<td>R22C30[2][B]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_23_s0/Q</td>
</tr>
<tr>
<td>4.962</td>
<td>0.124</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>R22C30[2][B]</td>
<td>cntInst2/n16_s/I1</td>
</tr>
<tr>
<td>5.194</td>
<td>0.232</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>R22C30[2][B]</td>
<td style=" background: #97FFFF;">cntInst2/n16_s/SUM</td>
</tr>
<tr>
<td>5.194</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>R22C30[2][B]</td>
<td style=" font-weight:bold;">cntInst2/cnt_r_23_s0/D</td>
</tr>
</table>
<p><b>Data Required Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th class="label">FANOUT</th>
<th class="label">LOC</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>IOT27[A]</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>3.126</td>
<td>3.126</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>IOT27[A]</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>4.638</td>
<td>1.511</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>R22C30[2][B]</td>
<td>cntInst2/cnt_r_23_s0/CLK</td>
</tr>
<tr>
<td>4.649</td>
<td>0.011</td>
<td>tHld</td>
<td></td>
<td>1</td>
<td>R22C30[2][B]</td>
<td>cntInst2/cnt_r_23_s0</td>
</tr>
</table>
<p><b>Path Statistics:</b></p>
<table class="summary_table">
<tr>
<td class="label">Clock Skew</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Hold Relationship </td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Logic Level</td>
<td>2</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay</td>
<td>cell: 3.126, 67.413%; route: 1.511, 32.587%</td>
</tr>
<tr>
<td class="label">Arrival Data Path Delay</td>
<td>cell: 0.232, 41.676%; route: 0.124, 22.216%; tC2Q: 0.201, 36.107%</td>
</tr>
<tr>
<td class="label">Required Clock Path Delay</td>
<td>cell: 3.126, 67.413%; route: 1.511, 32.587%</td>
</tr>
</table>
<h3><a name="Recovery_Analysis">Recovery Analysis Report</a></h3>
<h4>Report Command:report_timing -recovery -from_clock [get_clocks {clk_in}] -to_clock [get_clocks {clk_in}] -max_paths 25 -max_common_paths 1</h4>
<h4>No recovery paths to report!</h4>
<h3><a name="Removal_Analysis">Removal Analysis Report</a></h3>
<h4>Report Command:report_timing -removal -from_clock [get_clocks {clk_in}] -to_clock [get_clocks {clk_in}] -max_paths 25 -max_common_paths 1</h4>
<h4>No removal paths to report!</h4>
<h2><a name="Minimum_Pulse_Width_Report">Minimum Pulse Width Report:</a></h2>
<h4>Report Command:report_min_pulse_width -nworst 10 -detail</h4>
<h3>MPW1</h3>
<p><b>MPW Summary:</b></p>
<table class="summary_table">
<tr>
<td class="label">Slack:</td>
<td>2.966</td>
</tr>
<tr>
<td class="label">Actual Width:</td>
<td>3.966</td>
</tr>
<tr>
<td class="label">Required Width:</td>
<td>1.000</td>
</tr>
<tr>
<td class="label">Type:</td>
<td>Low Pulse Width</td>
</tr>
<tr>
<td class="label">Clock:</td>
<td>myclk</td>
</tr>
<tr>
<td class="label">Objects:</td>
<td>outCrt/cnt_r_1_s0</td>
</tr>
</table>
<p><b>Late clock Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th>NODE</th>
</tr>
<tr>
<td>5.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>5.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td>myclk</td>
</tr>
<tr>
<td>5.000</td>
<td>0.000</td>
<td>tCL</td>
<td>FF</td>
<td>cntInst2/clko_s2/Q</td>
</tr>
<tr>
<td>7.928</td>
<td>2.928</td>
<td>tNET</td>
<td>FF</td>
<td>outCrt/cnt_r_1_s0/CLK</td>
</tr>
</table>
<p><b>Early clock Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th>NODE</th>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td>myclk</td>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>cntInst2/clko_s2/Q</td>
</tr>
<tr>
<td>11.894</td>
<td>1.894</td>
<td>tNET</td>
<td>RR</td>
<td>outCrt/cnt_r_1_s0/CLK</td>
</tr>
</table>
<h3>MPW2</h3>
<p><b>MPW Summary:</b></p>
<table class="summary_table">
<tr>
<td class="label">Slack:</td>
<td>2.966</td>
</tr>
<tr>
<td class="label">Actual Width:</td>
<td>3.966</td>
</tr>
<tr>
<td class="label">Required Width:</td>
<td>1.000</td>
</tr>
<tr>
<td class="label">Type:</td>
<td>Low Pulse Width</td>
</tr>
<tr>
<td class="label">Clock:</td>
<td>myclk</td>
</tr>
<tr>
<td class="label">Objects:</td>
<td>outCrt/cnt_r_0_s0</td>
</tr>
</table>
<p><b>Late clock Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th>NODE</th>
</tr>
<tr>
<td>5.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>5.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td>myclk</td>
</tr>
<tr>
<td>5.000</td>
<td>0.000</td>
<td>tCL</td>
<td>FF</td>
<td>cntInst2/clko_s2/Q</td>
</tr>
<tr>
<td>7.928</td>
<td>2.928</td>
<td>tNET</td>
<td>FF</td>
<td>outCrt/cnt_r_0_s0/CLK</td>
</tr>
</table>
<p><b>Early clock Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th>NODE</th>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td>myclk</td>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>cntInst2/clko_s2/Q</td>
</tr>
<tr>
<td>11.894</td>
<td>1.894</td>
<td>tNET</td>
<td>RR</td>
<td>outCrt/cnt_r_0_s0/CLK</td>
</tr>
</table>
<h3>MPW3</h3>
<p><b>MPW Summary:</b></p>
<table class="summary_table">
<tr>
<td class="label">Slack:</td>
<td>2.966</td>
</tr>
<tr>
<td class="label">Actual Width:</td>
<td>3.966</td>
</tr>
<tr>
<td class="label">Required Width:</td>
<td>1.000</td>
</tr>
<tr>
<td class="label">Type:</td>
<td>Low Pulse Width</td>
</tr>
<tr>
<td class="label">Clock:</td>
<td>myclk</td>
</tr>
<tr>
<td class="label">Objects:</td>
<td>outCrt/cnt_r_2_s0</td>
</tr>
</table>
<p><b>Late clock Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th>NODE</th>
</tr>
<tr>
<td>5.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>5.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td>myclk</td>
</tr>
<tr>
<td>5.000</td>
<td>0.000</td>
<td>tCL</td>
<td>FF</td>
<td>cntInst2/clko_s2/Q</td>
</tr>
<tr>
<td>7.928</td>
<td>2.928</td>
<td>tNET</td>
<td>FF</td>
<td>outCrt/cnt_r_2_s0/CLK</td>
</tr>
</table>
<p><b>Early clock Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th>NODE</th>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td>myclk</td>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>cntInst2/clko_s2/Q</td>
</tr>
<tr>
<td>11.894</td>
<td>1.894</td>
<td>tNET</td>
<td>RR</td>
<td>outCrt/cnt_r_2_s0/CLK</td>
</tr>
</table>
<h3>MPW4</h3>
<p><b>MPW Summary:</b></p>
<table class="summary_table">
<tr>
<td class="label">Slack:</td>
<td>3.122</td>
</tr>
<tr>
<td class="label">Actual Width:</td>
<td>4.122</td>
</tr>
<tr>
<td class="label">Required Width:</td>
<td>1.000</td>
</tr>
<tr>
<td class="label">Type:</td>
<td>High Pulse Width</td>
</tr>
<tr>
<td class="label">Clock:</td>
<td>myclk</td>
</tr>
<tr>
<td class="label">Objects:</td>
<td>outCrt/cnt_r_1_s0</td>
</tr>
</table>
<p><b>Late clock Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td>myclk</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>cntInst2/clko_s2/Q</td>
</tr>
<tr>
<td>2.811</td>
<td>2.811</td>
<td>tNET</td>
<td>RR</td>
<td>outCrt/cnt_r_1_s0/CLK</td>
</tr>
</table>
<p><b>Early clock Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th>NODE</th>
</tr>
<tr>
<td>5.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>5.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td>myclk</td>
</tr>
<tr>
<td>5.000</td>
<td>0.000</td>
<td>tCL</td>
<td>FF</td>
<td>cntInst2/clko_s2/Q</td>
</tr>
<tr>
<td>6.933</td>
<td>1.933</td>
<td>tNET</td>
<td>FF</td>
<td>outCrt/cnt_r_1_s0/CLK</td>
</tr>
</table>
<h3>MPW5</h3>
<p><b>MPW Summary:</b></p>
<table class="summary_table">
<tr>
<td class="label">Slack:</td>
<td>3.122</td>
</tr>
<tr>
<td class="label">Actual Width:</td>
<td>4.122</td>
</tr>
<tr>
<td class="label">Required Width:</td>
<td>1.000</td>
</tr>
<tr>
<td class="label">Type:</td>
<td>High Pulse Width</td>
</tr>
<tr>
<td class="label">Clock:</td>
<td>myclk</td>
</tr>
<tr>
<td class="label">Objects:</td>
<td>outCrt/cnt_r_0_s0</td>
</tr>
</table>
<p><b>Late clock Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td>myclk</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>cntInst2/clko_s2/Q</td>
</tr>
<tr>
<td>2.811</td>
<td>2.811</td>
<td>tNET</td>
<td>RR</td>
<td>outCrt/cnt_r_0_s0/CLK</td>
</tr>
</table>
<p><b>Early clock Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th>NODE</th>
</tr>
<tr>
<td>5.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>5.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td>myclk</td>
</tr>
<tr>
<td>5.000</td>
<td>0.000</td>
<td>tCL</td>
<td>FF</td>
<td>cntInst2/clko_s2/Q</td>
</tr>
<tr>
<td>6.933</td>
<td>1.933</td>
<td>tNET</td>
<td>FF</td>
<td>outCrt/cnt_r_0_s0/CLK</td>
</tr>
</table>
<h3>MPW6</h3>
<p><b>MPW Summary:</b></p>
<table class="summary_table">
<tr>
<td class="label">Slack:</td>
<td>3.122</td>
</tr>
<tr>
<td class="label">Actual Width:</td>
<td>4.122</td>
</tr>
<tr>
<td class="label">Required Width:</td>
<td>1.000</td>
</tr>
<tr>
<td class="label">Type:</td>
<td>High Pulse Width</td>
</tr>
<tr>
<td class="label">Clock:</td>
<td>myclk</td>
</tr>
<tr>
<td class="label">Objects:</td>
<td>outCrt/cnt_r_2_s0</td>
</tr>
</table>
<p><b>Late clock Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td>myclk</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>cntInst2/clko_s2/Q</td>
</tr>
<tr>
<td>2.811</td>
<td>2.811</td>
<td>tNET</td>
<td>RR</td>
<td>outCrt/cnt_r_2_s0/CLK</td>
</tr>
</table>
<p><b>Early clock Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th>NODE</th>
</tr>
<tr>
<td>5.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>5.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td>myclk</td>
</tr>
<tr>
<td>5.000</td>
<td>0.000</td>
<td>tCL</td>
<td>FF</td>
<td>cntInst2/clko_s2/Q</td>
</tr>
<tr>
<td>6.933</td>
<td>1.933</td>
<td>tNET</td>
<td>FF</td>
<td>outCrt/cnt_r_2_s0/CLK</td>
</tr>
</table>
<h3>MPW7</h3>
<p><b>MPW Summary:</b></p>
<table class="summary_table">
<tr>
<td class="label">Slack:</td>
<td>6.152</td>
</tr>
<tr>
<td class="label">Actual Width:</td>
<td>7.152</td>
</tr>
<tr>
<td class="label">Required Width:</td>
<td>1.000</td>
</tr>
<tr>
<td class="label">Type:</td>
<td>High Pulse Width</td>
</tr>
<tr>
<td class="label">Clock:</td>
<td>clk_in</td>
</tr>
<tr>
<td class="label">Objects:</td>
<td>cntInst2/cnt_r_29_s0</td>
</tr>
</table>
<p><b>Late clock Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>4.230</td>
<td>4.230</td>
<td>tINS</td>
<td>RR</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>6.501</td>
<td>2.271</td>
<td>tNET</td>
<td>RR</td>
<td>cntInst2/cnt_r_29_s0/CLK</td>
</tr>
</table>
<p><b>Early clock Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th>NODE</th>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td>tCL</td>
<td>FF</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>12.140</td>
<td>2.140</td>
<td>tINS</td>
<td>FF</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>13.653</td>
<td>1.514</td>
<td>tNET</td>
<td>FF</td>
<td>cntInst2/cnt_r_29_s0/CLK</td>
</tr>
</table>
<h3>MPW8</h3>
<p><b>MPW Summary:</b></p>
<table class="summary_table">
<tr>
<td class="label">Slack:</td>
<td>6.152</td>
</tr>
<tr>
<td class="label">Actual Width:</td>
<td>7.152</td>
</tr>
<tr>
<td class="label">Required Width:</td>
<td>1.000</td>
</tr>
<tr>
<td class="label">Type:</td>
<td>High Pulse Width</td>
</tr>
<tr>
<td class="label">Clock:</td>
<td>clk_in</td>
</tr>
<tr>
<td class="label">Objects:</td>
<td>cntInst2/cnt_r_27_s0</td>
</tr>
</table>
<p><b>Late clock Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>4.230</td>
<td>4.230</td>
<td>tINS</td>
<td>RR</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>6.501</td>
<td>2.271</td>
<td>tNET</td>
<td>RR</td>
<td>cntInst2/cnt_r_27_s0/CLK</td>
</tr>
</table>
<p><b>Early clock Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th>NODE</th>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td>tCL</td>
<td>FF</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>12.140</td>
<td>2.140</td>
<td>tINS</td>
<td>FF</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>13.653</td>
<td>1.514</td>
<td>tNET</td>
<td>FF</td>
<td>cntInst2/cnt_r_27_s0/CLK</td>
</tr>
</table>
<h3>MPW9</h3>
<p><b>MPW Summary:</b></p>
<table class="summary_table">
<tr>
<td class="label">Slack:</td>
<td>6.152</td>
</tr>
<tr>
<td class="label">Actual Width:</td>
<td>7.152</td>
</tr>
<tr>
<td class="label">Required Width:</td>
<td>1.000</td>
</tr>
<tr>
<td class="label">Type:</td>
<td>High Pulse Width</td>
</tr>
<tr>
<td class="label">Clock:</td>
<td>clk_in</td>
</tr>
<tr>
<td class="label">Objects:</td>
<td>cntInst2/cnt_r_23_s0</td>
</tr>
</table>
<p><b>Late clock Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>4.230</td>
<td>4.230</td>
<td>tINS</td>
<td>RR</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>6.501</td>
<td>2.271</td>
<td>tNET</td>
<td>RR</td>
<td>cntInst2/cnt_r_23_s0/CLK</td>
</tr>
</table>
<p><b>Early clock Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th>NODE</th>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td>tCL</td>
<td>FF</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>12.140</td>
<td>2.140</td>
<td>tINS</td>
<td>FF</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>13.653</td>
<td>1.514</td>
<td>tNET</td>
<td>FF</td>
<td>cntInst2/cnt_r_23_s0/CLK</td>
</tr>
</table>
<h3>MPW10</h3>
<p><b>MPW Summary:</b></p>
<table class="summary_table">
<tr>
<td class="label">Slack:</td>
<td>6.152</td>
</tr>
<tr>
<td class="label">Actual Width:</td>
<td>7.152</td>
</tr>
<tr>
<td class="label">Required Width:</td>
<td>1.000</td>
</tr>
<tr>
<td class="label">Type:</td>
<td>High Pulse Width</td>
</tr>
<tr>
<td class="label">Clock:</td>
<td>clk_in</td>
</tr>
<tr>
<td class="label">Objects:</td>
<td>cntInst2/cnt_r_15_s0</td>
</tr>
</table>
<p><b>Late clock Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>4.230</td>
<td>4.230</td>
<td>tINS</td>
<td>RR</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>6.501</td>
<td>2.271</td>
<td>tNET</td>
<td>RR</td>
<td>cntInst2/cnt_r_15_s0/CLK</td>
</tr>
</table>
<p><b>Early clock Path:</b></p>
<table class="detail_table">
<tr>
<th class="label">AT</th>
<th class="label">DELAY</th>
<th class="label">TYPE</th>
<th class="label">RF</th>
<th>NODE</th>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td>active clock edge time</td>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td></td>
<td></td>
<td>clk_in</td>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td>tCL</td>
<td>FF</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>12.140</td>
<td>2.140</td>
<td>tINS</td>
<td>FF</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>13.653</td>
<td>1.514</td>
<td>tNET</td>
<td>FF</td>
<td>cntInst2/cnt_r_15_s0/CLK</td>
</tr>
</table>
<h2><a name="High_Fanout_Nets_Report">High Fanout Nets Report:</a></h2>
<h4>Report Command:report_high_fanout_nets -max_nets 10</h4>
<table class="detail_table">
<tr>
<th class="label">FANOUT</th>
<th class="label">NET NAME</th>
<th class="label">WORST SLACK</th>
<th class="label">MAX DELAY</th>
</tr>
<tr>
<td>393</td>
<td>out_d_11[35]</td>
<td>-0.541</td>
<td>2.442</td>
</tr>
<tr>
<td>128</td>
<td>myfft/fft_top_inst/qxnb_do_re_0_40</td>
<td>91.602</td>
<td>1.625</td>
</tr>
<tr>
<td>108</td>
<td>myfft/fft_top_inst/dSrc</td>
<td>92.137</td>
<td>1.544</td>
</tr>
<tr>
<td>84</td>
<td>myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1</td>
<td>92.887</td>
<td>1.086</td>
</tr>
<tr>
<td>56</td>
<td>myfft/fft_top_inst/ftCtl_ea</td>
<td>93.724</td>
<td>1.330</td>
</tr>
<tr>
<td>55</td>
<td>myfft/fft_top_inst/tf_inst/ctrl_inst/slfRst_2</td>
<td>92.297</td>
<td>1.716</td>
</tr>
<tr>
<td>47</td>
<td>out_d_12[30]</td>
<td>96.005</td>
<td>2.357</td>
</tr>
<tr>
<td>44</td>
<td>myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n148_7</td>
<td>92.355</td>
<td>1.306</td>
</tr>
<tr>
<td>44</td>
<td>myfft/fft_top_inst/isLdxn</td>
<td>96.028</td>
<td>1.768</td>
</tr>
<tr>
<td>38</td>
<td>myfft/fft_top_inst/tf_inst/slfRst_3</td>
<td>92.297</td>
<td>1.199</td>
</tr>
</table>
<h2><a name="Route_Congestions_Report">Route Congestions Report:</a></h2>
<h4>Report Command:report_route_congestion -max_grids 10</h4>
<table class="detail_table">
<tr>
<th class="label">GRID LOC</th>
<th class="label">ROUTE CONGESTIONS</th>
</tr>
<tr>
<td>R28C20</td>
<td>54.17%</td>
</tr>
<tr>
<td>R27C30</td>
<td>52.78%</td>
</tr>
<tr>
<td>R29C26</td>
<td>50.00%</td>
</tr>
<tr>
<td>R25C30</td>
<td>50.00%</td>
</tr>
<tr>
<td>R27C32</td>
<td>47.22%</td>
</tr>
<tr>
<td>R27C28</td>
<td>45.83%</td>
</tr>
<tr>
<td>R27C20</td>
<td>45.83%</td>
</tr>
<tr>
<td>R28C19</td>
<td>44.44%</td>
</tr>
<tr>
<td>R25C29</td>
<td>44.44%</td>
</tr>
<tr>
<td>R28C36</td>
<td>43.06%</td>
</tr>
</table>
<h2><a name="Timing_Exceptions_Report">Timing Exceptions Report:</a></h2>
<h3><a name="Setup_Analysis_Exceptions">Setup Analysis Report</a></h3>
<h4>Report Command:report_exceptions -setup -max_paths 5 -max_common_paths 1</h4>
<h4>No timing exceptions to report!</h4>
<h3><a name="Hold_Analysis_Exceptions">Hold Analysis Report</a></h3>
<h4>Report Command:report_exceptions -hold -max_paths 5 -max_common_paths 1</h4>
<h4>No timing exceptions to report!</h4>
<h3><a name="Recovery_Analysis_Exceptions">Recovery Analysis Report</a></h3>
<h4>Report Command:report_exceptions -recovery -max_paths 5 -max_common_paths 1</h4>
<h4>No timing exceptions to report!</h4>
<h3><a name="Removal_Analysis_Exceptions">Removal Analysis Report</a></h3>
<h4>Report Command:report_exceptions -removal -max_paths 5 -max_common_paths 1</h4>
<h4>No timing exceptions to report!</h4>
<h2><a name="SDC_Report">Timing Constraints Report:</a></h2>
<table class="detail_table">
<tr>
<th class="label">SDC Command Type</th>
<th class="label">State</th>
<th class="label">Detail Command</th>
</tr>
<tr>
<td>TC_CLOCK</td>
<td>Actived</td>
<td>create_clock -name clk_in -period 20 -waveform {0 10} [get_ports {clk}]</td>
</tr>
<tr>
<td>TC_CLOCK</td>
<td>Actived</td>
<td>create_clock -name myclk -period 10 -waveform {0 5} [get_nets {sysClk}]</td>
</tr>
<tr>
<td>TC_REPORT_TIMING</td>
<td>Actived</td>
<td>report_timing -setup -from_clock [get_clocks {clk_in}] -to_clock [get_clocks {clk_in}] -max_paths 25 -max_common_paths 1</td>
</tr>
<tr>
<td>TC_REPORT_TIMING</td>
<td>Actived</td>
<td>report_timing -hold -from_clock [get_clocks {clk_in}] -to_clock [get_clocks {clk_in}] -max_paths 25 -max_common_paths 1</td>
</tr>
<tr>
<td>TC_REPORT_TIMING</td>
<td>Actived</td>
<td>report_timing -recovery -from_clock [get_clocks {clk_in}] -to_clock [get_clocks {clk_in}] -max_paths 25 -max_common_paths 1</td>
</tr>
<tr>
<td>TC_REPORT_TIMING</td>
<td>Actived</td>
<td>report_timing -removal -from_clock [get_clocks {clk_in}] -to_clock [get_clocks {clk_in}] -max_paths 25 -max_common_paths 1</td>
</tr>
</table>
</div><!-- content -->
</body>
</html>
