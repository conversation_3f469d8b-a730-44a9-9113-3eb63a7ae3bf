parameter DATA_OUT_SHIFT_WIDTH = 33;
parameter MEM_DATA_WIDTH = 33;
parameter SIG_PORT_WIDTH = 32;
parameter CAPTURE_DATA = 1;
parameter TRIG_OUT_EN = 0;
parameter MAX_TRIG_LEVELS = 0;
parameter NUM_TRIG_PORTS = 5;
parameter STORAGE_DEPTH = 4096;
parameter TRIG_TYPE = 0;
parameter CAPTURE_TRIG_0 = 1;
parameter TRIG_PORT_WIDTH_0 = 2;
parameter CAPTURE_TRIG_1 = 1;
parameter TRIG_PORT_WIDTH_1 = 13;
parameter CAPTURE_TRIG_2 = 1;
parameter TRIG_PORT_WIDTH_2 = 16;
parameter CAPTURE_TRIG_3 = 1;
parameter TRIG_PORT_WIDTH_3 = 1;
parameter CAPTURE_TRIG_4 = 1;
parameter TRIG_PORT_WIDTH_4 = 1;
parameter CAPTURE_TRIG_5 = 1;
parameter TRIG_PORT_WIDTH_5 = 8;
parameter CAPTURE_TRIG_6 = 1;
parameter TRIG_PORT_WIDTH_6 = 8;
parameter CAPTURE_TRIG_7 = 1;
parameter TRIG_PORT_WIDTH_7 = 8;
parameter CAPTURE_TRIG_8 = 1;
parameter TRIG_PORT_WIDTH_8 = 8;
parameter CAPTURE_TRIG_9 = 1;
parameter TRIG_PORT_WIDTH_9 = 8;
parameter CAPTURE_TRIG_10 = 1;
parameter TRIG_PORT_WIDTH_10 = 8;
parameter CAPTURE_TRIG_11 = 1;
parameter TRIG_PORT_WIDTH_11 = 8;
parameter CAPTURE_TRIG_12 = 1;
parameter TRIG_PORT_WIDTH_12 = 8;
parameter CAPTURE_TRIG_13 = 1;
parameter TRIG_PORT_WIDTH_13 = 8;
parameter CAPTURE_TRIG_14 = 1;
parameter TRIG_PORT_WIDTH_14 = 8;
parameter CAPTURE_TRIG_15 = 1;
parameter TRIG_PORT_WIDTH_15 = 8;
parameter MATCH_EN_0 = 1;
parameter MATCH_CNT_WIDTH_0 = 0;
parameter MATCH_TYPE_0 = 3'b001;
parameter MATCHER_0 = 4'b0000;
parameter MATCH_EN_1 = 0;
parameter MATCH_CNT_WIDTH_1 = 8;
parameter MATCH_TYPE_1 = 3'b000;
parameter MATCHER_1 = 4'b0000;
parameter MATCH_EN_2 = 0;
parameter MATCH_CNT_WIDTH_2 = 8;
parameter MATCH_TYPE_2 = 3'b000;
parameter MATCHER_2 = 4'b0000;
parameter MATCH_EN_3 = 0;
parameter MATCH_CNT_WIDTH_3 = 8;
parameter MATCH_TYPE_3 = 3'b000;
parameter MATCHER_3 = 4'b0000;
parameter MATCH_EN_4 = 0;
parameter MATCH_CNT_WIDTH_4 = 8;
parameter MATCH_TYPE_4 = 3'b000;
parameter MATCHER_4 = 4'b0000;
parameter MATCH_EN_5 = 0;
parameter MATCH_CNT_WIDTH_5 = 8;
parameter MATCH_TYPE_5 = 3'b000;
parameter MATCHER_5 = 4'b0000;
parameter MATCH_EN_6 = 0;
parameter MATCH_CNT_WIDTH_6 = 8;
parameter MATCH_TYPE_6 = 3'b000;
parameter MATCHER_6 = 4'b0000;
parameter MATCH_EN_7 = 0;
parameter MATCH_CNT_WIDTH_7 = 8;
parameter MATCH_TYPE_7 = 3'b000;
parameter MATCHER_7 = 4'b0000;
parameter MATCH_EN_8 = 0;
parameter MATCH_CNT_WIDTH_8 = 8;
parameter MATCH_TYPE_8 = 3'b000;
parameter MATCHER_8 = 4'b0000;
parameter MATCH_EN_9 = 0;
parameter MATCH_CNT_WIDTH_9 = 8;
parameter MATCH_TYPE_9 = 3'b000;
parameter MATCHER_9 = 4'b0000;
parameter MATCH_EN_10 = 0;
parameter MATCH_CNT_WIDTH_10 = 8;
parameter MATCH_TYPE_10 = 3'b000;
parameter MATCHER_10 = 4'b0000;
parameter MATCH_EN_11 = 0;
parameter MATCH_CNT_WIDTH_11 = 8;
parameter MATCH_TYPE_11 = 3'b000;
parameter MATCHER_11 = 4'b0000;
parameter MATCH_EN_12 = 0;
parameter MATCH_CNT_WIDTH_12 = 8;
parameter MATCH_TYPE_12 = 3'b000;
parameter MATCHER_12 = 4'b0000;
parameter MATCH_EN_13 = 0;
parameter MATCH_CNT_WIDTH_13 = 8;
parameter MATCH_TYPE_13 = 3'b000;
parameter MATCHER_13 = 4'b0000;
parameter MATCH_EN_14 = 0;
parameter MATCH_CNT_WIDTH_14 = 8;
parameter MATCH_TYPE_14 = 3'b000;
parameter MATCHER_14 = 4'b0000;
parameter MATCH_EN_15 = 0;
parameter MATCH_CNT_WIDTH_15 = 8;
parameter MATCH_TYPE_15 = 3'b000;
parameter MATCHER_15 = 4'b0000;
