<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE gowin-synthesis-project>
<Project>
    <Version>beta</Version>
    <Device id="GW2A-18C" package="PBGA256" speed="8" partNumber="GW2A-LV18PG256C8/I7"/>
    <FileList>
        <File path="G:/Gowin/Gowin_V1.9.11.03_Education_x64_win/Gowin/Gowin_V1.9.11.03_Education_x64/IDE/data/ipcores/GAO/GW_AO_0/gw_ao_crc32.v" type="verilog"/>
        <File path="G:/Gowin/Gowin_V1.9.11.03_Education_x64_win/Gowin/Gowin_V1.9.11.03_Education_x64/IDE/data/ipcores/GAO/GW_AO_0/gw_ao_match.v" type="verilog"/>
        <File path="G:/Gowin/Gowin_V1.9.11.03_Education_x64_win/Gowin/Gowin_V1.9.11.03_Education_x64/IDE/data/ipcores/GAO/GW_AO_0/gw_ao_mem_ctrl.v" type="verilog"/>
        <File path="G:/Gowin/Gowin_V1.9.11.03_Education_x64_win/Gowin/Gowin_V1.9.11.03_Education_x64/IDE/data/ipcores/GAO/GW_AO_0/gw_ao_top.v" type="verilog"/>
        <File path="G:/Gowin/Gowin_V1.9.11.03_Education_x64_win/Gowin/Gowin_V1.9.11.03_Education_x64/IDE/data/ipcores/GAO/GW_CON/gw_con_top.v" type="verilog"/>
        <File path="G:/Gowin/Gowin_V1.9.11.03_Education_x64_win/Gowin/Gowin_V1.9.11.03_Education_x64/IDE/data/ipcores/gw_jtag.v" type="verilog"/>
        <File path="G:/Gowin/workspace/TangPrimer-20K-example-main/Litex/sipeed_tang_primer_20k/Documents/FFT/impl/gao/gw_gao_top.v" type="verilog"/>
    </FileList>
    <OptionList>
        <Option type="disable_insert_pad" value="1"/>
        <Option type="include_path" value="G:/Gowin/Gowin_V1.9.11.03_Education_x64_win/Gowin/Gowin_V1.9.11.03_Education_x64/IDE/data/ipcores/GAO"/>
        <Option type="include_path" value="G:/Gowin/workspace/TangPrimer-20K-example-main/Litex/sipeed_tang_primer_20k/Documents/FFT/impl/gao"/>
        <Option type="output_file" value="G:/Gowin/workspace/TangPrimer-20K-example-main/Litex/sipeed_tang_primer_20k/Documents/FFT/impl/gao/gao.v"/>
    </OptionList>
</Project>
