
IO_LOC "out[37]" B1;
IO_PORT "out[37]" PULL_MODE=UP DRIVE=8;
IO_LOC "out[36]" C2;
IO_PORT "out[36]" PULL_MODE=UP DRIVE=8;
IO_LOC "out[35]" C1;
IO_PORT "out[35]" PULL_MODE=UP DRIVE=8;
IO_LOC "out[34]" D3;
IO_PORT "out[34]" PULL_MODE=UP DRIVE=8;
IO_LOC "out[33]" D1;
IO_PORT "out[33]" PULL_MODE=UP DRIVE=8;
IO_LOC "out[32]" E3;
IO_PORT "out[32]" PULL_MODE=UP DRIVE=8;
IO_LOC "out[31]" E1;
IO_PORT "out[31]" PULL_MODE=UP DRIVE=8;
IO_LOC "out[30]" F1;
IO_PORT "out[30]" PULL_MODE=UP DRIVE=8;
IO_LOC "out[29]" F2;
IO_PORT "out[29]" PULL_MODE=UP DRIVE=8;
IO_LOC "out[28]" F3;
IO_PORT "out[28]" PULL_MODE=UP DRIVE=8;
IO_LOC "out[27]" G3;
IO_PORT "out[27]" PULL_MODE=UP DRIVE=8;
IO_LOC "out[26]" G1;
IO_PORT "out[26]" PULL_MODE=UP DRIVE=8;
IO_LOC "out[25]" G2;
IO_PORT "out[25]" PULL_MODE=UP DRIVE=8;
IO_LOC "out[24]" H1;
IO_PORT "out[24]" PULL_MODE=UP DRIVE=8;
IO_LOC "out[23]" H2;
IO_PORT "out[23]" PULL_MODE=UP DRIVE=8;
IO_LOC "out[22]" J1;
IO_PORT "out[22]" PULL_MODE=UP DRIVE=8;
IO_LOC "out[21]" K1;
IO_PORT "out[21]" PULL_MODE=UP DRIVE=8;
IO_LOC "out[20]" K3;
IO_PORT "out[20]" PULL_MODE=UP DRIVE=8;
IO_LOC "out[19]" L3;
IO_PORT "out[19]" PULL_MODE=UP DRIVE=8;
IO_LOC "out[18]" L1;
IO_PORT "out[18]" PULL_MODE=UP DRIVE=8;
IO_LOC "out[17]" M1;
IO_PORT "out[17]" PULL_MODE=UP DRIVE=8;
IO_LOC "out[16]" N1;
IO_PORT "out[16]" PULL_MODE=UP DRIVE=8;
IO_LOC "out[15]" P1;
IO_PORT "out[15]" PULL_MODE=UP DRIVE=8;
IO_LOC "out[14]" R1;
IO_PORT "out[14]" PULL_MODE=UP DRIVE=8;
IO_LOC "led[9]" L8;
IO_PORT "led[9]" PULL_MODE=UP DRIVE=8;
IO_LOC "led[7]" A11;
IO_PORT "led[7]" PULL_MODE=UP DRIVE=8;
IO_LOC "led[6]" B11;
IO_PORT "led[6]" PULL_MODE=UP DRIVE=8;
IO_LOC "led[5]" C8;
IO_PORT "led[5]" PULL_MODE=UP DRIVE=8;
IO_LOC "led[4]" F9;
IO_PORT "led[4]" PULL_MODE=UP DRIVE=8;
IO_LOC "led[3]" R8;
IO_PORT "led[3]" PULL_MODE=UP DRIVE=8;
IO_LOC "clk" H11;
IO_PORT "clk" PULL_MODE=UP;
