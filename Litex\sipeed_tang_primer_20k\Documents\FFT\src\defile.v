`define MODULE_NAME MOD_FFT
`define TARGET_DEVICE_GW2A_55
`define FIXED
`define FFT1024
`define POINTS 1024
`define POINTS_LOG 10
`define LOW_RESOURCE
`define MODE_FORWARD
`define OUTPUT_NATURAL
`define SCALING_RS111
`define INPUT_DATA_WIDTH 16
`define INPUT_DATA_WIDTH_LOG 4
`define TWIDDLE_FACTOR_WIDTH 16
`define TWIDDLE_FACTOR_WIDTH_LOG 4
`define OUTPUT_DATA_WIDTH 16
`define OUTPUT_DATA_WIDTH_LOG 4
`define REDUCTION_TRUNCATION
`define DSP_BLOCK_BASED
`define EBR_MEMORY
`define TDF_EBR_MEMORY
