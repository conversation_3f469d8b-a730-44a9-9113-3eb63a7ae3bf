//=============================================================================
// 正弦波生成器顶层模块
// 功能：使用CORDIC算法生成正弦波，通过PWM输出
// 目标：Tang Primer 20K (GW2A-18C)
// 作者：AI Assistant
// 日期：2025-07-30
//=============================================================================

module sine_wave_generator (
    // 系统信号
    input  wire        clk,           // 系统时钟 (27MHz)
    input  wire        rst_n,         // 复位信号 (低有效)
    
    // 控制信号
    input  wire [3:0]  freq_div,      // 频率分频系数 (4位)
    input  wire        enable,        // 使能信号
    
    // 输出信号
    output wire        pwm_out,       // PWM输出
    output wire        sine_valid,    // 正弦值有效信号
    output wire [7:0]  debug_duty     // 调试用占空比输出
);

//=============================================================================
// 参数定义
//=============================================================================
localparam ANGLE_WIDTH = 17;
localparam DATA_WIDTH  = 17;
localparam PWM_WIDTH   = 8;

// CORDIC相关常数
localparam CORDIC_GAIN = 17'h09B74;  // CORDIC增益补偿 (约0.607)
localparam ANGLE_STEP  = 17'h0648;   // 角度步进 (约2π/1024)

//=============================================================================
// 内部信号定义
//=============================================================================
// 时钟分频
reg [15:0] clk_div_cnt;
reg        clk_div;
wire [15:0] freq_div_extended;

// 角度生成器
reg [16:0] angle_acc;
reg [16:0] current_angle;

// CORDIC接口信号
wire [16:0] cordic_x_i;
wire [16:0] cordic_y_i;
wire [16:0] cordic_theta_i;
wire [16:0] cordic_x_o;
wire [16:0] cordic_y_o;
wire [16:0] cordic_theta_o;

// 数据处理
reg  [16:0] sine_raw;
reg  [7:0]  sine_scaled;
reg         sine_valid_reg;

// PWM接口信号
wire        pwm_clk;
wire [7:0]  pwm_duty;
wire        pwm_en;

//=============================================================================
// 频率控制扩展和时钟分频器
//=============================================================================
// 将4位频率控制扩展为16位，增加可调范围
assign freq_div_extended = {8'h00, freq_div, 4'h0} + 16'd15;  // 15-255范围

always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        clk_div_cnt <= 16'h0;
        clk_div     <= 1'b0;
    end else if (enable) begin
        if (clk_div_cnt >= freq_div_extended) begin
            clk_div_cnt <= 16'h0;
            clk_div     <= ~clk_div;
        end else begin
            clk_div_cnt <= clk_div_cnt + 1'b1;
        end
    end
end

//=============================================================================
// 角度累加器 - 生成连续的角度值
//=============================================================================
always @(posedge clk_div or negedge rst_n) begin
    if (!rst_n) begin
        angle_acc     <= 17'h0;
        current_angle <= 17'h0;
    end else if (enable) begin
        angle_acc     <= angle_acc + ANGLE_STEP;
        current_angle <= angle_acc;
    end
end

//=============================================================================
// CORDIC模块实例化
//=============================================================================
// CORDIC输入设置 (旋转模式，计算sin/cos)
assign cordic_x_i     = CORDIC_GAIN;      // 初始X值 (CORDIC增益)
assign cordic_y_i     = 17'h0;            // 初始Y值 (0)
assign cordic_theta_i = current_angle;    // 输入角度

// CORDIC IP核实例化
CORDIC_Top u_cordic (
    .clk      (clk),
    .rst      (~rst_n),
    .x_i      (cordic_x_i),
    .y_i      (cordic_y_i),
    .theta_i  (cordic_theta_i),
    .x_o      (cordic_x_o),      // cos输出
    .y_o      (cordic_y_o),      // sin输出
    .theta_o  (cordic_theta_o)   // 剩余角度
);

//=============================================================================
// 正弦值处理和缩放
//=============================================================================
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        sine_raw       <= 17'h0;
        sine_scaled    <= 8'h0;
        sine_valid_reg <= 1'b0;
    end else begin
        // 获取CORDIC输出的正弦值
        sine_raw <= cordic_y_o;
        
        // 将17位有符号数转换为8位无符号数 (0-255)
        // 正弦值范围: -1 ~ +1 对应 -65536 ~ +65536
        // 转换为PWM占空比: 0 ~ 255
        if (sine_raw[16]) begin  // 负数
            sine_scaled <= 8'd128 - ((-sine_raw) >> 9);
        end else begin           // 正数
            sine_scaled <= 8'd128 + (sine_raw >> 9);
        end
        
        sine_valid_reg <= enable;
    end
end

//=============================================================================
// PWM模块实例化
//=============================================================================
assign pwm_clk  = clk;
assign pwm_duty = sine_scaled;
assign pwm_en   = enable;

PWM_Top u_pwm (
    .fclk                (pwm_clk),           // PWM工作时钟
    .pclk                (clk),               // 参数时钟
    .pwm_en              (pwm_en),            // PWM使能
    .up                  (1'b0),              // 不使用递增
    .down                (1'b0),              // 不使用递减
    .duty_cycle          (pwm_duty),          // 占空比
    .duty_cycle_update   (sine_valid_reg),    // 占空比更新
    .initial_cycle       (8'd255),            // 初始周期
    .initial_duty_cycle  (8'd128),            // 初始占空比
    .initial_update      (1'b1),              // 初始化更新
    .pwm                 (pwm_out)            // PWM输出
);

//=============================================================================
// 输出信号分配
//=============================================================================
assign sine_valid = sine_valid_reg;
assign debug_duty = sine_scaled;

endmodule
