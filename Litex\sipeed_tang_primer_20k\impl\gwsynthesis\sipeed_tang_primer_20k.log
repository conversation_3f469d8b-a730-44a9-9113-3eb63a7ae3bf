GowinSynthesis start
Running parser ...
Analyzing Verilog file 'G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\VexRiscv.v'
Analyzing Verilog file 'G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v'
Compiling module 'sipeed_tang_primer_20k'("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":21)
WARN  (EX3780) : Using initial value of 'basesoc_basesoc_adr_burst' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":426)
WARN  (EX3780) : Using initial value of 'basesoc_basesoc_ram_bus_err' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":435)
WARN  (EX3780) : Using initial value of 'basesoc_ram_adr_burst' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":495)
WARN  (EX3780) : Using initial value of 'basesoc_ram_bus_ram_bus_err' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":503)
WARN  (EX3780) : Using initial value of 'basesoc_rx_source_first' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":523)
WARN  (EX3780) : Using initial value of 'basesoc_rx_source_last' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":524)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_bankmachine0_replace' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":570)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_bankmachine0_sink_first' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":584)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_bankmachine0_sink_last' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":585)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_bankmachine1_replace' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":666)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_bankmachine1_sink_first' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":680)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_bankmachine1_sink_last' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":681)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_bankmachine2_replace' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":762)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_bankmachine2_sink_first' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":776)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_bankmachine2_sink_last' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":777)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_bankmachine3_replace' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":858)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_bankmachine3_sink_first' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":872)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_bankmachine3_sink_last' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":873)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_bankmachine4_replace' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":954)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_bankmachine4_sink_first' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":968)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_bankmachine4_sink_last' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":969)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_bankmachine5_replace' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":1050)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_bankmachine5_sink_first' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":1064)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_bankmachine5_sink_last' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":1065)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_bankmachine6_replace' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":1146)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_bankmachine6_sink_first' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":1160)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_bankmachine6_sink_last' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":1161)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_bankmachine7_replace' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":1242)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_bankmachine7_sink_first' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":1256)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_bankmachine7_sink_last' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":1257)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_choose_cmd_want_cmds' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":1315)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_choose_cmd_want_reads' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":1316)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_choose_cmd_want_writes' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":1317)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_choose_req_want_activates' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":1332)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_choose_req_want_cmds' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":1333)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_cmd_payload_is_read' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":1341)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_cmd_payload_is_write' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":1342)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_csr_dfi_p0_act_n' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":1347)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_csr_dfi_p1_act_n' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":1363)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_dfi_p0_act_n' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":1379)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_dfi_p1_act_n' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":1395)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_ext_dfi_p0_act_n' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":1413)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_ext_dfi_p0_address' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":1414)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_ext_dfi_p0_bank' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":1415)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_ext_dfi_p0_cas_n' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":1416)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_ext_dfi_p0_cke' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":1417)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_ext_dfi_p0_cs_n' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":1418)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_ext_dfi_p0_odt' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":1419)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_ext_dfi_p0_ras_n' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":1420)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_ext_dfi_p0_rddata_en' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":1422)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_ext_dfi_p0_reset_n' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":1424)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_ext_dfi_p0_we_n' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":1425)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_ext_dfi_p0_wrdata' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":1426)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_ext_dfi_p0_wrdata_en' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":1427)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_ext_dfi_p0_wrdata_mask' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":1428)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_ext_dfi_p1_act_n' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":1429)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_ext_dfi_p1_address' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":1430)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_ext_dfi_p1_bank' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":1431)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_ext_dfi_p1_cas_n' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":1432)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_ext_dfi_p1_cke' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":1433)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_ext_dfi_p1_cs_n' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":1434)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_ext_dfi_p1_odt' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":1435)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_ext_dfi_p1_ras_n' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":1436)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_ext_dfi_p1_rddata_en' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":1438)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_ext_dfi_p1_reset_n' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":1440)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_ext_dfi_p1_we_n' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":1441)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_ext_dfi_p1_wrdata' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":1442)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_ext_dfi_p1_wrdata_en' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":1443)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_ext_dfi_p1_wrdata_mask' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":1444)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_ext_dfi_sel' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":1445)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_nop_a' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":1540)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_nop_ba' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":1541)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_phaseinjector0_command_issue_w' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":1549)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_phaseinjector1_command_issue_w' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":1572)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_steerer2' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":1637)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_steerer3' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":1638)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_steerer4' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":1639)
WARN  (EX3780) : Using initial value of 'basesoc_sdram_steerer5' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":1640)
WARN  (EX3780) : Using initial value of 'basesoc_uart_rx_fifo_replace' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":1755)
WARN  (EX3780) : Using initial value of 'basesoc_uart_tx_fifo_replace' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":1813)
WARN  (EX3780) : Using initial value of 'basesoc_uart_tx_fifo_sink_first' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":1814)
WARN  (EX3780) : Using initial value of 'basesoc_uart_tx_fifo_sink_last' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":1815)
WARN  (EX3780) : Using initial value of 'basesoc_vexriscv' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":1854)
WARN  (EX3780) : Using initial value of 'basesoc_wb_sdram_err' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":1862)
WARN  (EX3780) : Using initial value of 'gw2ddrphy_burstdet_clr_w' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":2219)
WARN  (EX3780) : Using initial value of 'gw2ddrphy_rdly_dq_bitslip_rst_w' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":2465)
WARN  (EX3780) : Using initial value of 'gw2ddrphy_rdly_dq_bitslip_w' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":2467)
WARN  (EX3780) : Using initial value of 'gw2ddrphy_rdly_dq_inc_w' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":2471)
WARN  (EX3780) : Using initial value of 'gw2ddrphy_rdly_dq_rst_w' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":2475)
WARN  (EX3780) : Using initial value of 'interface0_err' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":2502)
WARN  (EX3780) : Using initial value of 'locked0' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":2529)
WARN  (EX3780) : Using initial value of 'locked1' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":2530)
WARN  (EX3780) : Using initial value of 'locked2' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":2531)
WARN  (EX3780) : Using initial value of 'locked3' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":2532)
WARN  (EX3780) : Using initial value of 'locked4' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":2533)
WARN  (EX3780) : Using initial value of 'locked5' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":2534)
WARN  (EX3780) : Using initial value of 'locked6' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":2535)
WARN  (EX3780) : Using initial value of 'locked7' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":2536)
WARN  (EX3780) : Using initial value of 'por_rst' since it is never assigned("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":2562)
Extracting RAM for identifier 'rom'("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":10102)
Extracting RAM for identifier 'sram'("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":10117)
Extracting RAM for identifier 'mem'("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":10140)
Extracting RAM for identifier 'storage'("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":10156)
Extracting RAM for identifier 'storage_1'("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":10177)
Extracting RAM for identifier 'storage_2'("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":12433)
Extracting RAM for identifier 'storage_3'("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":12451)
Extracting RAM for identifier 'storage_4'("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":12469)
Extracting RAM for identifier 'storage_5'("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":12487)
Extracting RAM for identifier 'storage_6'("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":12505)
Extracting RAM for identifier 'storage_7'("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":12523)
Extracting RAM for identifier 'storage_8'("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":12541)
Extracting RAM for identifier 'storage_9'("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":12559)
Extracting RAM for identifier 'tag_mem'("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":12576)
Extracting RAM for identifier 'data_mem_grain0'("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":12675)
Extracting RAM for identifier 'data_mem_grain1'("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":12689)
Extracting RAM for identifier 'data_mem_grain2'("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":12703)
Extracting RAM for identifier 'data_mem_grain3'("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":12717)
Extracting RAM for identifier 'data_mem_grain4'("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":12731)
Extracting RAM for identifier 'data_mem_grain5'("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":12745)
Extracting RAM for identifier 'data_mem_grain6'("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":12759)
Extracting RAM for identifier 'data_mem_grain7'("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":12773)
Extracting RAM for identifier 'data_mem_grain8'("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":12787)
Extracting RAM for identifier 'data_mem_grain9'("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":12801)
Extracting RAM for identifier 'data_mem_grain10'("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":12815)
Extracting RAM for identifier 'data_mem_grain11'("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":12829)
Extracting RAM for identifier 'data_mem_grain12'("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":12843)
Extracting RAM for identifier 'data_mem_grain13'("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":12857)
Extracting RAM for identifier 'data_mem_grain14'("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":12871)
Extracting RAM for identifier 'data_mem_grain15'("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":12885)
WARN  (EX3791) : Expression size 8 truncated to fit in target size 1("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":2805)
WARN  (EX3791) : Expression size 2 truncated to fit in target size 1("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":3856)
WARN  (EX3791) : Expression size 2 truncated to fit in target size 1("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":3887)
WARN  (EX3791) : Expression size 30 truncated to fit in target size 28("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":5782)
WARN  (EX3791) : Expression size 28 truncated to fit in target size 23("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":5849)
WARN  (EX3791) : Expression size 30 truncated to fit in target size 14("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":5949)
WARN  (EX3791) : Expression size 4 truncated to fit in target size 3("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":9338)
WARN  (EX3791) : Expression size 7 truncated to fit in target size 6("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v":9400)
Compiling module 'VexRiscv'("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\VexRiscv.v":7)
Extracting RAM for identifier 'RegFilePlugin_regFile'("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\VexRiscv.v":1323)
Compiling module 'InstructionCache'("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\VexRiscv.v":6021)
Extracting RAM for identifier 'banks_0'("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\VexRiscv.v":6127)
Extracting RAM for identifier 'ways_0_tags'("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\VexRiscv.v":6128)
Compiling module 'DataCache'("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\VexRiscv.v":5167)
Extracting RAM for identifier 'ways_0_tags'("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\VexRiscv.v":5381)
Extracting RAM for identifier 'ways_0_data_symbol0'("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\VexRiscv.v":5382)
Extracting RAM for identifier 'ways_0_data_symbol1'("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\VexRiscv.v":5383)
Extracting RAM for identifier 'ways_0_data_symbol2'("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\VexRiscv.v":5384)
Extracting RAM for identifier 'ways_0_data_symbol3'("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\VexRiscv.v":5385)
WARN  (EX3858) : System task 'display' is ignored for synthesis("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\VexRiscv.v":5993)
WARN  (EX1998) : Net 'IBusCachedPlugin_cache_io_cpu_fetch_isRemoved' does not have a driver("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\VexRiscv.v":67)
WARN  (EX1998) : Net 'dataCache_1_io_cpu_writeBack_fence_SW' does not have a driver("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\VexRiscv.v":81)
WARN  (EX1998) : Net 'dataCache_1_io_cpu_writeBack_fence_SR' does not have a driver("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\VexRiscv.v":82)
WARN  (EX1998) : Net 'dataCache_1_io_cpu_writeBack_fence_SO' does not have a driver("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\VexRiscv.v":83)
WARN  (EX1998) : Net 'dataCache_1_io_cpu_writeBack_fence_SI' does not have a driver("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\VexRiscv.v":84)
WARN  (EX1998) : Net 'dataCache_1_io_cpu_writeBack_fence_PW' does not have a driver("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\VexRiscv.v":85)
WARN  (EX1998) : Net 'dataCache_1_io_cpu_writeBack_fence_PR' does not have a driver("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\VexRiscv.v":86)
WARN  (EX1998) : Net 'dataCache_1_io_cpu_writeBack_fence_PO' does not have a driver("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\VexRiscv.v":87)
WARN  (EX1998) : Net 'dataCache_1_io_cpu_writeBack_fence_PI' does not have a driver("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\VexRiscv.v":88)
WARN  (EX1998) : Net 'dataCache_1_io_cpu_writeBack_fence_FM[3]' does not have a driver("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\VexRiscv.v":89)
WARN  (EX1998) : Net 'IBusCachedPlugin_mmuBus_rsp_bypassTranslation' does not have a driver("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\VexRiscv.v":604)
WARN  (EX1998) : Net 'dBus_rsp_payload_last' does not have a driver("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\VexRiscv.v":617)
WARN  (EX1998) : Net 'DBusCachedPlugin_mmuBus_rsp_bypassTranslation' does not have a driver("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\VexRiscv.v":632)
WARN  (EX1998) : Net 'CsrPlugin_mtvec_mode[1]' does not have a driver("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\VexRiscv.v":930)
NOTE  (EX0101) : Current top module is "sipeed_tang_primer_20k"
WARN  (EX0211) : The output port "io_cpu_writeBack_exclusiveOk" of module "DataCache" has no driver, assigning undriven bits to Z, simulation mismatch possible("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\VexRiscv.v":5210)
WARN  (EX0211) : The output port "io_cpu_writesPending" of module "DataCache" has no driver, assigning undriven bits to Z, simulation mismatch possible("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\VexRiscv.v":5216)
[5%] Running netlist conversion ...
Running device independent optimization ...
[10%] Optimizing Phase 0 completed
[15%] Optimizing Phase 1 completed
[25%] Optimizing Phase 2 completed
Running inference ...
[30%] Inferring Phase 0 completed
[40%] Inferring Phase 1 completed
[50%] Inferring Phase 2 completed
[55%] Inferring Phase 3 completed
Running technical mapping ...
[60%] Tech-Mapping Phase 0 completed
[65%] Tech-Mapping Phase 1 completed
[75%] Tech-Mapping Phase 2 completed
[80%] Tech-Mapping Phase 3 completed
[90%] Tech-Mapping Phase 4 completed
[95%] Generate netlist file "G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\impl\gwsynthesis\sipeed_tang_primer_20k.vg" completed
[100%] Generate report file "G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\impl\gwsynthesis\sipeed_tang_primer_20k_syn.rpt.html" completed
GowinSynthesis finish
