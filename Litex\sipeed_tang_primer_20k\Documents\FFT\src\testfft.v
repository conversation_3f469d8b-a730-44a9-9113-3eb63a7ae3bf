
module testfft(
    clk   ,
    rst  ,
    ipd  ,
    dout  
    );

  input         clk   ;
  input         rst  ;
  input         ipd  ;
  output  [15:0] dout  ;

  reg     [15:0] dout  ;
  reg     [9:0] addr  ;
  reg     [15:0] data [0:1023];

  always@(posedge clk or posedge rst)
  begin
    if(rst)
      addr <= 'b0;
    else if(ipd)
      addr <= addr + 1'b1;
  end

  GW_ROM U_GW_ROM (.dout(dout), .clk(clk), .oce(1'b1), .ce(ipd), .reset(rst), .wre(1'b0), .ad(addr));

endmodule

module GW_ROM (dout, clk, oce, ce, reset, wre, ad);

output [15:0] dout;
input clk;
input oce;
input ce;
input reset;
input wre;
input [9:0] ad;

wire gw_gnd;
wire [15:0] dout_o;
assign gw_gnd = 1'b0;

ROM bram_rom_0 (
    .DO({dout[15:0],dout_o[15:0]}),
    .CLK(clk),
    .OCE(oce),
    .CE(ce),
    .RESET(reset),
    .WRE(wre),
    .BLKSEL({gw_gnd,gw_gnd,gw_gnd}),
    .AD({ad[9:0],gw_gnd,gw_gnd,gw_gnd,gw_gnd})
);

defparam bram_rom_0.READ_MODE = 1'b0;
defparam bram_rom_0.BIT_WIDTH = 16;
defparam bram_rom_0.BLK_SEL = 3'b000;
defparam bram_rom_0.RESET_MODE = "SYNC";
defparam bram_rom_0.INIT_RAM_00 = 256'hF780E580F68013001380F780E580F68013001380F780E580F68013001380F780;
defparam bram_rom_0.INIT_RAM_01 = 256'h1380F780E580F68013001380F780E580F68013001380F780E580F68013001380;
defparam bram_rom_0.INIT_RAM_02 = 256'h13001380F780E580F68013001380F780E580F68013001380F780E580F6801300;
defparam bram_rom_0.INIT_RAM_03 = 256'hF68013001380F780E580F68013001380F780E580F68013001380F780E580F680;
defparam bram_rom_0.INIT_RAM_04 = 256'hE580F68013001380F780E580F68013001380F780E580F68013001380F780E580;
defparam bram_rom_0.INIT_RAM_05 = 256'hF780E580F68013001380F780E580F68013001380F780E580F68013001380F780;
defparam bram_rom_0.INIT_RAM_06 = 256'h1380F780E580F68013001380F780E580F68013001380F780E580F68013001380;
defparam bram_rom_0.INIT_RAM_07 = 256'h13001380F780E580F68013001380F780E580F68013001380F780E580F6801300;
defparam bram_rom_0.INIT_RAM_08 = 256'hF68013001380F780E580F68012801380F780E580F68013001380F780E580F680;
defparam bram_rom_0.INIT_RAM_09 = 256'hE580F68013001380F780E580F68013001380F780E580F68013001380F780E580;
defparam bram_rom_0.INIT_RAM_0A = 256'hF780E580F68013001380F780E580F68013001380F780E580F68013001380F780;
defparam bram_rom_0.INIT_RAM_0B = 256'h1380F780E580F68013001380F780E580F68013001380F780E580F68013001380;
defparam bram_rom_0.INIT_RAM_0C = 256'h13001380F780E600F68013001380F780E580F68013001380F780E580F6801300;
defparam bram_rom_0.INIT_RAM_0D = 256'hF68013001380F780E580F68013001380F780E580F68013001380F780E580F680;
defparam bram_rom_0.INIT_RAM_0E = 256'hE600F68013001380F780E580F68013001380F780E580F68013001380F780E580;
defparam bram_rom_0.INIT_RAM_0F = 256'hF780E580F68012801380F780E580F68013001380F780E580F68013001380F780;
defparam bram_rom_0.INIT_RAM_10 = 256'h1380F780E580F68013001380F780E580F68013001380F780E580F68013001380;
defparam bram_rom_0.INIT_RAM_11 = 256'h13001380F780E580F68013001380F780E580F68013001380F780E580F6801300;
defparam bram_rom_0.INIT_RAM_12 = 256'hF68013001380F780E580F68013001380F780E580F68013001380F780E580F680;
defparam bram_rom_0.INIT_RAM_13 = 256'hE580F68013001380F780E580F68013001380F780E580F68013001380F780E580;
defparam bram_rom_0.INIT_RAM_14 = 256'hF780E580F68013001380F780E580F68013001380F780E580F68013001380F780;
defparam bram_rom_0.INIT_RAM_15 = 256'h1380F780E580F68013001380F780E580F68013001380F780E580F68013001380;
defparam bram_rom_0.INIT_RAM_16 = 256'h13001380F780E580F68013001380F780E580F68013001380F780E580F6801300;
defparam bram_rom_0.INIT_RAM_17 = 256'hF68013001380F780E580F68013001380F780E580F68013001380F780E580F680;
defparam bram_rom_0.INIT_RAM_18 = 256'hE580F68013001380F780E580F68013001380F780E580F68013001380F780E580;
defparam bram_rom_0.INIT_RAM_19 = 256'hF780E580F68013001380F780E580F68013001380F780E580F68013001380F780;
defparam bram_rom_0.INIT_RAM_1A = 256'h1380F800E580F68013001380F780E580F68013001380F780E600F68013001380;
defparam bram_rom_0.INIT_RAM_1B = 256'h13001380F780E580F68013001380F780E580F68013001380F780E600F6801300;
defparam bram_rom_0.INIT_RAM_1C = 256'hF68013001380F780E580F68013001380F780E580F68013001380F780E580F680;
defparam bram_rom_0.INIT_RAM_1D = 256'hE580F68013001380F780E580F68013001380F780E580F68013001380F780E580;
defparam bram_rom_0.INIT_RAM_1E = 256'hF780E580F68013001380F780E580F68013001380F780E580F68013001380F780;
defparam bram_rom_0.INIT_RAM_1F = 256'h1380F780E580F68013001380F780E580F68013001380F780E580F68013001380;
defparam bram_rom_0.INIT_RAM_20 = 256'h13001380F780E580F68013001380F780E580F68013001380F780E580F6801300;
defparam bram_rom_0.INIT_RAM_21 = 256'hF68013001380F780E580F68013001380F780E580F68013001380F780E580F680;
defparam bram_rom_0.INIT_RAM_22 = 256'hE580F68013001380F780E580F68013001380F780E580F68013001380F780E580;
defparam bram_rom_0.INIT_RAM_23 = 256'hF780E580F68013001380F780E580F68013001380F780E580F68013001380F780;
defparam bram_rom_0.INIT_RAM_24 = 256'h1380F780E580F68013001380F780E580F68013001380F780E580F68013001380;
defparam bram_rom_0.INIT_RAM_25 = 256'h13001380F780E580F68013001380F780E580F68013001380F780E580F6801300;
defparam bram_rom_0.INIT_RAM_26 = 256'hF68013001380F780E580F68013001380F780E580F68013001380F780E580F680;
defparam bram_rom_0.INIT_RAM_27 = 256'hE580F68013001380F780E580F68013001380F780E580F68013001380F780E580;
defparam bram_rom_0.INIT_RAM_28 = 256'hF780E580F68013001380F780E580F68013001380F780E580F68013001380F780;
defparam bram_rom_0.INIT_RAM_29 = 256'h1380F780E580F68013001380F780E580F68013001380F780E580F68013001380;
defparam bram_rom_0.INIT_RAM_2A = 256'h13001380F780E580F68013001380F780E580F68013001380F780E600F6801300;
defparam bram_rom_0.INIT_RAM_2B = 256'hF68013001380F780E580F68013001380F780E580F68013001380F800E580F680;
defparam bram_rom_0.INIT_RAM_2C = 256'hE580F68013001380F780E580F68013001380F780E580F68013001380F780E580;
defparam bram_rom_0.INIT_RAM_2D = 256'hF780E580F68013001380F780E580F68013001380F780E580F68013001400F780;
defparam bram_rom_0.INIT_RAM_2E = 256'h1380F780E580F68013001380F780E580F68013001380F780E580F68013001380;
defparam bram_rom_0.INIT_RAM_2F = 256'h12801380F780E580F68013001380F780E580F68013001380F780E580F6801300;
defparam bram_rom_0.INIT_RAM_30 = 256'hF68013001380F780E580F68013001380F780E580F68013001380F780E580F680;
defparam bram_rom_0.INIT_RAM_31 = 256'hE580F68013001380F780E580F68013001380F780E580F68013001380F780E580;
defparam bram_rom_0.INIT_RAM_32 = 256'hF780E580F68013001380F780E580F68013001380F780E580F68013001380F780;
defparam bram_rom_0.INIT_RAM_33 = 256'h1380F780E580F68013001380F780E580F68013001380F780E580F68013001380;
defparam bram_rom_0.INIT_RAM_34 = 256'h13001380F780E580F68013001380F780E580F68013001380F780E580F6801300;
defparam bram_rom_0.INIT_RAM_35 = 256'hF68013001380F780E580F68013001380F780E580F68013001380F800E580F680;
defparam bram_rom_0.INIT_RAM_36 = 256'hE580F68013001380F780E580F68013001380F780E580F68013001380F780E580;
defparam bram_rom_0.INIT_RAM_37 = 256'hF780E580F68013001380F780E580F68013001380F780E580F68012801380F780;
defparam bram_rom_0.INIT_RAM_38 = 256'h1380F800E580F68013001380F780E580F68013001380F780E580F68013001380;
defparam bram_rom_0.INIT_RAM_39 = 256'h13001380F780E580F68013001380F800E580F68013001380F780E580F6801300;
defparam bram_rom_0.INIT_RAM_3A = 256'hF68013001380F780E580F68013001380F780E580F68013001380F780E580F680;
defparam bram_rom_0.INIT_RAM_3B = 256'hE580F68013001380F780E580F68013001380F780E580F68013001380F780E580;
defparam bram_rom_0.INIT_RAM_3C = 256'hF780E580F68013001380F780E600F68013001380F780E580F68012801380F780;
defparam bram_rom_0.INIT_RAM_3D = 256'h1380F780E580F68013001380F780E580F68013001380F780E580F68013001380;
defparam bram_rom_0.INIT_RAM_3E = 256'h13001380F780E580F68013001380F780E580F68013001380F780E580F6801300;
defparam bram_rom_0.INIT_RAM_3F = 256'hF68012801380F780E600F68013001380F780E580F68013001380F780E580F680;

endmodule //GW_ROM

