parameter TRIG_LEVELS_INIT = 33'h000000000;
parameter MAX_DATA_DEPTH_INIT = 33'h0000007ff;
parameter DATA_DEPTH_INIT = 33'h000000799;
parameter NUM_WINDOWS_INIT = 33'h000000001;
parameter MATCH0_REG_M0_INIT = 33'h000000000;
parameter MATCH0_REG_M1_INIT = 33'h000000003;
parameter MATCH0_REG_M2_INIT = 33'h000000000;
parameter MATCH0_REG_CTRL_INIT = 33'h000000001;
parameter MATCH0_TRIG_CNT_INIT = 16'h0000;
parameter MATCH1_REG_M0_INIT = 16'h0000;
parameter MATCH1_REG_M1_INIT = 16'h0000;
parameter MATCH1_REG_M2_INIT = 16'h0000;
parameter MATCH1_REG_CTRL_INIT = 16'h0000;
parameter MATCH1_TRIG_CNT_INIT = 16'h0000;
parameter MATCH2_REG_M0_INIT = 16'h0000;
parameter MATCH2_REG_M1_INIT = 16'h0000;
parameter MATCH2_REG_M2_INIT = 16'h0000;
parameter MATCH2_REG_CTRL_INIT = 16'h0000;
parameter MATCH2_TRIG_CNT_INIT = 16'h0000;
parameter MATCH3_REG_M0_INIT = 16'h0000;
parameter MATCH3_REG_M1_INIT = 16'h0000;
parameter MATCH3_REG_M2_INIT = 16'h0000;
parameter MATCH3_REG_CTRL_INIT = 16'h0000;
parameter MATCH3_TRIG_CNT_INIT = 16'h0000;
parameter MATCH4_REG_M0_INIT = 16'h0000;
parameter MATCH4_REG_M1_INIT = 16'h0000;
parameter MATCH4_REG_M2_INIT = 16'h0000;
parameter MATCH4_REG_CTRL_INIT = 16'h0000;
parameter MATCH4_TRIG_CNT_INIT = 16'h0000;
parameter MATCH5_REG_M0_INIT = 16'h0000;
parameter MATCH5_REG_M1_INIT = 16'h0000;
parameter MATCH5_REG_M2_INIT = 16'h0000;
parameter MATCH5_REG_CTRL_INIT = 16'h0000;
parameter MATCH5_TRIG_CNT_INIT = 16'h0000;
parameter MATCH6_REG_M0_INIT = 16'h0000;
parameter MATCH6_REG_M1_INIT = 16'h0000;
parameter MATCH6_REG_M2_INIT = 16'h0000;
parameter MATCH6_REG_CTRL_INIT = 16'h0000;
parameter MATCH6_TRIG_CNT_INIT = 16'h0000;
parameter MATCH7_REG_M0_INIT = 16'h0000;
parameter MATCH7_REG_M1_INIT = 16'h0000;
parameter MATCH7_REG_M2_INIT = 16'h0000;
parameter MATCH7_REG_CTRL_INIT = 16'h0000;
parameter MATCH7_TRIG_CNT_INIT = 16'h0000;
parameter MATCH8_REG_M0_INIT = 16'h0000;
parameter MATCH8_REG_M1_INIT = 16'h0000;
parameter MATCH8_REG_M2_INIT = 16'h0000;
parameter MATCH8_REG_CTRL_INIT = 16'h0000;
parameter MATCH8_TRIG_CNT_INIT = 16'h0000;
parameter MATCH9_REG_M0_INIT = 16'h0000;
parameter MATCH9_REG_M1_INIT = 16'h0000;
parameter MATCH9_REG_M2_INIT = 16'h0000;
parameter MATCH9_REG_CTRL_INIT = 16'h0000;
parameter MATCH9_TRIG_CNT_INIT = 16'h0000;
parameter MATCH10_REG_M0_INIT = 16'h0000;
parameter MATCH10_REG_M1_INIT = 16'h0000;
parameter MATCH10_REG_M2_INIT = 16'h0000;
parameter MATCH10_REG_CTRL_INIT = 16'h0000;
parameter MATCH10_TRIG_CNT_INIT = 16'h0000;
parameter MATCH11_REG_M0_INIT = 16'h0000;
parameter MATCH11_REG_M1_INIT = 16'h0000;
parameter MATCH11_REG_M2_INIT = 16'h0000;
parameter MATCH11_REG_CTRL_INIT = 16'h0000;
parameter MATCH11_TRIG_CNT_INIT = 16'h0000;
parameter MATCH12_REG_M0_INIT = 16'h0000;
parameter MATCH12_REG_M1_INIT = 16'h0000;
parameter MATCH12_REG_M2_INIT = 16'h0000;
parameter MATCH12_REG_CTRL_INIT = 16'h0000;
parameter MATCH12_TRIG_CNT_INIT = 16'h0000;
parameter MATCH13_REG_M0_INIT = 16'h0000;
parameter MATCH13_REG_M1_INIT = 16'h0000;
parameter MATCH13_REG_M2_INIT = 16'h0000;
parameter MATCH13_REG_CTRL_INIT = 16'h0000;
parameter MATCH13_TRIG_CNT_INIT = 16'h0000;
parameter MATCH14_REG_M0_INIT = 16'h0000;
parameter MATCH14_REG_M1_INIT = 16'h0000;
parameter MATCH14_REG_M2_INIT = 16'h0000;
parameter MATCH14_REG_CTRL_INIT = 16'h0000;
parameter MATCH14_TRIG_CNT_INIT = 16'h0000;
parameter MATCH15_REG_M0_INIT = 16'h0000;
parameter MATCH15_REG_M1_INIT = 16'h0000;
parameter MATCH15_REG_M2_INIT = 16'h0000;
parameter MATCH15_REG_CTRL_INIT = 16'h0000;
parameter MATCH15_TRIG_CNT_INIT = 16'h0000;
