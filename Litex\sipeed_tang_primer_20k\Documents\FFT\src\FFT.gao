<?xml version="1" encoding="UTF-8"?>
<GAO_CONFIG>
    <Version>3.0</Version>
    <Mode>Standard</Mode>
    <AoCore index="0" sample_clock="out_d_11[35]" trig_type="0" storage_depth="4096" window_num="2" capture_amount="2048" implementation="0" trigger_pos="100" module_name="testfft_top" force_trigger_by_falling_edge="false" capture_init_data_enabled="false">
        <SignalList>
            <Signal capture_enable="true">out_d_14[52]</Signal>
            <Signal capture_enable="true">out_d_14[51]</Signal>
            <Signal capture_enable="true">out_d_14[50]</Signal>
            <Signal capture_enable="true">out_d_14[49]</Signal>
            <Signal capture_enable="true">out_d_14[48]</Signal>
            <Signal capture_enable="true">out_d_14[47]</Signal>
            <Signal capture_enable="true">out_d_14[46]</Signal>
            <Signal capture_enable="true">out_d_14[45]</Signal>
            <Signal capture_enable="true">out_d_14[44]</Signal>
            <Signal capture_enable="true">out_d_14[43]</Signal>
            <Signal capture_enable="true">out_d_14[42]</Signal>
            <Signal capture_enable="true">out_d_14[41]</Signal>
            <Signal capture_enable="true">out_d_14[40]</Signal>
            <Signal capture_enable="true">out_d_14[32]</Signal>
            <Signal capture_enable="true">out_d_14[26]</Signal>
            <Signal capture_enable="true">out_d_14[25]</Signal>
            <Signal capture_enable="true">out_d_14[24]</Signal>
            <Signal capture_enable="true">out_d_14[23]</Signal>
            <Signal capture_enable="true">out_d_14[22]</Signal>
            <Signal capture_enable="true">out_d_14[21]</Signal>
            <Signal capture_enable="true">out_d_14[20]</Signal>
            <Signal capture_enable="true">out_d_14[19]</Signal>
            <Signal capture_enable="true">out_d_14[18]</Signal>
            <Signal capture_enable="true">out_d_14[17]</Signal>
            <Signal capture_enable="true">out_d_14[16]</Signal>
            <Signal capture_enable="true">out_d_14[15]</Signal>
            <Signal capture_enable="true">out_d_14[14]</Signal>
            <Signal capture_enable="true">out_d_14[13]</Signal>
            <Signal capture_enable="true">out_d_14[12]</Signal>
            <Signal capture_enable="true">out_d_14[11]</Signal>
            <Signal capture_enable="true">out_d_14[32]</Signal>
            <Signal capture_enable="true">out_d_13[28]</Signal>
        </SignalList>
        <Triggers>
            <Trigger index="0">
                <SignalList>
                    <Signal>out_d_13[28]</Signal>
                    <Signal>out_d_14[32]</Signal>
                </SignalList>
            </Trigger>
            <Trigger index="1">
                <SignalList>
                    <Signal>out[52]</Signal>
                    <Signal>out[51]</Signal>
                    <Signal>out[50]</Signal>
                    <Signal>out[49]</Signal>
                    <Signal>out[48]</Signal>
                    <Signal>out[47]</Signal>
                    <Signal>out[46]</Signal>
                    <Signal>out[45]</Signal>
                    <Signal>out[44]</Signal>
                    <Signal>out[43]</Signal>
                    <Signal>out[42]</Signal>
                    <Signal>out[41]</Signal>
                    <Signal>out[40]</Signal>
                </SignalList>
            </Trigger>
            <Trigger index="2">
                <SignalList>
                    <Signal>out[26]</Signal>
                    <Signal>out[25]</Signal>
                    <Signal>out[24]</Signal>
                    <Signal>out[23]</Signal>
                    <Signal>out[22]</Signal>
                    <Signal>out[21]</Signal>
                    <Signal>out[20]</Signal>
                    <Signal>out[19]</Signal>
                    <Signal>out[18]</Signal>
                    <Signal>out[17]</Signal>
                    <Signal>out[16]</Signal>
                    <Signal>out[15]</Signal>
                    <Signal>out[14]</Signal>
                    <Signal>out[13]</Signal>
                    <Signal>out[12]</Signal>
                    <Signal>out[11]</Signal>
                </SignalList>
            </Trigger>
            <Trigger index="3">
                <SignalList>
                    <Signal>out[28]</Signal>
                </SignalList>
            </Trigger>
            <Trigger index="4">
                <SignalList>
                    <Signal>out[32]</Signal>
                </SignalList>
            </Trigger>
            <Trigger index="5"/>
            <Trigger index="6"/>
            <Trigger index="7"/>
            <Trigger index="8"/>
            <Trigger index="9"/>
            <Trigger index="10"/>
            <Trigger index="11"/>
            <Trigger index="12"/>
            <Trigger index="13"/>
            <Trigger index="14"/>
            <Trigger index="15"/>
        </Triggers>
        <MatchUnits>
            <MatchUnit index="0" enabled="1" match_type="1" counter_enable="0" counter_width="2" counter="2" countinuous="0" func="1" value0="00" value1="00" trigger="0"/>
            <MatchUnit index="1" enabled="0" match_type="0" counter_enable="0" counter_width="2" counter="2" countinuous="0" func="0" value0="" value1=""/>
            <MatchUnit index="2" enabled="0" match_type="0" counter_enable="0" counter_width="2" counter="2" countinuous="0" func="0" value0="" value1=""/>
            <MatchUnit index="3" enabled="0" match_type="0" counter_enable="0" counter_width="2" counter="2" countinuous="0" func="0" value0="" value1=""/>
            <MatchUnit index="4" enabled="0" match_type="0" counter_enable="0" counter_width="2" counter="2" countinuous="0" func="0" value0="" value1=""/>
            <MatchUnit index="5" enabled="0" match_type="0" counter_enable="0" counter_width="2" counter="2" countinuous="0" func="0" value0="" value1=""/>
            <MatchUnit index="6" enabled="0" match_type="0" counter_enable="0" counter_width="2" counter="2" countinuous="0" func="0" value0="" value1=""/>
            <MatchUnit index="7" enabled="0" match_type="0" counter_enable="0" counter_width="2" counter="2" countinuous="0" func="0" value0="" value1=""/>
            <MatchUnit index="8" enabled="0" match_type="0" counter_enable="0" counter_width="2" counter="2" countinuous="0" func="0" value0="" value1=""/>
            <MatchUnit index="9" enabled="0" match_type="0" counter_enable="0" counter_width="2" counter="2" countinuous="0" func="0" value0="" value1=""/>
            <MatchUnit index="10" enabled="0" match_type="0" counter_enable="0" counter_width="2" counter="2" countinuous="0" func="0" value0="" value1=""/>
            <MatchUnit index="11" enabled="0" match_type="0" counter_enable="0" counter_width="2" counter="2" countinuous="0" func="0" value0="" value1=""/>
            <MatchUnit index="12" enabled="0" match_type="0" counter_enable="0" counter_width="2" counter="2" countinuous="0" func="0" value0="" value1=""/>
            <MatchUnit index="13" enabled="0" match_type="0" counter_enable="0" counter_width="2" counter="2" countinuous="0" func="0" value0="" value1=""/>
            <MatchUnit index="14" enabled="0" match_type="0" counter_enable="0" counter_width="2" counter="2" countinuous="0" func="0" value0="" value1=""/>
            <MatchUnit index="15" enabled="0" match_type="0" counter_enable="0" counter_width="2" counter="2" countinuous="0" func="0" value0="" value1=""/>
        </MatchUnits>
        <Expressions type="Static">
            <Expression>M0</Expression>
        </Expressions>
    </AoCore>
    <GAO_ID>0000111101100101</GAO_ID>
</GAO_CONFIG>
