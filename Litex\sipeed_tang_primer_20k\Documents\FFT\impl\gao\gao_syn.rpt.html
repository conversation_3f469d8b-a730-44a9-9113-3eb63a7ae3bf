<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>synthesis Report</title>
<style type="text/css">
body { font-family: Verdana, Arial, sans-serif; font-size: 12px; }
div#main_wrapper{ width: 100%; }
div#content { margin-left: 350px; margin-right: 30px; }
div#catalog_wrapper {position: fixed; top: 30px; width: 350px; float: left; }
div#catalog ul { list-style-type: none; }
div#catalog li { text-align: left; list-style-type:circle; color: #0084ff; margin-top: 3px; margin-bottom: 3px; }
div#catalog a { display:inline-block; text-decoration: none; color: #0084ff; font-weight: bold; padding: 3px; }
div#catalog a:visited { color: #0084ff; }
div#catalog a:hover { color: #fff; background: #0084ff; }
hr { margin-top: 30px; margin-bottom: 30px; }
h1, h3 { text-align: center; }
h1 {margin-top: 50px; }
table, th, td { border: 1px solid #aaa; }
table { border-collapse:collapse; margin-top: 10px; margin-bottom: 20px; width: 100%; }
th, td { padding: 5px 5px 5px 5px; }
th { color: #fff; font-weight: bold; background-color: #0084ff; }
table.summary_table td.label { width: 24%; min-width: 200px; background-color: #dee8f4; }
table.detail_table td.label { min-width: 100px; width: 8%;}
</style>
</head>
<body>
<div id="main_wrapper">
<div id="catalog_wrapper">
<div id="catalog">
<ul>
<li><a href="#about" style=" font-size: 16px;">Synthesis Messages</a></li>
<li><a href="#summary" style=" font-size: 16px;">Synthesis Details</a></li>
<li><a href="#resource" style=" font-size: 16px;">Resource</a>
<ul>
<li><a href="#usage" style=" font-size: 14px;">Resource Usage Summary</a></li>
<li><a href="#utilization" style=" font-size: 14px;">Resource Utilization Summary</a></li>
</ul>
</li>
<li><a href="#timing" style=" font-size: 16px;">Timing</a>
<ul>
<li><a href="#clock" style=" font-size: 14px;">Clock Summary</a></li>
<li><a href="#performance" style=" font-size: 14px;">Max Frequency Summary</a></li>
<li><a href="#detail timing" style=" font-size: 14px;">Detail Timing Paths Informations</a></li>
</ul>
</li>
</ul>
</div><!-- catalog -->
</div><!-- catalog_wrapper -->
<div id="content">
<h1><a name="about">Synthesis Messages</a></h1>
<table class="summary_table">
<tr>
<td class="label">Report Title</td>
<td>GowinSynthesis Report</td>
</tr>
<tr>
<td class="label">Design File</td>
<td>G:\Gowin\Gowin_V1.9.11.03_Education_x64_win\Gowin\Gowin_V1.9.11.03_Education_x64\IDE\data\ipcores\GAO\GW_AO_0\gw_ao_crc32.v<br>
G:\Gowin\Gowin_V1.9.11.03_Education_x64_win\Gowin\Gowin_V1.9.11.03_Education_x64\IDE\data\ipcores\GAO\GW_AO_0\gw_ao_match.v<br>
G:\Gowin\Gowin_V1.9.11.03_Education_x64_win\Gowin\Gowin_V1.9.11.03_Education_x64\IDE\data\ipcores\GAO\GW_AO_0\gw_ao_mem_ctrl.v<br>
G:\Gowin\Gowin_V1.9.11.03_Education_x64_win\Gowin\Gowin_V1.9.11.03_Education_x64\IDE\data\ipcores\GAO\GW_AO_0\gw_ao_top.v<br>
G:\Gowin\Gowin_V1.9.11.03_Education_x64_win\Gowin\Gowin_V1.9.11.03_Education_x64\IDE\data\ipcores\GAO\GW_CON\gw_con_top.v<br>
G:\Gowin\Gowin_V1.9.11.03_Education_x64_win\Gowin\Gowin_V1.9.11.03_Education_x64\IDE\data\ipcores\gw_jtag.v<br>
G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\Documents\FFT\impl\gao\gw_gao_top.v<br>
</td>
</tr>
<tr>
<td class="label">GowinSynthesis Constraints File</td>
<td>---</td>
</tr>
<tr>
<td class="label">Tool Version</td>
<td>V1.9.11.03 Education</td>
</tr>
<tr>
<td class="label">Part Number</td>
<td>GW2A-LV18PG256C8/I7</td>
</tr>
<tr>
<td class="label">Device</td>
<td>GW2A-18</td>
</tr>
<tr>
<td class="label">Device Version</td>
<td>C</td>
</tr>
<tr>
<td class="label">Created Time</td>
<td>Wed Jul 30 11:37:29 2025
</td>
</tr>
<tr>
<td class="label">Legal Announcement</td>
<td>Copyright (C)2014-2025 Gowin Semiconductor Corporation. ALL rights reserved.</td>
</tr>
</table>
<h1><a name="summary">Synthesis Details</a></h1>
<table class="summary_table">
<tr>
<td class="label">Top Level Module</td>
<td>gw_gao</td>
</tr>
<tr>
<td class="label">Synthesis Process</td>
<td>Running parser:<br/>&nbsp;&nbsp;&nbsp;&nbsp;CPU time = 0h 0m 0.14s, Elapsed time = 0h 0m 0.161s, Peak memory usage = 29.395MB<br/>Running netlist conversion:<br/>&nbsp;&nbsp;&nbsp;&nbsp;CPU time = 0h 0m 0.015s, Elapsed time = 0h 0m 0.002s, Peak memory usage = 29.395MB<br/>Running device independent optimization:<br/>&nbsp;&nbsp;&nbsp;&nbsp;Optimizing Phase 0: CPU time = 0h 0m 0.015s, Elapsed time = 0h 0m 0.016s, Peak memory usage = 29.395MB<br/>&nbsp;&nbsp;&nbsp;&nbsp;Optimizing Phase 1: CPU time = 0h 0m 0s, Elapsed time = 0h 0m 0.008s, Peak memory usage = 29.395MB<br/>&nbsp;&nbsp;&nbsp;&nbsp;Optimizing Phase 2: CPU time = 0h 0m 0.015s, Elapsed time = 0h 0m 0.02s, Peak memory usage = 29.395MB<br/>Running inference:<br/>&nbsp;&nbsp;&nbsp;&nbsp;Inferring Phase 0: CPU time = 0h 0m 0.015s, Elapsed time = 0h 0m 0.005s, Peak memory usage = 29.395MB<br/>&nbsp;&nbsp;&nbsp;&nbsp;Inferring Phase 1: CPU time = 0h 0m 0s, Elapsed time = 0h 0m 0s, Peak memory usage = 29.395MB<br/>&nbsp;&nbsp;&nbsp;&nbsp;Inferring Phase 2: CPU time = 0h 0m 0s, Elapsed time = 0h 0m 0.001s, Peak memory usage = 29.395MB<br/>&nbsp;&nbsp;&nbsp;&nbsp;Inferring Phase 3: CPU time = 0h 0m 0s, Elapsed time = 0h 0m 0.001s, Peak memory usage = 29.395MB<br/>Running technical mapping:<br/>&nbsp;&nbsp;&nbsp;&nbsp;Tech-Mapping Phase 0: CPU time = 0h 0m 0.015s, Elapsed time = 0h 0m 0.016s, Peak memory usage = 29.395MB<br/>&nbsp;&nbsp;&nbsp;&nbsp;Tech-Mapping Phase 1: CPU time = 0h 0m 0s, Elapsed time = 0h 0m 0.005s, Peak memory usage = 29.395MB<br/>&nbsp;&nbsp;&nbsp;&nbsp;Tech-Mapping Phase 2: CPU time = 0h 0m 0.015s, Elapsed time = 0h 0m 0.003s, Peak memory usage = 29.395MB<br/>&nbsp;&nbsp;&nbsp;&nbsp;Tech-Mapping Phase 3: CPU time = 0h 0m 1s, Elapsed time = 0h 0m 1s, Peak memory usage = 58.312MB<br/>&nbsp;&nbsp;&nbsp;&nbsp;Tech-Mapping Phase 4: CPU time = 0h 0m 0.046s, Elapsed time = 0h 0m 0.066s, Peak memory usage = 58.312MB<br/>Generate output files:<br/>&nbsp;&nbsp;&nbsp;&nbsp;CPU time = 0h 0m 0.015s, Elapsed time = 0h 0m 0.023s, Peak memory usage = 58.312MB<br/></td>
</tr>
<tr>
<td class="label">Total Time and Memory Usage</td>
<td>CPU time = 0h 0m 1s, Elapsed time = 0h 0m 1s, Peak memory usage = 58.312MB</td>
</tr>
</table>
<h1><a name="resource">Resource</a></h1>
<h2><a name="usage">Resource Usage Summary</a></h2>
<table class="summary_table">
<tr>
<td class="label"><b>Resource</b></td>
<td><b>Usage</b></td>
</tr>
<tr>
<td class="label"><b>I/O Port </b></td>
<td>67</td>
</tr>
<tr>
<td class="label"><b>I/O Buf </b></td>
<td>36</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspIBUF</td>
<td>35</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspOBUF</td>
<td>1</td>
</tr>
<tr>
<td class="label"><b>Register </b></td>
<td>384</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspDFF</td>
<td>73</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspDFFR</td>
<td>1</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspDFFP</td>
<td>3</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspDFFPE</td>
<td>33</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspDFFC</td>
<td>30</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspDFFCE</td>
<td>244</td>
</tr>
<tr>
<td class="label"><b>LUT </b></td>
<td>457</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspLUT2</td>
<td>46</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspLUT3</td>
<td>93</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspLUT4</td>
<td>318</td>
</tr>
<tr>
<td class="label"><b>MUX </b></td>
<td>1</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspMUX16</td>
<td>1</td>
</tr>
<tr>
<td class="label"><b>ALU </b></td>
<td>15</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspALU</td>
<td>15</td>
</tr>
<tr>
<td class="label"><b>INV </b></td>
<td>4</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspINV</td>
<td>4</td>
</tr>
<tr>
<td class="label"><b>BSRAM </b></td>
<td>9</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspSDPB</td>
<td>9</td>
</tr>
<tr>
<td class="label"><b>Black Box </b></td>
<td>1</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspGW_JTAG</td>
<td>1</td>
</tr>
</table>
<h2><a name="utilization">Resource Utilization Summary</a></h2>
<table class="summary_table">
<tr>
<td class="label"><b>Resource</b></td>
<td><b>Usage</b></td>
<td><b>Utilization</b></td>
</tr>
<tr>
<td class="label">Logic</td>
<td>484(469 LUT, 15 ALU) / 20736</td>
<td>3%</td>
</tr>
<tr>
<td class="label">Register</td>
<td>384 / 16173</td>
<td>3%</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp--Register as Latch</td>
<td>0 / 16173</td>
<td>0%</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp--Register as FF</td>
<td>384 / 16173</td>
<td>3%</td>
</tr>
<tr>
<td class="label">BSRAM</td>
<td>9 / 46</td>
<td>20%</td>
</tr>
</table>
<h1><a name="timing">Timing</a></h1>
<h2><a name="clock">Clock Summary:</a></h2>
<table class="summary_table">
<tr>
<th>NO.</th>
<th>Clock Name</th>
<th>Type</th>
<th>Period</th>
<th>Frequency(MHz)</th>
<th>Rise</th>
<th>Fall</th>
<th>Source</th>
<th>Master</th>
<th>Object</th>
</tr>
<tr>
<td>1</td>
<td>out_d_11[35]</td>
<td>Base</td>
<td>10.000</td>
<td>100.000</td>
<td>0.000</td>
<td>5.000</td>
<td> </td>
<td> </td>
<td>out_d_11[35]_ibuf/I </td>
</tr>
</table>
<h2><a name="performance">Max Frequency Summary:</a></h2>
<table class="summary_table">
<tr>
<th>NO.</th>
<th>Clock Name</th>
<th>Constraint</th>
<th>Actual Fmax</th>
<th>Logic Level</th>
<th>Entity</th>
</tr>
<tr>
<td>1</td>
<td>out_d_11[35]</td>
<td>100.000(MHz)</td>
<td>181.225(MHz)</td>
<td>6</td>
<td>TOP</td>
</tr>
</table>
<h2><a name="detail timing">Detail Timing Paths Information</a></h2>
<h3>Path&nbsp1</h3>
<b>Path Summary:</b></br>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>4.482</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>5.843</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>10.325</td>
</tr>
<tr>
<td class="label">From</td>
<td>u_la0_top/u_ao_mem_ctrl/capture_mem_addr_1_s1</td>
</tr>
<tr>
<td class="label">To</td>
<td>u_la0_top/u_ao_mem_ctrl/capture_mem_addr_9_s1</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>out_d_11[35][R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>out_d_11[35][R]</td>
</tr>
</table>
<b>Data Arrival Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>out_d_11[35]</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>out_d_11[35]_ibuf/I</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tINS</td>
<td>RR</td>
<td>145</td>
<td>out_d_11[35]_ibuf/O</td>
</tr>
<tr>
<td>0.360</td>
<td>0.360</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>u_la0_top/u_ao_mem_ctrl/capture_mem_addr_1_s1/CLK</td>
</tr>
<tr>
<td>0.592</td>
<td>0.232</td>
<td>tC2Q</td>
<td>RF</td>
<td>14</td>
<td>u_la0_top/u_ao_mem_ctrl/capture_mem_addr_1_s1/Q</td>
</tr>
<tr>
<td>1.066</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>u_la0_top/u_ao_mem_ctrl/n402_s2/I1</td>
</tr>
<tr>
<td>1.621</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>2</td>
<td>u_la0_top/u_ao_mem_ctrl/n402_s2/F</td>
</tr>
<tr>
<td>2.095</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>u_la0_top/u_ao_mem_ctrl/n399_s2/I3</td>
</tr>
<tr>
<td>2.466</td>
<td>0.371</td>
<td>tINS</td>
<td>FF</td>
<td>4</td>
<td>u_la0_top/u_ao_mem_ctrl/n399_s2/F</td>
</tr>
<tr>
<td>2.940</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>u_la0_top/u_ao_mem_ctrl/n396_s2/I3</td>
</tr>
<tr>
<td>3.311</td>
<td>0.371</td>
<td>tINS</td>
<td>FF</td>
<td>4</td>
<td>u_la0_top/u_ao_mem_ctrl/n396_s2/F</td>
</tr>
<tr>
<td>3.785</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>u_la0_top/u_ao_mem_ctrl/n395_s2/I1</td>
</tr>
<tr>
<td>4.340</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_la0_top/u_ao_mem_ctrl/n395_s2/F</td>
</tr>
<tr>
<td>4.814</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>u_la0_top/u_ao_mem_ctrl/n395_s1/I1</td>
</tr>
<tr>
<td>5.369</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_la0_top/u_ao_mem_ctrl/n395_s1/F</td>
</tr>
<tr>
<td>5.843</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>u_la0_top/u_ao_mem_ctrl/capture_mem_addr_9_s1/D</td>
</tr>
</table>
<b>Data Required Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>out_d_11[35]</td>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>out_d_11[35]_ibuf/I</td>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td>tINS</td>
<td>RR</td>
<td>145</td>
<td>out_d_11[35]_ibuf/O</td>
</tr>
<tr>
<td>10.360</td>
<td>0.360</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>u_la0_top/u_ao_mem_ctrl/capture_mem_addr_9_s1/CLK</td>
</tr>
<tr>
<td>10.325</td>
<td>-0.035</td>
<td>tSu</td>
<td> </td>
<td>1</td>
<td>u_la0_top/u_ao_mem_ctrl/capture_mem_addr_9_s1</td>
</tr>
</table>
<b>Path Statistics:</b>
<table class="summary_table">
<tr>
<td class="label">Clock Skew:</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Setup Relationship:</td>
<td>10.000</td>
</tr>
<tr>
<td class="label">Logic Level:</td>
<td>6</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay:</td><td> cell: 0.000, 0.000%; route: 0.360, 100.000%</td></tr>
<tr>
<td class="label">Arrival Data Path Delay:</td><td> cell: 2.407, 43.899%; route: 2.844, 51.870%; tC2Q: 0.232, 4.231%</td></tr>
<tr>
<td class="label">Required Clock Path Delay:</td><td> cell: 0.000, 0.000%; route: 0.360, 100.000%</td></tr>
</table>
<br/>
<h3>Path&nbsp2</h3>
<b>Path Summary:</b></br>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>4.540</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>5.785</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>10.325</td>
</tr>
<tr>
<td class="label">From</td>
<td>u_la0_top/capture_window_sel_2_s1</td>
</tr>
<tr>
<td class="label">To</td>
<td>u_la0_top/capture_window_sel_11_s1</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>out_d_11[35][R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>out_d_11[35][R]</td>
</tr>
</table>
<b>Data Arrival Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>out_d_11[35]</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>out_d_11[35]_ibuf/I</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tINS</td>
<td>RR</td>
<td>145</td>
<td>out_d_11[35]_ibuf/O</td>
</tr>
<tr>
<td>0.360</td>
<td>0.360</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>u_la0_top/capture_window_sel_2_s1/CLK</td>
</tr>
<tr>
<td>0.592</td>
<td>0.232</td>
<td>tC2Q</td>
<td>RF</td>
<td>12</td>
<td>u_la0_top/capture_window_sel_2_s1/Q</td>
</tr>
<tr>
<td>1.066</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>u_la0_top/n1888_s2/I1</td>
</tr>
<tr>
<td>1.621</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>4</td>
<td>u_la0_top/n1888_s2/F</td>
</tr>
<tr>
<td>2.095</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>u_la0_top/n1885_s2/I3</td>
</tr>
<tr>
<td>2.466</td>
<td>0.371</td>
<td>tINS</td>
<td>FF</td>
<td>3</td>
<td>u_la0_top/n1885_s2/F</td>
</tr>
<tr>
<td>2.940</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>u_la0_top/n1883_s2/I2</td>
</tr>
<tr>
<td>3.393</td>
<td>0.453</td>
<td>tINS</td>
<td>FF</td>
<td>3</td>
<td>u_la0_top/n1883_s2/F</td>
</tr>
<tr>
<td>3.867</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>u_la0_top/n1881_s2/I2</td>
</tr>
<tr>
<td>4.320</td>
<td>0.453</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_la0_top/n1881_s2/F</td>
</tr>
<tr>
<td>4.794</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>u_la0_top/n1881_s1/I0</td>
</tr>
<tr>
<td>5.311</td>
<td>0.517</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_la0_top/n1881_s1/F</td>
</tr>
<tr>
<td>5.785</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>u_la0_top/capture_window_sel_11_s1/D</td>
</tr>
</table>
<b>Data Required Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>out_d_11[35]</td>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>out_d_11[35]_ibuf/I</td>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td>tINS</td>
<td>RR</td>
<td>145</td>
<td>out_d_11[35]_ibuf/O</td>
</tr>
<tr>
<td>10.360</td>
<td>0.360</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>u_la0_top/capture_window_sel_11_s1/CLK</td>
</tr>
<tr>
<td>10.325</td>
<td>-0.035</td>
<td>tSu</td>
<td> </td>
<td>1</td>
<td>u_la0_top/capture_window_sel_11_s1</td>
</tr>
</table>
<b>Path Statistics:</b>
<table class="summary_table">
<tr>
<td class="label">Clock Skew:</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Setup Relationship:</td>
<td>10.000</td>
</tr>
<tr>
<td class="label">Logic Level:</td>
<td>6</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay:</td><td> cell: 0.000, 0.000%; route: 0.360, 100.000%</td></tr>
<tr>
<td class="label">Arrival Data Path Delay:</td><td> cell: 2.349, 43.300%; route: 2.844, 52.424%; tC2Q: 0.232, 4.276%</td></tr>
<tr>
<td class="label">Required Clock Path Delay:</td><td> cell: 0.000, 0.000%; route: 0.360, 100.000%</td></tr>
</table>
<br/>
<h3>Path&nbsp3</h3>
<b>Path Summary:</b></br>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>4.584</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>5.741</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>10.325</td>
</tr>
<tr>
<td class="label">From</td>
<td>u_la0_top/u_ao_mem_ctrl/capture_mem_addr_1_s1</td>
</tr>
<tr>
<td class="label">To</td>
<td>u_la0_top/u_ao_mem_ctrl/capture_mem_addr_10_s1</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>out_d_11[35][R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>out_d_11[35][R]</td>
</tr>
</table>
<b>Data Arrival Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>out_d_11[35]</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>out_d_11[35]_ibuf/I</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tINS</td>
<td>RR</td>
<td>145</td>
<td>out_d_11[35]_ibuf/O</td>
</tr>
<tr>
<td>0.360</td>
<td>0.360</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>u_la0_top/u_ao_mem_ctrl/capture_mem_addr_1_s1/CLK</td>
</tr>
<tr>
<td>0.592</td>
<td>0.232</td>
<td>tC2Q</td>
<td>RF</td>
<td>14</td>
<td>u_la0_top/u_ao_mem_ctrl/capture_mem_addr_1_s1/Q</td>
</tr>
<tr>
<td>1.066</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>u_la0_top/u_ao_mem_ctrl/n402_s2/I1</td>
</tr>
<tr>
<td>1.621</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>2</td>
<td>u_la0_top/u_ao_mem_ctrl/n402_s2/F</td>
</tr>
<tr>
<td>2.095</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>u_la0_top/u_ao_mem_ctrl/n399_s2/I3</td>
</tr>
<tr>
<td>2.466</td>
<td>0.371</td>
<td>tINS</td>
<td>FF</td>
<td>4</td>
<td>u_la0_top/u_ao_mem_ctrl/n399_s2/F</td>
</tr>
<tr>
<td>2.940</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>u_la0_top/u_ao_mem_ctrl/n396_s2/I3</td>
</tr>
<tr>
<td>3.311</td>
<td>0.371</td>
<td>tINS</td>
<td>FF</td>
<td>4</td>
<td>u_la0_top/u_ao_mem_ctrl/n396_s2/F</td>
</tr>
<tr>
<td>3.785</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>u_la0_top/u_ao_mem_ctrl/n394_s2/I2</td>
</tr>
<tr>
<td>4.238</td>
<td>0.453</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_la0_top/u_ao_mem_ctrl/n394_s2/F</td>
</tr>
<tr>
<td>4.712</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>u_la0_top/u_ao_mem_ctrl/n394_s1/I1</td>
</tr>
<tr>
<td>5.267</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_la0_top/u_ao_mem_ctrl/n394_s1/F</td>
</tr>
<tr>
<td>5.741</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>u_la0_top/u_ao_mem_ctrl/capture_mem_addr_10_s1/D</td>
</tr>
</table>
<b>Data Required Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>out_d_11[35]</td>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>out_d_11[35]_ibuf/I</td>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td>tINS</td>
<td>RR</td>
<td>145</td>
<td>out_d_11[35]_ibuf/O</td>
</tr>
<tr>
<td>10.360</td>
<td>0.360</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>u_la0_top/u_ao_mem_ctrl/capture_mem_addr_10_s1/CLK</td>
</tr>
<tr>
<td>10.325</td>
<td>-0.035</td>
<td>tSu</td>
<td> </td>
<td>1</td>
<td>u_la0_top/u_ao_mem_ctrl/capture_mem_addr_10_s1</td>
</tr>
</table>
<b>Path Statistics:</b>
<table class="summary_table">
<tr>
<td class="label">Clock Skew:</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Setup Relationship:</td>
<td>10.000</td>
</tr>
<tr>
<td class="label">Logic Level:</td>
<td>6</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay:</td><td> cell: 0.000, 0.000%; route: 0.360, 100.000%</td></tr>
<tr>
<td class="label">Arrival Data Path Delay:</td><td> cell: 2.305, 42.836%; route: 2.844, 52.853%; tC2Q: 0.232, 4.311%</td></tr>
<tr>
<td class="label">Required Clock Path Delay:</td><td> cell: 0.000, 0.000%; route: 0.360, 100.000%</td></tr>
</table>
<br/>
<h3>Path&nbsp4</h3>
<b>Path Summary:</b></br>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>4.666</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>5.659</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>10.325</td>
</tr>
<tr>
<td class="label">From</td>
<td>u_la0_top/u_ao_mem_ctrl/capture_mem_addr_1_s1</td>
</tr>
<tr>
<td class="label">To</td>
<td>u_la0_top/u_ao_mem_ctrl/capture_mem_addr_11_s1</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>out_d_11[35][R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>out_d_11[35][R]</td>
</tr>
</table>
<b>Data Arrival Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>out_d_11[35]</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>out_d_11[35]_ibuf/I</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tINS</td>
<td>RR</td>
<td>145</td>
<td>out_d_11[35]_ibuf/O</td>
</tr>
<tr>
<td>0.360</td>
<td>0.360</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>u_la0_top/u_ao_mem_ctrl/capture_mem_addr_1_s1/CLK</td>
</tr>
<tr>
<td>0.592</td>
<td>0.232</td>
<td>tC2Q</td>
<td>RF</td>
<td>14</td>
<td>u_la0_top/u_ao_mem_ctrl/capture_mem_addr_1_s1/Q</td>
</tr>
<tr>
<td>1.066</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>u_la0_top/u_ao_mem_ctrl/n402_s2/I1</td>
</tr>
<tr>
<td>1.621</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>2</td>
<td>u_la0_top/u_ao_mem_ctrl/n402_s2/F</td>
</tr>
<tr>
<td>2.095</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>u_la0_top/u_ao_mem_ctrl/n399_s2/I3</td>
</tr>
<tr>
<td>2.466</td>
<td>0.371</td>
<td>tINS</td>
<td>FF</td>
<td>4</td>
<td>u_la0_top/u_ao_mem_ctrl/n399_s2/F</td>
</tr>
<tr>
<td>2.940</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>u_la0_top/u_ao_mem_ctrl/n396_s2/I3</td>
</tr>
<tr>
<td>3.311</td>
<td>0.371</td>
<td>tINS</td>
<td>FF</td>
<td>4</td>
<td>u_la0_top/u_ao_mem_ctrl/n396_s2/F</td>
</tr>
<tr>
<td>3.785</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>u_la0_top/u_ao_mem_ctrl/n393_s3/I3</td>
</tr>
<tr>
<td>4.156</td>
<td>0.371</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_la0_top/u_ao_mem_ctrl/n393_s3/F</td>
</tr>
<tr>
<td>4.630</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>u_la0_top/u_ao_mem_ctrl/n393_s2/I1</td>
</tr>
<tr>
<td>5.185</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_la0_top/u_ao_mem_ctrl/n393_s2/F</td>
</tr>
<tr>
<td>5.659</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>u_la0_top/u_ao_mem_ctrl/capture_mem_addr_11_s1/D</td>
</tr>
</table>
<b>Data Required Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>out_d_11[35]</td>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>out_d_11[35]_ibuf/I</td>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td>tINS</td>
<td>RR</td>
<td>145</td>
<td>out_d_11[35]_ibuf/O</td>
</tr>
<tr>
<td>10.360</td>
<td>0.360</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>u_la0_top/u_ao_mem_ctrl/capture_mem_addr_11_s1/CLK</td>
</tr>
<tr>
<td>10.325</td>
<td>-0.035</td>
<td>tSu</td>
<td> </td>
<td>1</td>
<td>u_la0_top/u_ao_mem_ctrl/capture_mem_addr_11_s1</td>
</tr>
</table>
<b>Path Statistics:</b>
<table class="summary_table">
<tr>
<td class="label">Clock Skew:</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Setup Relationship:</td>
<td>10.000</td>
</tr>
<tr>
<td class="label">Logic Level:</td>
<td>6</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay:</td><td> cell: 0.000, 0.000%; route: 0.360, 100.000%</td></tr>
<tr>
<td class="label">Arrival Data Path Delay:</td><td> cell: 2.223, 41.951%; route: 2.844, 53.671%; tC2Q: 0.232, 4.378%</td></tr>
<tr>
<td class="label">Required Clock Path Delay:</td><td> cell: 0.000, 0.000%; route: 0.360, 100.000%</td></tr>
</table>
<br/>
<h3>Path&nbsp5</h3>
<b>Path Summary:</b></br>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>5.245</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>5.080</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>10.325</td>
</tr>
<tr>
<td class="label">From</td>
<td>u_la0_top/capture_window_sel_0_s3</td>
</tr>
<tr>
<td class="label">To</td>
<td>u_la0_top/u_ao_mem_ctrl/capture_mem_addr_start_reg_10_s0</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>out_d_11[35][R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>out_d_11[35][R]</td>
</tr>
</table>
<b>Data Arrival Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>out_d_11[35]</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>out_d_11[35]_ibuf/I</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tINS</td>
<td>RR</td>
<td>145</td>
<td>out_d_11[35]_ibuf/O</td>
</tr>
<tr>
<td>0.360</td>
<td>0.360</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>u_la0_top/capture_window_sel_0_s3/CLK</td>
</tr>
<tr>
<td>0.592</td>
<td>0.232</td>
<td>tC2Q</td>
<td>RF</td>
<td>16</td>
<td>u_la0_top/capture_window_sel_0_s3/Q</td>
</tr>
<tr>
<td>1.066</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>u_la0_top/u_ao_mem_ctrl/capture_mem_addr_start_10_s19/I1</td>
</tr>
<tr>
<td>1.621</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_la0_top/u_ao_mem_ctrl/capture_mem_addr_start_10_s19/F</td>
</tr>
<tr>
<td>2.095</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>u_la0_top/u_ao_mem_ctrl/capture_mem_addr_start_10_s17/I1</td>
</tr>
<tr>
<td>2.650</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_la0_top/u_ao_mem_ctrl/capture_mem_addr_start_10_s17/F</td>
</tr>
<tr>
<td>3.124</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>u_la0_top/u_ao_mem_ctrl/capture_mem_addr_start_10_s13/I2</td>
</tr>
<tr>
<td>3.577</td>
<td>0.453</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_la0_top/u_ao_mem_ctrl/capture_mem_addr_start_10_s13/F</td>
</tr>
<tr>
<td>4.051</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>u_la0_top/u_ao_mem_ctrl/capture_mem_addr_start_10_s11/I1</td>
</tr>
<tr>
<td>4.606</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_la0_top/u_ao_mem_ctrl/capture_mem_addr_start_10_s11/F</td>
</tr>
<tr>
<td>5.080</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>u_la0_top/u_ao_mem_ctrl/capture_mem_addr_start_reg_10_s0/D</td>
</tr>
</table>
<b>Data Required Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>out_d_11[35]</td>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>out_d_11[35]_ibuf/I</td>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td>tINS</td>
<td>RR</td>
<td>145</td>
<td>out_d_11[35]_ibuf/O</td>
</tr>
<tr>
<td>10.360</td>
<td>0.360</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>u_la0_top/u_ao_mem_ctrl/capture_mem_addr_start_reg_10_s0/CLK</td>
</tr>
<tr>
<td>10.325</td>
<td>-0.035</td>
<td>tSu</td>
<td> </td>
<td>1</td>
<td>u_la0_top/u_ao_mem_ctrl/capture_mem_addr_start_reg_10_s0</td>
</tr>
</table>
<b>Path Statistics:</b>
<table class="summary_table">
<tr>
<td class="label">Clock Skew:</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Setup Relationship:</td>
<td>10.000</td>
</tr>
<tr>
<td class="label">Logic Level:</td>
<td>5</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay:</td><td> cell: 0.000, 0.000%; route: 0.360, 100.000%</td></tr>
<tr>
<td class="label">Arrival Data Path Delay:</td><td> cell: 2.118, 44.873%; route: 2.370, 50.212%; tC2Q: 0.232, 4.915%</td></tr>
<tr>
<td class="label">Required Clock Path Delay:</td><td> cell: 0.000, 0.000%; route: 0.360, 100.000%</td></tr>
</table>
<br/>
</div><!-- content -->
</div><!-- main_wrapper -->
</body>
</html>
