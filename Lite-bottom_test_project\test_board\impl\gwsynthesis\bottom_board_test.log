GowinSynthesis start
Running parser ...
Analyzing Verilog file 'G:\Gowin\workspace\TangPrimer-20K-example-main\Lite-bottom_test_project\test_board\src\ddr3_memory_interface\ddr3_memory_interface.v'
Analyzing Verilog file 'G:\Gowin\workspace\TangPrimer-20K-example-main\Lite-bottom_test_project\test_board\src\ddr3_syn_top.v'
Analyzing Verilog file 'G:\Gowin\workspace\TangPrimer-20K-example-main\Lite-bottom_test_project\test_board\src\gowin_rpll\gowin_rpll.v'
Analyzing Verilog file 'G:\Gowin\workspace\TangPrimer-20K-example-main\Lite-bottom_test_project\test_board\src\key_blink.v'
Analyzing Verilog file 'G:\Gowin\workspace\TangPrimer-20K-example-main\Lite-bottom_test_project\test_board\src\tester.v'
Analyzing included file 'G:\Gowin\workspace\TangPrimer-20K-example-main\Lite-bottom_test_project\test_board\src\print.v'("G:\Gowin\workspace\TangPrimer-20K-example-main\Lite-bottom_test_project\test_board\src\tester.v":559)
Back to file 'G:\Gowin\workspace\TangPrimer-20K-example-main\Lite-bottom_test_project\test_board\src\tester.v'("G:\Gowin\workspace\TangPrimer-20K-example-main\Lite-bottom_test_project\test_board\src\tester.v":559)
Analyzing Verilog file 'G:\Gowin\workspace\TangPrimer-20K-example-main\Lite-bottom_test_project\test_board\src\top.v'
Analyzing Verilog file 'G:\Gowin\workspace\TangPrimer-20K-example-main\Lite-bottom_test_project\test_board\src\uart_tx_V2.v'
Compiling module 'top'("G:\Gowin\workspace\TangPrimer-20K-example-main\Lite-bottom_test_project\test_board\src\top.v":1)
ERROR (EX3937) : Instantiating unknown module 'sine_wave_generator'("G:\Gowin\workspace\TangPrimer-20K-example-main\Lite-bottom_test_project\test_board\src\top.v":64)
Module 'top' remains a black box due to errors in its contents("G:\Gowin\workspace\TangPrimer-20K-example-main\Lite-bottom_test_project\test_board\src\top.v":1)
GowinSynthesis finish
