<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>Hierarchy Module Resource</title>
<style type="text/css">
body { font-family: Verdana, Arial, sans-serif; font-size: 14px; }
div#main_wrapper{ width: 100%; }
h1 {text-align: center; }
h1 {margin-top: 36px; }
table, th, td { border: 1px solid #aaa; }
table { border-collapse:collapse; margin-top: 10px; margin-bottom: 20px; width: 100%; }
th, td { align = "center"; padding: 5px 2px 5px 5px; }
th { color: #fff; font-weight: bold; background-color: #0084ff; }
table td.label { width: 20%; white-space: nowrap; min-width: 20px; background-color: #dee8f4; }
</style>
</head>
<body>
<div id="main_wrapper">
<div id="content">
<h1>Hierarchy Module Resource</h1>
<table>
<tr>
<th class="label">MODULE NAME</th>
<th class="label">REG NUMBER</th>
<th class="label">ALU NUMBER</th>
<th class="label">LUT NUMBER</th>
<th class="label">DSP NUMBER</th>
<th class="label">BSRAM NUMBER</th>
<th class="label">SSRAM NUMBER</th>
<th class="label">ROM16 NUMBER</th>
</tr>
<tr>
<td class="label">top (G:/Gowin/workspace/TangPrimer-20K-example-main/Lite-bottom_test_project/test_board/src/top.v)</td>
<td align = "center">-</td>
<td align = "center">-</td>
<td align = "center">-</td>
<td align = "center">-</td>
<td align = "center">-</td>
<td align = "center">-</td>
<td align = "center">-</td>
</tr>
<td class="label">&nbsp&nbsp&nbsp&nbsp|--key_blink_inst
 (G:/Gowin/workspace/TangPrimer-20K-example-main/Lite-bottom_test_project/test_board/src/top.v)</td>
<td align = "center">129</td>
<td align = "center">-</td>
<td align = "center">242</td>
<td align = "center">-</td>
<td align = "center">-</td>
<td align = "center">-</td>
<td align = "center">-</td>
</tr>
<td class="label">&nbsp&nbsp&nbsp&nbsp|--ddr3_syn_top_inst
 (G:/Gowin/workspace/TangPrimer-20K-example-main/Lite-bottom_test_project/test_board/src/top.v)</td>
<td align = "center">-</td>
<td align = "center">-</td>
<td align = "center">-</td>
<td align = "center">-</td>
<td align = "center">-</td>
<td align = "center">-</td>
<td align = "center">-</td>
</tr>
<td class="label">&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp|--test
 (G:/Gowin/workspace/TangPrimer-20K-example-main/Lite-bottom_test_project/test_board/src/ddr3_syn_top.v)</td>
<td align = "center">613</td>
<td align = "center">193</td>
<td align = "center">965</td>
<td align = "center">-</td>
<td align = "center">5</td>
<td align = "center">-</td>
<td align = "center">-</td>
</tr>
<td class="label">&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp|--tx
 (G:/Gowin/workspace/TangPrimer-20K-example-main/Lite-bottom_test_project/test_board/src/print.v)</td>
<td align = "center">16</td>
<td align = "center">9</td>
<td align = "center">23</td>
<td align = "center">-</td>
<td align = "center">-</td>
<td align = "center">-</td>
<td align = "center">-</td>
</tr>
<td class="label">&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp|--pll
 (G:/Gowin/workspace/TangPrimer-20K-example-main/Lite-bottom_test_project/test_board/src/ddr3_syn_top.v)</td>
<td align = "center">-</td>
<td align = "center">-</td>
<td align = "center">-</td>
<td align = "center">-</td>
<td align = "center">-</td>
<td align = "center">-</td>
<td align = "center">-</td>
</tr>
<td class="label">&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp|--u_ddr3
 (G:/Gowin/workspace/TangPrimer-20K-example-main/Lite-bottom_test_project/test_board/src/ddr3_syn_top.v)</td>
<td align = "center">1288</td>
<td align = "center">102</td>
<td align = "center">1363</td>
<td align = "center">-</td>
<td align = "center">8</td>
<td align = "center">110</td>
<td align = "center">-</td>
</tr>
</table>
</div><!-- content -->
</div><!-- main_wrapper -->
</body>
</html>
