//Copyright (C)2014-2022 Gowin Semiconductor Corporation.
//All rights reserved.
//File Title: Post-PnR Simulation Model file
//GOWIN Version: V1.9.8.07 Education
//Created Time: Thu Aug 18 21:22:23 2022

`timescale 100 ps/100 ps
module video_fifo(
	Data,
	Reset,
	WrClk,
	RdClk,
	WrEn,
	RdEn,
	Q,
	Empty,
	Full
);
input [15:0] Data;
input Reset;
input WrClk;
input RdClk;
input WrEn;
input RdEn;
output [15:0] Q;
output Empty;
output Full;
wire [15:0] Data;
wire Empty;
wire Full;
wire GND;
wire [15:0] Q;
wire RdClk;
wire RdEn;
wire Reset;
wire VCC;
wire WrClk;
wire WrEn;
wire \fifo_inst/n33_5 ;
wire \fifo_inst/n37_3 ;
wire \fifo_inst/wfull_val ;
wire \fifo_inst/n537_3 ;
wire \fifo_inst/n760_3 ;
wire \fifo_inst/wfull_val1 ;
wire \fifo_inst/Equal.wbinnext_0_7 ;
wire \fifo_inst/Equal.rgraynext_5_4 ;
wire \fifo_inst/Equal.rgraynext_8_4 ;
wire \fifo_inst/Equal.rgraynext_10_4 ;
wire \fifo_inst/Equal.rgraynext_11_4 ;
wire \fifo_inst/Equal.rgraynext_13_4 ;
wire \fifo_inst/Equal.wgraynext_5_4 ;
wire \fifo_inst/Equal.wgraynext_7_4 ;
wire \fifo_inst/Equal.wgraynext_8_4 ;
wire \fifo_inst/Equal.wgraynext_10_4 ;
wire \fifo_inst/Equal.wgraynext_12_4 ;
wire \fifo_inst/Equal.wgraynext_13_4 ;
wire \fifo_inst/wfull_val_4 ;
wire \fifo_inst/wfull_val_5 ;
wire \fifo_inst/rbin_num_next_2_8 ;
wire \fifo_inst/rbin_num_next_4_8 ;
wire \fifo_inst/Equal.rgraynext_5_5 ;
wire \fifo_inst/Equal.rgraynext_10_5 ;
wire \fifo_inst/Equal.rgraynext_11_5 ;
wire \fifo_inst/Equal.wgraynext_2_5 ;
wire \fifo_inst/Equal.wgraynext_7_5 ;
wire \fifo_inst/Equal.wgraynext_8_5 ;
wire \fifo_inst/wfull_val_6 ;
wire \fifo_inst/wfull_val_7 ;
wire \fifo_inst/wfull_val_8 ;
wire \fifo_inst/wfull_val_9 ;
wire \fifo_inst/wfull_val_10 ;
wire \fifo_inst/wfull_val_11 ;
wire \fifo_inst/wfull_val_12 ;
wire \fifo_inst/wfull_val_13 ;
wire \fifo_inst/Equal.wgraynext_11_6 ;
wire \fifo_inst/Equal.wgraynext_2_7 ;
wire \fifo_inst/rbin_num_next_0_9 ;
wire \fifo_inst/rempty_val ;
wire \fifo_inst/rempty_val1 ;
wire \fifo_inst/wfull_val1_2 ;
wire \fifo_inst/wfull_val1_3 ;
wire \fifo_inst/Full_1 ;
wire \fifo_inst/Full_2 ;
wire \fifo_inst/wfull_val1_9 ;
wire \fifo_inst/n148_1_SUM ;
wire \fifo_inst/n148_3 ;
wire \fifo_inst/n149_1_SUM ;
wire \fifo_inst/n149_3 ;
wire \fifo_inst/n150_1_SUM ;
wire \fifo_inst/n150_3 ;
wire \fifo_inst/n151_1_SUM ;
wire \fifo_inst/n151_3 ;
wire \fifo_inst/n152_1_SUM ;
wire \fifo_inst/n152_3 ;
wire \fifo_inst/n153_1_SUM ;
wire \fifo_inst/n153_3 ;
wire \fifo_inst/n154_1_SUM ;
wire \fifo_inst/n154_3 ;
wire \fifo_inst/n155_1_SUM ;
wire \fifo_inst/n155_3 ;
wire \fifo_inst/n156_1_SUM ;
wire \fifo_inst/n156_3 ;
wire \fifo_inst/n157_1_SUM ;
wire \fifo_inst/n157_3 ;
wire \fifo_inst/n158_1_SUM ;
wire \fifo_inst/n158_3 ;
wire \fifo_inst/n159_1_SUM ;
wire \fifo_inst/n159_3 ;
wire \fifo_inst/n160_1_SUM ;
wire \fifo_inst/n160_3 ;
wire \fifo_inst/n161_1_SUM ;
wire \fifo_inst/n161_3 ;
wire \fifo_inst/wfull_val1_14 ;
wire [13:0] \fifo_inst/Equal.rgraynext ;
wire [13:0] \fifo_inst/Equal.wgraynext ;
wire [14:1] \fifo_inst/rbin_num_next ;
wire [14:1] \fifo_inst/Equal.wbinnext ;
wire [14:0] \fifo_inst/rbin_num ;
wire [13:0] \fifo_inst/rptr ;
wire [14:0] \fifo_inst/wptr ;
wire [13:0] \fifo_inst/Equal.wbin ;
wire [1:0] \fifo_inst/reset_r ;
wire [1:0] \fifo_inst/reset_w ;
wire [31:1] \fifo_inst/DO ;
wire [31:1] \fifo_inst/DO_0 ;
wire [31:1] \fifo_inst/DO_1 ;
wire [31:1] \fifo_inst/DO_2 ;
wire [31:1] \fifo_inst/DO_3 ;
wire [31:1] \fifo_inst/DO_4 ;
wire [31:1] \fifo_inst/DO_5 ;
wire [31:1] \fifo_inst/DO_6 ;
wire [31:1] \fifo_inst/DO_7 ;
wire [31:1] \fifo_inst/DO_8 ;
wire [31:1] \fifo_inst/DO_9 ;
wire [31:1] \fifo_inst/DO_10 ;
wire [31:1] \fifo_inst/DO_11 ;
wire [31:1] \fifo_inst/DO_12 ;
wire [31:1] \fifo_inst/DO_13 ;
wire [31:1] \fifo_inst/DO_14 ;
VCC VCC_cZ (
  .V(VCC)
);
GND GND_cZ (
  .G(GND)
);
GSR GSR (
	.GSRI(VCC)
);
LUT4 \fifo_inst/n33_s1  (
	.I0(\fifo_inst/Full_1 ),
	.I1(\fifo_inst/Full_2 ),
	.I2(\fifo_inst/wfull_val1_9 ),
	.I3(WrEn),
	.F(\fifo_inst/n33_5 )
);
defparam \fifo_inst/n33_s1 .INIT=16'h3500;
LUT2 \fifo_inst/n37_s0  (
	.I0(Empty),
	.I1(RdEn),
	.F(\fifo_inst/n37_3 )
);
defparam \fifo_inst/n37_s0 .INIT=4'h4;
LUT2 \fifo_inst/Equal.rgraynext_4_s0  (
	.I0(\fifo_inst/rbin_num_next [4]),
	.I1(\fifo_inst/rbin_num_next [5]),
	.F(\fifo_inst/Equal.rgraynext [4])
);
defparam \fifo_inst/Equal.rgraynext_4_s0 .INIT=4'h6;
LUT4 \fifo_inst/Equal.rgraynext_7_s0  (
	.I0(\fifo_inst/rbin_num [6]),
	.I1(\fifo_inst/Equal.rgraynext_5_4 ),
	.I2(\fifo_inst/rbin_num [7]),
	.I3(\fifo_inst/rbin_num [8]),
	.F(\fifo_inst/Equal.rgraynext [7])
);
defparam \fifo_inst/Equal.rgraynext_7_s0 .INIT=16'h07F8;
LUT3 \fifo_inst/Equal.rgraynext_8_s0  (
	.I0(\fifo_inst/rbin_num [8]),
	.I1(\fifo_inst/Equal.rgraynext_8_4 ),
	.I2(\fifo_inst/rbin_num [9]),
	.F(\fifo_inst/Equal.rgraynext [8])
);
defparam \fifo_inst/Equal.rgraynext_8_s0 .INIT=8'h1E;
LUT4 \fifo_inst/Equal.rgraynext_9_s0  (
	.I0(\fifo_inst/rbin_num [8]),
	.I1(\fifo_inst/Equal.rgraynext_8_4 ),
	.I2(\fifo_inst/rbin_num [9]),
	.I3(\fifo_inst/rbin_num [10]),
	.F(\fifo_inst/Equal.rgraynext [9])
);
defparam \fifo_inst/Equal.rgraynext_9_s0 .INIT=16'h07F8;
LUT3 \fifo_inst/Equal.rgraynext_10_s0  (
	.I0(\fifo_inst/rbin_num [10]),
	.I1(\fifo_inst/Equal.rgraynext_10_4 ),
	.I2(\fifo_inst/rbin_num [11]),
	.F(\fifo_inst/Equal.rgraynext [10])
);
defparam \fifo_inst/Equal.rgraynext_10_s0 .INIT=8'h1E;
LUT3 \fifo_inst/Equal.rgraynext_11_s0  (
	.I0(\fifo_inst/rbin_num [11]),
	.I1(\fifo_inst/Equal.rgraynext_11_4 ),
	.I2(\fifo_inst/rbin_num [12]),
	.F(\fifo_inst/Equal.rgraynext [11])
);
defparam \fifo_inst/Equal.rgraynext_11_s0 .INIT=8'h1E;
LUT4 \fifo_inst/Equal.rgraynext_12_s0  (
	.I0(\fifo_inst/rbin_num [11]),
	.I1(\fifo_inst/Equal.rgraynext_11_4 ),
	.I2(\fifo_inst/rbin_num [12]),
	.I3(\fifo_inst/rbin_num [13]),
	.F(\fifo_inst/Equal.rgraynext [12])
);
defparam \fifo_inst/Equal.rgraynext_12_s0 .INIT=16'h07F8;
LUT4 \fifo_inst/Equal.rgraynext_13_s0  (
	.I0(\fifo_inst/Equal.rgraynext_11_4 ),
	.I1(\fifo_inst/Equal.rgraynext_13_4 ),
	.I2(\fifo_inst/rbin_num [13]),
	.I3(\fifo_inst/rbin_num [14]),
	.F(\fifo_inst/Equal.rgraynext [13])
);
defparam \fifo_inst/Equal.rgraynext_13_s0 .INIT=16'h07F8;
LUT3 \fifo_inst/Equal.wgraynext_2_s0  (
	.I0(\fifo_inst/Equal.wbinnext [2]),
	.I1(\fifo_inst/Equal.wgraynext_2_7 ),
	.I2(\fifo_inst/Equal.wbin [3]),
	.F(\fifo_inst/Equal.wgraynext [2])
);
defparam \fifo_inst/Equal.wgraynext_2_s0 .INIT=8'h1E;
LUT3 \fifo_inst/Equal.wgraynext_3_s0  (
	.I0(\fifo_inst/Equal.wbin [3]),
	.I1(\fifo_inst/Equal.wgraynext_2_7 ),
	.I2(\fifo_inst/Equal.wbin [4]),
	.F(\fifo_inst/Equal.wgraynext [3])
);
defparam \fifo_inst/Equal.wgraynext_3_s0 .INIT=8'h1E;
LUT4 \fifo_inst/Equal.wgraynext_4_s0  (
	.I0(\fifo_inst/Equal.wbin [3]),
	.I1(\fifo_inst/Equal.wgraynext_2_7 ),
	.I2(\fifo_inst/Equal.wbin [4]),
	.I3(\fifo_inst/Equal.wbin [5]),
	.F(\fifo_inst/Equal.wgraynext [4])
);
defparam \fifo_inst/Equal.wgraynext_4_s0 .INIT=16'h07F8;
LUT3 \fifo_inst/Equal.wgraynext_5_s0  (
	.I0(\fifo_inst/Equal.wbin [5]),
	.I1(\fifo_inst/Equal.wgraynext_5_4 ),
	.I2(\fifo_inst/Equal.wbin [6]),
	.F(\fifo_inst/Equal.wgraynext [5])
);
defparam \fifo_inst/Equal.wgraynext_5_s0 .INIT=8'h1E;
LUT4 \fifo_inst/Equal.wgraynext_6_s0  (
	.I0(\fifo_inst/Equal.wbin [5]),
	.I1(\fifo_inst/Equal.wgraynext_5_4 ),
	.I2(\fifo_inst/Equal.wbin [6]),
	.I3(\fifo_inst/Equal.wbin [7]),
	.F(\fifo_inst/Equal.wgraynext [6])
);
defparam \fifo_inst/Equal.wgraynext_6_s0 .INIT=16'h07F8;
LUT3 \fifo_inst/Equal.wgraynext_7_s0  (
	.I0(\fifo_inst/Equal.wbin [7]),
	.I1(\fifo_inst/Equal.wgraynext_7_4 ),
	.I2(\fifo_inst/Equal.wbin [8]),
	.F(\fifo_inst/Equal.wgraynext [7])
);
defparam \fifo_inst/Equal.wgraynext_7_s0 .INIT=8'h1E;
LUT3 \fifo_inst/Equal.wgraynext_8_s0  (
	.I0(\fifo_inst/Equal.wbin [8]),
	.I1(\fifo_inst/Equal.wgraynext_8_4 ),
	.I2(\fifo_inst/Equal.wbin [9]),
	.F(\fifo_inst/Equal.wgraynext [8])
);
defparam \fifo_inst/Equal.wgraynext_8_s0 .INIT=8'h1E;
LUT4 \fifo_inst/Equal.wgraynext_9_s0  (
	.I0(\fifo_inst/Equal.wbin [8]),
	.I1(\fifo_inst/Equal.wgraynext_8_4 ),
	.I2(\fifo_inst/Equal.wbin [9]),
	.I3(\fifo_inst/Equal.wbin [10]),
	.F(\fifo_inst/Equal.wgraynext [9])
);
defparam \fifo_inst/Equal.wgraynext_9_s0 .INIT=16'h07F8;
LUT4 \fifo_inst/Equal.wgraynext_10_s0  (
	.I0(\fifo_inst/Equal.wgraynext_8_4 ),
	.I1(\fifo_inst/Equal.wgraynext_10_4 ),
	.I2(\fifo_inst/Equal.wbin [10]),
	.I3(\fifo_inst/Equal.wbin [11]),
	.F(\fifo_inst/Equal.wgraynext [10])
);
defparam \fifo_inst/Equal.wgraynext_10_s0 .INIT=16'h07F8;
LUT4 \fifo_inst/Equal.wgraynext_11_s0  (
	.I0(\fifo_inst/Equal.wgraynext_8_4 ),
	.I1(\fifo_inst/Equal.wgraynext_11_6 ),
	.I2(\fifo_inst/Equal.wbin [11]),
	.I3(\fifo_inst/Equal.wbin [12]),
	.F(\fifo_inst/Equal.wgraynext [11])
);
defparam \fifo_inst/Equal.wgraynext_11_s0 .INIT=16'h07F8;
LUT4 \fifo_inst/Equal.wgraynext_12_s0  (
	.I0(\fifo_inst/Equal.wgraynext_8_4 ),
	.I1(\fifo_inst/Equal.wgraynext_12_4 ),
	.I2(\fifo_inst/Equal.wbin [12]),
	.I3(\fifo_inst/Equal.wbin [13]),
	.F(\fifo_inst/Equal.wgraynext [12])
);
defparam \fifo_inst/Equal.wgraynext_12_s0 .INIT=16'h07F8;
LUT4 \fifo_inst/Equal.wgraynext_13_s0  (
	.I0(\fifo_inst/Equal.wgraynext_8_4 ),
	.I1(\fifo_inst/Equal.wgraynext_13_4 ),
	.I2(\fifo_inst/Equal.wbin [13]),
	.I3(\fifo_inst/wptr [14]),
	.F(\fifo_inst/Equal.wgraynext [13])
);
defparam \fifo_inst/Equal.wgraynext_13_s0 .INIT=16'h07F8;
LUT2 \fifo_inst/wfull_val_s0  (
	.I0(\fifo_inst/wfull_val_4 ),
	.I1(\fifo_inst/wfull_val_5 ),
	.F(\fifo_inst/wfull_val )
);
defparam \fifo_inst/wfull_val_s0 .INIT=4'h8;
LUT2 \fifo_inst/n537_s0  (
	.I0(\fifo_inst/rempty_val ),
	.I1(\fifo_inst/reset_r [1]),
	.F(\fifo_inst/n537_3 )
);
defparam \fifo_inst/n537_s0 .INIT=4'hE;
LUT3 \fifo_inst/n760_s0  (
	.I0(\fifo_inst/reset_w [1]),
	.I1(\fifo_inst/wfull_val_4 ),
	.I2(\fifo_inst/wfull_val_5 ),
	.F(\fifo_inst/n760_3 )
);
defparam \fifo_inst/n760_s0 .INIT=8'h40;
LUT3 \fifo_inst/wfull_val1_s6  (
	.I0(\fifo_inst/wfull_val1_3 ),
	.I1(\fifo_inst/wfull_val1_2 ),
	.I2(\fifo_inst/wfull_val1_9 ),
	.F(\fifo_inst/wfull_val1 )
);
defparam \fifo_inst/wfull_val1_s6 .INIT=8'hAC;
LUT3 \fifo_inst/Full_d_s  (
	.I0(\fifo_inst/Full_1 ),
	.I1(\fifo_inst/Full_2 ),
	.I2(\fifo_inst/wfull_val1_9 ),
	.F(Full)
);
defparam \fifo_inst/Full_d_s .INIT=8'hCA;
LUT2 \fifo_inst/rbin_num_next_2_s3  (
	.I0(\fifo_inst/rbin_num [2]),
	.I1(\fifo_inst/rbin_num_next_2_8 ),
	.F(\fifo_inst/rbin_num_next [2])
);
defparam \fifo_inst/rbin_num_next_2_s3 .INIT=4'h6;
LUT3 \fifo_inst/rbin_num_next_3_s3  (
	.I0(\fifo_inst/rbin_num [2]),
	.I1(\fifo_inst/rbin_num_next_2_8 ),
	.I2(\fifo_inst/rbin_num [3]),
	.F(\fifo_inst/rbin_num_next [3])
);
defparam \fifo_inst/rbin_num_next_3_s3 .INIT=8'h78;
LUT4 \fifo_inst/rbin_num_next_5_s3  (
	.I0(\fifo_inst/rbin_num [4]),
	.I1(\fifo_inst/rbin_num_next_2_8 ),
	.I2(\fifo_inst/rbin_num_next_4_8 ),
	.I3(\fifo_inst/rbin_num [5]),
	.F(\fifo_inst/rbin_num_next [5])
);
defparam \fifo_inst/rbin_num_next_5_s3 .INIT=16'h7F80;
LUT2 \fifo_inst/rbin_num_next_8_s3  (
	.I0(\fifo_inst/rbin_num [8]),
	.I1(\fifo_inst/Equal.rgraynext_8_4 ),
	.F(\fifo_inst/rbin_num_next [8])
);
defparam \fifo_inst/rbin_num_next_8_s3 .INIT=4'h6;
LUT3 \fifo_inst/rbin_num_next_9_s3  (
	.I0(\fifo_inst/rbin_num [8]),
	.I1(\fifo_inst/Equal.rgraynext_8_4 ),
	.I2(\fifo_inst/rbin_num [9]),
	.F(\fifo_inst/rbin_num_next [9])
);
defparam \fifo_inst/rbin_num_next_9_s3 .INIT=8'h78;
LUT2 \fifo_inst/rbin_num_next_10_s3  (
	.I0(\fifo_inst/rbin_num [10]),
	.I1(\fifo_inst/Equal.rgraynext_10_4 ),
	.F(\fifo_inst/rbin_num_next [10])
);
defparam \fifo_inst/rbin_num_next_10_s3 .INIT=4'h6;
LUT2 \fifo_inst/rbin_num_next_11_s3  (
	.I0(\fifo_inst/rbin_num [11]),
	.I1(\fifo_inst/Equal.rgraynext_11_4 ),
	.F(\fifo_inst/rbin_num_next [11])
);
defparam \fifo_inst/rbin_num_next_11_s3 .INIT=4'h6;
LUT3 \fifo_inst/rbin_num_next_12_s3  (
	.I0(\fifo_inst/rbin_num [11]),
	.I1(\fifo_inst/Equal.rgraynext_11_4 ),
	.I2(\fifo_inst/rbin_num [12]),
	.F(\fifo_inst/rbin_num_next [12])
);
defparam \fifo_inst/rbin_num_next_12_s3 .INIT=8'h78;
LUT4 \fifo_inst/rbin_num_next_14_s2  (
	.I0(\fifo_inst/rbin_num [13]),
	.I1(\fifo_inst/Equal.rgraynext_11_4 ),
	.I2(\fifo_inst/Equal.rgraynext_13_4 ),
	.I3(\fifo_inst/rbin_num [14]),
	.F(\fifo_inst/rbin_num_next [14])
);
defparam \fifo_inst/rbin_num_next_14_s2 .INIT=16'h7F80;
LUT2 \fifo_inst/Equal.wbinnext_0_s3  (
	.I0(\fifo_inst/Equal.wbin [0]),
	.I1(\fifo_inst/n33_5 ),
	.F(\fifo_inst/Equal.wbinnext_0_7 )
);
defparam \fifo_inst/Equal.wbinnext_0_s3 .INIT=4'h6;
LUT3 \fifo_inst/Equal.wbinnext_1_s3  (
	.I0(\fifo_inst/Equal.wbin [0]),
	.I1(\fifo_inst/n33_5 ),
	.I2(\fifo_inst/Equal.wbin [1]),
	.F(\fifo_inst/Equal.wbinnext [1])
);
defparam \fifo_inst/Equal.wbinnext_1_s3 .INIT=8'h78;
LUT4 \fifo_inst/Equal.wbinnext_2_s3  (
	.I0(\fifo_inst/Equal.wbin [0]),
	.I1(\fifo_inst/Equal.wbin [1]),
	.I2(\fifo_inst/n33_5 ),
	.I3(\fifo_inst/Equal.wbin [2]),
	.F(\fifo_inst/Equal.wbinnext [2])
);
defparam \fifo_inst/Equal.wbinnext_2_s3 .INIT=16'h7F80;
LUT2 \fifo_inst/Equal.wbinnext_3_s3  (
	.I0(\fifo_inst/Equal.wbin [3]),
	.I1(\fifo_inst/Equal.wgraynext_2_7 ),
	.F(\fifo_inst/Equal.wbinnext [3])
);
defparam \fifo_inst/Equal.wbinnext_3_s3 .INIT=4'h6;
LUT3 \fifo_inst/Equal.wbinnext_4_s3  (
	.I0(\fifo_inst/Equal.wbin [3]),
	.I1(\fifo_inst/Equal.wgraynext_2_7 ),
	.I2(\fifo_inst/Equal.wbin [4]),
	.F(\fifo_inst/Equal.wbinnext [4])
);
defparam \fifo_inst/Equal.wbinnext_4_s3 .INIT=8'h78;
LUT2 \fifo_inst/Equal.wbinnext_5_s3  (
	.I0(\fifo_inst/Equal.wbin [5]),
	.I1(\fifo_inst/Equal.wgraynext_5_4 ),
	.F(\fifo_inst/Equal.wbinnext [5])
);
defparam \fifo_inst/Equal.wbinnext_5_s3 .INIT=4'h6;
LUT3 \fifo_inst/Equal.wbinnext_6_s3  (
	.I0(\fifo_inst/Equal.wbin [5]),
	.I1(\fifo_inst/Equal.wgraynext_5_4 ),
	.I2(\fifo_inst/Equal.wbin [6]),
	.F(\fifo_inst/Equal.wbinnext [6])
);
defparam \fifo_inst/Equal.wbinnext_6_s3 .INIT=8'h78;
LUT2 \fifo_inst/Equal.wbinnext_7_s3  (
	.I0(\fifo_inst/Equal.wbin [7]),
	.I1(\fifo_inst/Equal.wgraynext_7_4 ),
	.F(\fifo_inst/Equal.wbinnext [7])
);
defparam \fifo_inst/Equal.wbinnext_7_s3 .INIT=4'h6;
LUT2 \fifo_inst/Equal.wbinnext_8_s3  (
	.I0(\fifo_inst/Equal.wbin [8]),
	.I1(\fifo_inst/Equal.wgraynext_8_4 ),
	.F(\fifo_inst/Equal.wbinnext [8])
);
defparam \fifo_inst/Equal.wbinnext_8_s3 .INIT=4'h6;
LUT3 \fifo_inst/Equal.wbinnext_9_s3  (
	.I0(\fifo_inst/Equal.wbin [8]),
	.I1(\fifo_inst/Equal.wgraynext_8_4 ),
	.I2(\fifo_inst/Equal.wbin [9]),
	.F(\fifo_inst/Equal.wbinnext [9])
);
defparam \fifo_inst/Equal.wbinnext_9_s3 .INIT=8'h78;
LUT3 \fifo_inst/Equal.wbinnext_11_s3  (
	.I0(\fifo_inst/Equal.wgraynext_8_4 ),
	.I1(\fifo_inst/Equal.wgraynext_11_6 ),
	.I2(\fifo_inst/Equal.wbin [11]),
	.F(\fifo_inst/Equal.wbinnext [11])
);
defparam \fifo_inst/Equal.wbinnext_11_s3 .INIT=8'h78;
LUT3 \fifo_inst/Equal.wbinnext_12_s3  (
	.I0(\fifo_inst/Equal.wgraynext_8_4 ),
	.I1(\fifo_inst/Equal.wgraynext_12_4 ),
	.I2(\fifo_inst/Equal.wbin [12]),
	.F(\fifo_inst/Equal.wbinnext [12])
);
defparam \fifo_inst/Equal.wbinnext_12_s3 .INIT=8'h78;
LUT4 \fifo_inst/Equal.wbinnext_14_s2  (
	.I0(\fifo_inst/Equal.wbin [13]),
	.I1(\fifo_inst/Equal.wgraynext_8_4 ),
	.I2(\fifo_inst/Equal.wgraynext_13_4 ),
	.I3(\fifo_inst/wptr [14]),
	.F(\fifo_inst/Equal.wbinnext [14])
);
defparam \fifo_inst/Equal.wbinnext_14_s2 .INIT=16'h7F80;
LUT2 \fifo_inst/Equal.rgraynext_5_s1  (
	.I0(\fifo_inst/rbin_num_next_2_8 ),
	.I1(\fifo_inst/Equal.rgraynext_5_5 ),
	.F(\fifo_inst/Equal.rgraynext_5_4 )
);
defparam \fifo_inst/Equal.rgraynext_5_s1 .INIT=4'h8;
LUT4 \fifo_inst/Equal.rgraynext_8_s1  (
	.I0(\fifo_inst/rbin_num [6]),
	.I1(\fifo_inst/rbin_num [7]),
	.I2(\fifo_inst/rbin_num_next_2_8 ),
	.I3(\fifo_inst/Equal.rgraynext_5_5 ),
	.F(\fifo_inst/Equal.rgraynext_8_4 )
);
defparam \fifo_inst/Equal.rgraynext_8_s1 .INIT=16'h8000;
LUT4 \fifo_inst/Equal.rgraynext_10_s1  (
	.I0(\fifo_inst/rbin_num [9]),
	.I1(\fifo_inst/rbin_num_next_2_8 ),
	.I2(\fifo_inst/Equal.rgraynext_5_5 ),
	.I3(\fifo_inst/Equal.rgraynext_10_5 ),
	.F(\fifo_inst/Equal.rgraynext_10_4 )
);
defparam \fifo_inst/Equal.rgraynext_10_s1 .INIT=16'h8000;
LUT4 \fifo_inst/Equal.rgraynext_11_s1  (
	.I0(\fifo_inst/rbin_num_next_2_8 ),
	.I1(\fifo_inst/Equal.rgraynext_5_5 ),
	.I2(\fifo_inst/Equal.rgraynext_10_5 ),
	.I3(\fifo_inst/Equal.rgraynext_11_5 ),
	.F(\fifo_inst/Equal.rgraynext_11_4 )
);
defparam \fifo_inst/Equal.rgraynext_11_s1 .INIT=16'h8000;
LUT2 \fifo_inst/Equal.rgraynext_13_s1  (
	.I0(\fifo_inst/rbin_num [11]),
	.I1(\fifo_inst/rbin_num [12]),
	.F(\fifo_inst/Equal.rgraynext_13_4 )
);
defparam \fifo_inst/Equal.rgraynext_13_s1 .INIT=4'h8;
LUT4 \fifo_inst/Equal.wgraynext_5_s1  (
	.I0(Full),
	.I1(\fifo_inst/Equal.wbin [4]),
	.I2(\fifo_inst/Equal.wbin [3]),
	.I3(\fifo_inst/Equal.wgraynext_2_5 ),
	.F(\fifo_inst/Equal.wgraynext_5_4 )
);
defparam \fifo_inst/Equal.wgraynext_5_s1 .INIT=16'h4000;
LUT4 \fifo_inst/Equal.wgraynext_7_s1  (
	.I0(Full),
	.I1(\fifo_inst/Equal.wbin [6]),
	.I2(\fifo_inst/Equal.wgraynext_2_5 ),
	.I3(\fifo_inst/Equal.wgraynext_7_5 ),
	.F(\fifo_inst/Equal.wgraynext_7_4 )
);
defparam \fifo_inst/Equal.wgraynext_7_s1 .INIT=16'h4000;
LUT4 \fifo_inst/Equal.wgraynext_8_s1  (
	.I0(Full),
	.I1(\fifo_inst/Equal.wgraynext_2_5 ),
	.I2(\fifo_inst/Equal.wgraynext_7_5 ),
	.I3(\fifo_inst/Equal.wgraynext_8_5 ),
	.F(\fifo_inst/Equal.wgraynext_8_4 )
);
defparam \fifo_inst/Equal.wgraynext_8_s1 .INIT=16'h4000;
LUT2 \fifo_inst/Equal.wgraynext_10_s1  (
	.I0(\fifo_inst/Equal.wbin [8]),
	.I1(\fifo_inst/Equal.wbin [9]),
	.F(\fifo_inst/Equal.wgraynext_10_4 )
);
defparam \fifo_inst/Equal.wgraynext_10_s1 .INIT=4'h8;
LUT4 \fifo_inst/Equal.wgraynext_12_s1  (
	.I0(\fifo_inst/Equal.wbin [8]),
	.I1(\fifo_inst/Equal.wbin [9]),
	.I2(\fifo_inst/Equal.wbin [10]),
	.I3(\fifo_inst/Equal.wbin [11]),
	.F(\fifo_inst/Equal.wgraynext_12_4 )
);
defparam \fifo_inst/Equal.wgraynext_12_s1 .INIT=16'h8000;
LUT2 \fifo_inst/Equal.wgraynext_13_s1  (
	.I0(\fifo_inst/Equal.wbin [12]),
	.I1(\fifo_inst/Equal.wgraynext_12_4 ),
	.F(\fifo_inst/Equal.wgraynext_13_4 )
);
defparam \fifo_inst/Equal.wgraynext_13_s1 .INIT=4'h8;
LUT4 \fifo_inst/wfull_val_s1  (
	.I0(\fifo_inst/wfull_val_6 ),
	.I1(\fifo_inst/wfull_val_7 ),
	.I2(\fifo_inst/wfull_val_8 ),
	.I3(\fifo_inst/wfull_val_9 ),
	.F(\fifo_inst/wfull_val_4 )
);
defparam \fifo_inst/wfull_val_s1 .INIT=16'h8000;
LUT4 \fifo_inst/wfull_val_s2  (
	.I0(\fifo_inst/wfull_val_10 ),
	.I1(\fifo_inst/wfull_val_11 ),
	.I2(\fifo_inst/wfull_val_12 ),
	.I3(\fifo_inst/wfull_val_13 ),
	.F(\fifo_inst/wfull_val_5 )
);
defparam \fifo_inst/wfull_val_s2 .INIT=16'h8000;
LUT4 \fifo_inst/rbin_num_next_2_s4  (
	.I0(Empty),
	.I1(RdEn),
	.I2(\fifo_inst/rbin_num [0]),
	.I3(\fifo_inst/rbin_num [1]),
	.F(\fifo_inst/rbin_num_next_2_8 )
);
defparam \fifo_inst/rbin_num_next_2_s4 .INIT=16'h4000;
LUT2 \fifo_inst/rbin_num_next_4_s4  (
	.I0(\fifo_inst/rbin_num [2]),
	.I1(\fifo_inst/rbin_num [3]),
	.F(\fifo_inst/rbin_num_next_4_8 )
);
defparam \fifo_inst/rbin_num_next_4_s4 .INIT=4'h8;
LUT4 \fifo_inst/Equal.rgraynext_5_s2  (
	.I0(\fifo_inst/rbin_num [2]),
	.I1(\fifo_inst/rbin_num [3]),
	.I2(\fifo_inst/rbin_num [4]),
	.I3(\fifo_inst/rbin_num [5]),
	.F(\fifo_inst/Equal.rgraynext_5_5 )
);
defparam \fifo_inst/Equal.rgraynext_5_s2 .INIT=16'h8000;
LUT3 \fifo_inst/Equal.rgraynext_10_s2  (
	.I0(\fifo_inst/rbin_num [6]),
	.I1(\fifo_inst/rbin_num [7]),
	.I2(\fifo_inst/rbin_num [8]),
	.F(\fifo_inst/Equal.rgraynext_10_5 )
);
defparam \fifo_inst/Equal.rgraynext_10_s2 .INIT=8'h80;
LUT2 \fifo_inst/Equal.rgraynext_11_s2  (
	.I0(\fifo_inst/rbin_num [9]),
	.I1(\fifo_inst/rbin_num [10]),
	.F(\fifo_inst/Equal.rgraynext_11_5 )
);
defparam \fifo_inst/Equal.rgraynext_11_s2 .INIT=4'h8;
LUT4 \fifo_inst/Equal.wgraynext_2_s2  (
	.I0(WrEn),
	.I1(\fifo_inst/Equal.wbin [0]),
	.I2(\fifo_inst/Equal.wbin [1]),
	.I3(\fifo_inst/Equal.wbin [2]),
	.F(\fifo_inst/Equal.wgraynext_2_5 )
);
defparam \fifo_inst/Equal.wgraynext_2_s2 .INIT=16'h8000;
LUT3 \fifo_inst/Equal.wgraynext_7_s2  (
	.I0(\fifo_inst/Equal.wbin [3]),
	.I1(\fifo_inst/Equal.wbin [4]),
	.I2(\fifo_inst/Equal.wbin [5]),
	.F(\fifo_inst/Equal.wgraynext_7_5 )
);
defparam \fifo_inst/Equal.wgraynext_7_s2 .INIT=8'h80;
LUT2 \fifo_inst/Equal.wgraynext_8_s2  (
	.I0(\fifo_inst/Equal.wbin [6]),
	.I1(\fifo_inst/Equal.wbin [7]),
	.F(\fifo_inst/Equal.wgraynext_8_5 )
);
defparam \fifo_inst/Equal.wgraynext_8_s2 .INIT=4'h8;
LUT4 \fifo_inst/wfull_val_s3  (
	.I0(\fifo_inst/wptr [8]),
	.I1(\fifo_inst/rptr [8]),
	.I2(\fifo_inst/wptr [9]),
	.I3(\fifo_inst/rptr [9]),
	.F(\fifo_inst/wfull_val_6 )
);
defparam \fifo_inst/wfull_val_s3 .INIT=16'h9009;
LUT4 \fifo_inst/wfull_val_s4  (
	.I0(\fifo_inst/wptr [10]),
	.I1(\fifo_inst/rptr [10]),
	.I2(\fifo_inst/wptr [14]),
	.I3(\fifo_inst/rbin_num [14]),
	.F(\fifo_inst/wfull_val_7 )
);
defparam \fifo_inst/wfull_val_s4 .INIT=16'h0990;
LUT4 \fifo_inst/wfull_val_s5  (
	.I0(\fifo_inst/wptr [0]),
	.I1(\fifo_inst/rptr [0]),
	.I2(\fifo_inst/wptr [1]),
	.I3(\fifo_inst/rptr [1]),
	.F(\fifo_inst/wfull_val_8 )
);
defparam \fifo_inst/wfull_val_s5 .INIT=16'h9009;
LUT4 \fifo_inst/wfull_val_s6  (
	.I0(\fifo_inst/wptr [6]),
	.I1(\fifo_inst/rptr [6]),
	.I2(\fifo_inst/wptr [11]),
	.I3(\fifo_inst/rptr [11]),
	.F(\fifo_inst/wfull_val_9 )
);
defparam \fifo_inst/wfull_val_s6 .INIT=16'h9009;
LUT2 \fifo_inst/wfull_val_s7  (
	.I0(\fifo_inst/wptr [3]),
	.I1(\fifo_inst/rptr [3]),
	.F(\fifo_inst/wfull_val_10 )
);
defparam \fifo_inst/wfull_val_s7 .INIT=4'h9;
LUT4 \fifo_inst/wfull_val_s8  (
	.I0(\fifo_inst/wptr [5]),
	.I1(\fifo_inst/rptr [5]),
	.I2(\fifo_inst/wptr [13]),
	.I3(\fifo_inst/rptr [13]),
	.F(\fifo_inst/wfull_val_11 )
);
defparam \fifo_inst/wfull_val_s8 .INIT=16'h0990;
LUT4 \fifo_inst/wfull_val_s9  (
	.I0(\fifo_inst/wptr [4]),
	.I1(\fifo_inst/rptr [4]),
	.I2(\fifo_inst/wptr [7]),
	.I3(\fifo_inst/rptr [7]),
	.F(\fifo_inst/wfull_val_12 )
);
defparam \fifo_inst/wfull_val_s9 .INIT=16'h9009;
LUT4 \fifo_inst/wfull_val_s10  (
	.I0(\fifo_inst/wptr [2]),
	.I1(\fifo_inst/rptr [2]),
	.I2(\fifo_inst/wptr [12]),
	.I3(\fifo_inst/rptr [12]),
	.F(\fifo_inst/wfull_val_13 )
);
defparam \fifo_inst/wfull_val_s10 .INIT=16'h9009;
LUT3 \fifo_inst/Equal.wgraynext_0_s1  (
	.I0(\fifo_inst/Equal.wbin [0]),
	.I1(\fifo_inst/n33_5 ),
	.I2(\fifo_inst/Equal.wbinnext [1]),
	.F(\fifo_inst/Equal.wgraynext [0])
);
defparam \fifo_inst/Equal.wgraynext_0_s1 .INIT=8'h96;
LUT4 \fifo_inst/rbin_num_next_4_s5  (
	.I0(\fifo_inst/rbin_num_next_2_8 ),
	.I1(\fifo_inst/rbin_num [2]),
	.I2(\fifo_inst/rbin_num [3]),
	.I3(\fifo_inst/rbin_num [4]),
	.F(\fifo_inst/rbin_num_next [4])
);
defparam \fifo_inst/rbin_num_next_4_s5 .INIT=16'h7F80;
LUT4 \fifo_inst/rbin_num_next_13_s4  (
	.I0(\fifo_inst/Equal.rgraynext_11_4 ),
	.I1(\fifo_inst/rbin_num [11]),
	.I2(\fifo_inst/rbin_num [12]),
	.I3(\fifo_inst/rbin_num [13]),
	.F(\fifo_inst/rbin_num_next [13])
);
defparam \fifo_inst/rbin_num_next_13_s4 .INIT=16'h7F80;
LUT4 \fifo_inst/Equal.wgraynext_1_s1  (
	.I0(\fifo_inst/Equal.wbin [0]),
	.I1(\fifo_inst/n33_5 ),
	.I2(\fifo_inst/Equal.wbin [1]),
	.I3(\fifo_inst/Equal.wbinnext [2]),
	.F(\fifo_inst/Equal.wgraynext [1])
);
defparam \fifo_inst/Equal.wgraynext_1_s1 .INIT=16'h8778;
LUT3 \fifo_inst/Equal.wgraynext_11_s2  (
	.I0(\fifo_inst/Equal.wbin [10]),
	.I1(\fifo_inst/Equal.wbin [8]),
	.I2(\fifo_inst/Equal.wbin [9]),
	.F(\fifo_inst/Equal.wgraynext_11_6 )
);
defparam \fifo_inst/Equal.wgraynext_11_s2 .INIT=8'h80;
LUT4 \fifo_inst/Equal.wbinnext_10_s4  (
	.I0(\fifo_inst/Equal.wgraynext_8_4 ),
	.I1(\fifo_inst/Equal.wbin [8]),
	.I2(\fifo_inst/Equal.wbin [9]),
	.I3(\fifo_inst/Equal.wbin [10]),
	.F(\fifo_inst/Equal.wbinnext [10])
);
defparam \fifo_inst/Equal.wbinnext_10_s4 .INIT=16'h7F80;
LUT4 \fifo_inst/Equal.wbinnext_13_s4  (
	.I0(\fifo_inst/Equal.wgraynext_8_4 ),
	.I1(\fifo_inst/Equal.wbin [12]),
	.I2(\fifo_inst/Equal.wgraynext_12_4 ),
	.I3(\fifo_inst/Equal.wbin [13]),
	.F(\fifo_inst/Equal.wbinnext [13])
);
defparam \fifo_inst/Equal.wbinnext_13_s4 .INIT=16'h7F80;
LUT3 \fifo_inst/Equal.rgraynext_2_s1  (
	.I0(\fifo_inst/rbin_num [2]),
	.I1(\fifo_inst/rbin_num_next_2_8 ),
	.I2(\fifo_inst/rbin_num_next [3]),
	.F(\fifo_inst/Equal.rgraynext [2])
);
defparam \fifo_inst/Equal.rgraynext_2_s1 .INIT=8'h96;
LUT3 \fifo_inst/Equal.rgraynext_1_s1  (
	.I0(\fifo_inst/rbin_num_next [1]),
	.I1(\fifo_inst/rbin_num [2]),
	.I2(\fifo_inst/rbin_num_next_2_8 ),
	.F(\fifo_inst/Equal.rgraynext [1])
);
defparam \fifo_inst/Equal.rgraynext_1_s1 .INIT=8'h96;
LUT4 \fifo_inst/Equal.rgraynext_3_s1  (
	.I0(\fifo_inst/rbin_num [2]),
	.I1(\fifo_inst/rbin_num_next_2_8 ),
	.I2(\fifo_inst/rbin_num [3]),
	.I3(\fifo_inst/rbin_num_next [4]),
	.F(\fifo_inst/Equal.rgraynext [3])
);
defparam \fifo_inst/Equal.rgraynext_3_s1 .INIT=16'h8778;
LUT4 \fifo_inst/rbin_num_next_7_s4  (
	.I0(\fifo_inst/rbin_num [6]),
	.I1(\fifo_inst/rbin_num_next_2_8 ),
	.I2(\fifo_inst/Equal.rgraynext_5_5 ),
	.I3(\fifo_inst/rbin_num [7]),
	.F(\fifo_inst/rbin_num_next [7])
);
defparam \fifo_inst/rbin_num_next_7_s4 .INIT=16'h7F80;
LUT3 \fifo_inst/rbin_num_next_6_s4  (
	.I0(\fifo_inst/rbin_num [6]),
	.I1(\fifo_inst/rbin_num_next_2_8 ),
	.I2(\fifo_inst/Equal.rgraynext_5_5 ),
	.F(\fifo_inst/rbin_num_next [6])
);
defparam \fifo_inst/rbin_num_next_6_s4 .INIT=8'h6A;
LUT4 \fifo_inst/Equal.rgraynext_6_s1  (
	.I0(\fifo_inst/rbin_num [6]),
	.I1(\fifo_inst/rbin_num_next_2_8 ),
	.I2(\fifo_inst/Equal.rgraynext_5_5 ),
	.I3(\fifo_inst/rbin_num [7]),
	.F(\fifo_inst/Equal.rgraynext [6])
);
defparam \fifo_inst/Equal.rgraynext_6_s1 .INIT=16'h15EA;
LUT4 \fifo_inst/Equal.rgraynext_5_s3  (
	.I0(\fifo_inst/rbin_num_next [5]),
	.I1(\fifo_inst/rbin_num_next_2_8 ),
	.I2(\fifo_inst/Equal.rgraynext_5_5 ),
	.I3(\fifo_inst/rbin_num [6]),
	.F(\fifo_inst/Equal.rgraynext [5])
);
defparam \fifo_inst/Equal.rgraynext_5_s3 .INIT=16'h15EA;
LUT4 \fifo_inst/Equal.wgraynext_2_s3  (
	.I0(\fifo_inst/Full_1 ),
	.I1(\fifo_inst/Full_2 ),
	.I2(\fifo_inst/wfull_val1_9 ),
	.I3(\fifo_inst/Equal.wgraynext_2_5 ),
	.F(\fifo_inst/Equal.wgraynext_2_7 )
);
defparam \fifo_inst/Equal.wgraynext_2_s3 .INIT=16'h3500;
LUT4 \fifo_inst/rbin_num_next_1_s4  (
	.I0(\fifo_inst/rbin_num [0]),
	.I1(Empty),
	.I2(RdEn),
	.I3(\fifo_inst/rbin_num [1]),
	.F(\fifo_inst/rbin_num_next [1])
);
defparam \fifo_inst/rbin_num_next_1_s4 .INIT=16'hDF20;
LUT3 \fifo_inst/rbin_num_next_0_s4  (
	.I0(\fifo_inst/rbin_num [0]),
	.I1(Empty),
	.I2(RdEn),
	.F(\fifo_inst/rbin_num_next_0_9 )
);
defparam \fifo_inst/rbin_num_next_0_s4 .INIT=8'h9A;
LUT3 \fifo_inst/rempty_val_s2  (
	.I0(\fifo_inst/wptr [14]),
	.I1(\fifo_inst/rbin_num [14]),
	.I2(\fifo_inst/n161_3 ),
	.F(\fifo_inst/rempty_val )
);
defparam \fifo_inst/rempty_val_s2 .INIT=8'h09;
LUT4 \fifo_inst/Equal.rgraynext_0_s1  (
	.I0(\fifo_inst/rbin_num_next [1]),
	.I1(\fifo_inst/rbin_num [0]),
	.I2(Empty),
	.I3(RdEn),
	.F(\fifo_inst/Equal.rgraynext [0])
);
defparam \fifo_inst/Equal.rgraynext_0_s1 .INIT=16'h6966;
DFFC \fifo_inst/rbin_num_14_s0  (
	.D(\fifo_inst/rbin_num_next [14]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(\fifo_inst/rbin_num [14])
);
defparam \fifo_inst/rbin_num_14_s0 .INIT=1'b0;
DFFC \fifo_inst/rbin_num_13_s0  (
	.D(\fifo_inst/rbin_num_next [13]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(\fifo_inst/rbin_num [13])
);
defparam \fifo_inst/rbin_num_13_s0 .INIT=1'b0;
DFFC \fifo_inst/rbin_num_12_s0  (
	.D(\fifo_inst/rbin_num_next [12]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(\fifo_inst/rbin_num [12])
);
defparam \fifo_inst/rbin_num_12_s0 .INIT=1'b0;
DFFC \fifo_inst/rbin_num_11_s0  (
	.D(\fifo_inst/rbin_num_next [11]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(\fifo_inst/rbin_num [11])
);
defparam \fifo_inst/rbin_num_11_s0 .INIT=1'b0;
DFFC \fifo_inst/rbin_num_10_s0  (
	.D(\fifo_inst/rbin_num_next [10]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(\fifo_inst/rbin_num [10])
);
defparam \fifo_inst/rbin_num_10_s0 .INIT=1'b0;
DFFC \fifo_inst/rbin_num_9_s0  (
	.D(\fifo_inst/rbin_num_next [9]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(\fifo_inst/rbin_num [9])
);
defparam \fifo_inst/rbin_num_9_s0 .INIT=1'b0;
DFFC \fifo_inst/rbin_num_8_s0  (
	.D(\fifo_inst/rbin_num_next [8]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(\fifo_inst/rbin_num [8])
);
defparam \fifo_inst/rbin_num_8_s0 .INIT=1'b0;
DFFC \fifo_inst/rbin_num_7_s0  (
	.D(\fifo_inst/rbin_num_next [7]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(\fifo_inst/rbin_num [7])
);
defparam \fifo_inst/rbin_num_7_s0 .INIT=1'b0;
DFFC \fifo_inst/rbin_num_6_s0  (
	.D(\fifo_inst/rbin_num_next [6]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(\fifo_inst/rbin_num [6])
);
defparam \fifo_inst/rbin_num_6_s0 .INIT=1'b0;
DFFC \fifo_inst/rbin_num_5_s0  (
	.D(\fifo_inst/rbin_num_next [5]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(\fifo_inst/rbin_num [5])
);
defparam \fifo_inst/rbin_num_5_s0 .INIT=1'b0;
DFFC \fifo_inst/rbin_num_4_s0  (
	.D(\fifo_inst/rbin_num_next [4]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(\fifo_inst/rbin_num [4])
);
defparam \fifo_inst/rbin_num_4_s0 .INIT=1'b0;
DFFC \fifo_inst/rbin_num_3_s0  (
	.D(\fifo_inst/rbin_num_next [3]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(\fifo_inst/rbin_num [3])
);
defparam \fifo_inst/rbin_num_3_s0 .INIT=1'b0;
DFFC \fifo_inst/rbin_num_2_s0  (
	.D(\fifo_inst/rbin_num_next [2]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(\fifo_inst/rbin_num [2])
);
defparam \fifo_inst/rbin_num_2_s0 .INIT=1'b0;
DFFC \fifo_inst/rbin_num_1_s0  (
	.D(\fifo_inst/rbin_num_next [1]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(\fifo_inst/rbin_num [1])
);
defparam \fifo_inst/rbin_num_1_s0 .INIT=1'b0;
DFFC \fifo_inst/rbin_num_0_s0  (
	.D(\fifo_inst/rbin_num_next_0_9 ),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(\fifo_inst/rbin_num [0])
);
defparam \fifo_inst/rbin_num_0_s0 .INIT=1'b0;
DFFC \fifo_inst/rptr_13_s0  (
	.D(\fifo_inst/Equal.rgraynext [13]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(\fifo_inst/rptr [13])
);
defparam \fifo_inst/rptr_13_s0 .INIT=1'b0;
DFFC \fifo_inst/rptr_12_s0  (
	.D(\fifo_inst/Equal.rgraynext [12]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(\fifo_inst/rptr [12])
);
defparam \fifo_inst/rptr_12_s0 .INIT=1'b0;
DFFC \fifo_inst/rptr_11_s0  (
	.D(\fifo_inst/Equal.rgraynext [11]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(\fifo_inst/rptr [11])
);
defparam \fifo_inst/rptr_11_s0 .INIT=1'b0;
DFFC \fifo_inst/rptr_10_s0  (
	.D(\fifo_inst/Equal.rgraynext [10]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(\fifo_inst/rptr [10])
);
defparam \fifo_inst/rptr_10_s0 .INIT=1'b0;
DFFC \fifo_inst/rptr_9_s0  (
	.D(\fifo_inst/Equal.rgraynext [9]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(\fifo_inst/rptr [9])
);
defparam \fifo_inst/rptr_9_s0 .INIT=1'b0;
DFFC \fifo_inst/rptr_8_s0  (
	.D(\fifo_inst/Equal.rgraynext [8]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(\fifo_inst/rptr [8])
);
defparam \fifo_inst/rptr_8_s0 .INIT=1'b0;
DFFC \fifo_inst/rptr_7_s0  (
	.D(\fifo_inst/Equal.rgraynext [7]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(\fifo_inst/rptr [7])
);
defparam \fifo_inst/rptr_7_s0 .INIT=1'b0;
DFFC \fifo_inst/rptr_6_s0  (
	.D(\fifo_inst/Equal.rgraynext [6]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(\fifo_inst/rptr [6])
);
defparam \fifo_inst/rptr_6_s0 .INIT=1'b0;
DFFC \fifo_inst/rptr_5_s0  (
	.D(\fifo_inst/Equal.rgraynext [5]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(\fifo_inst/rptr [5])
);
defparam \fifo_inst/rptr_5_s0 .INIT=1'b0;
DFFC \fifo_inst/rptr_4_s0  (
	.D(\fifo_inst/Equal.rgraynext [4]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(\fifo_inst/rptr [4])
);
defparam \fifo_inst/rptr_4_s0 .INIT=1'b0;
DFFC \fifo_inst/rptr_3_s0  (
	.D(\fifo_inst/Equal.rgraynext [3]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(\fifo_inst/rptr [3])
);
defparam \fifo_inst/rptr_3_s0 .INIT=1'b0;
DFFC \fifo_inst/rptr_2_s0  (
	.D(\fifo_inst/Equal.rgraynext [2]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(\fifo_inst/rptr [2])
);
defparam \fifo_inst/rptr_2_s0 .INIT=1'b0;
DFFC \fifo_inst/rptr_1_s0  (
	.D(\fifo_inst/Equal.rgraynext [1]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(\fifo_inst/rptr [1])
);
defparam \fifo_inst/rptr_1_s0 .INIT=1'b0;
DFFC \fifo_inst/rptr_0_s0  (
	.D(\fifo_inst/Equal.rgraynext [0]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(\fifo_inst/rptr [0])
);
defparam \fifo_inst/rptr_0_s0 .INIT=1'b0;
DFFC \fifo_inst/wptr_14_s0  (
	.D(\fifo_inst/Equal.wbinnext [14]),
	.CLK(WrClk),
	.CLEAR(\fifo_inst/reset_w [1]),
	.Q(\fifo_inst/wptr [14])
);
defparam \fifo_inst/wptr_14_s0 .INIT=1'b0;
DFFC \fifo_inst/wptr_13_s0  (
	.D(\fifo_inst/Equal.wgraynext [13]),
	.CLK(WrClk),
	.CLEAR(\fifo_inst/reset_w [1]),
	.Q(\fifo_inst/wptr [13])
);
defparam \fifo_inst/wptr_13_s0 .INIT=1'b0;
DFFC \fifo_inst/wptr_12_s0  (
	.D(\fifo_inst/Equal.wgraynext [12]),
	.CLK(WrClk),
	.CLEAR(\fifo_inst/reset_w [1]),
	.Q(\fifo_inst/wptr [12])
);
defparam \fifo_inst/wptr_12_s0 .INIT=1'b0;
DFFC \fifo_inst/wptr_11_s0  (
	.D(\fifo_inst/Equal.wgraynext [11]),
	.CLK(WrClk),
	.CLEAR(\fifo_inst/reset_w [1]),
	.Q(\fifo_inst/wptr [11])
);
defparam \fifo_inst/wptr_11_s0 .INIT=1'b0;
DFFC \fifo_inst/wptr_10_s0  (
	.D(\fifo_inst/Equal.wgraynext [10]),
	.CLK(WrClk),
	.CLEAR(\fifo_inst/reset_w [1]),
	.Q(\fifo_inst/wptr [10])
);
defparam \fifo_inst/wptr_10_s0 .INIT=1'b0;
DFFC \fifo_inst/wptr_9_s0  (
	.D(\fifo_inst/Equal.wgraynext [9]),
	.CLK(WrClk),
	.CLEAR(\fifo_inst/reset_w [1]),
	.Q(\fifo_inst/wptr [9])
);
defparam \fifo_inst/wptr_9_s0 .INIT=1'b0;
DFFC \fifo_inst/wptr_8_s0  (
	.D(\fifo_inst/Equal.wgraynext [8]),
	.CLK(WrClk),
	.CLEAR(\fifo_inst/reset_w [1]),
	.Q(\fifo_inst/wptr [8])
);
defparam \fifo_inst/wptr_8_s0 .INIT=1'b0;
DFFC \fifo_inst/wptr_7_s0  (
	.D(\fifo_inst/Equal.wgraynext [7]),
	.CLK(WrClk),
	.CLEAR(\fifo_inst/reset_w [1]),
	.Q(\fifo_inst/wptr [7])
);
defparam \fifo_inst/wptr_7_s0 .INIT=1'b0;
DFFC \fifo_inst/wptr_6_s0  (
	.D(\fifo_inst/Equal.wgraynext [6]),
	.CLK(WrClk),
	.CLEAR(\fifo_inst/reset_w [1]),
	.Q(\fifo_inst/wptr [6])
);
defparam \fifo_inst/wptr_6_s0 .INIT=1'b0;
DFFC \fifo_inst/wptr_5_s0  (
	.D(\fifo_inst/Equal.wgraynext [5]),
	.CLK(WrClk),
	.CLEAR(\fifo_inst/reset_w [1]),
	.Q(\fifo_inst/wptr [5])
);
defparam \fifo_inst/wptr_5_s0 .INIT=1'b0;
DFFC \fifo_inst/wptr_4_s0  (
	.D(\fifo_inst/Equal.wgraynext [4]),
	.CLK(WrClk),
	.CLEAR(\fifo_inst/reset_w [1]),
	.Q(\fifo_inst/wptr [4])
);
defparam \fifo_inst/wptr_4_s0 .INIT=1'b0;
DFFC \fifo_inst/wptr_3_s0  (
	.D(\fifo_inst/Equal.wgraynext [3]),
	.CLK(WrClk),
	.CLEAR(\fifo_inst/reset_w [1]),
	.Q(\fifo_inst/wptr [3])
);
defparam \fifo_inst/wptr_3_s0 .INIT=1'b0;
DFFC \fifo_inst/wptr_2_s0  (
	.D(\fifo_inst/Equal.wgraynext [2]),
	.CLK(WrClk),
	.CLEAR(\fifo_inst/reset_w [1]),
	.Q(\fifo_inst/wptr [2])
);
defparam \fifo_inst/wptr_2_s0 .INIT=1'b0;
DFFC \fifo_inst/wptr_1_s0  (
	.D(\fifo_inst/Equal.wgraynext [1]),
	.CLK(WrClk),
	.CLEAR(\fifo_inst/reset_w [1]),
	.Q(\fifo_inst/wptr [1])
);
defparam \fifo_inst/wptr_1_s0 .INIT=1'b0;
DFFC \fifo_inst/wptr_0_s0  (
	.D(\fifo_inst/Equal.wgraynext [0]),
	.CLK(WrClk),
	.CLEAR(\fifo_inst/reset_w [1]),
	.Q(\fifo_inst/wptr [0])
);
defparam \fifo_inst/wptr_0_s0 .INIT=1'b0;
DFFC \fifo_inst/Equal.wbin_13_s0  (
	.D(\fifo_inst/Equal.wbinnext [13]),
	.CLK(WrClk),
	.CLEAR(\fifo_inst/reset_w [1]),
	.Q(\fifo_inst/Equal.wbin [13])
);
defparam \fifo_inst/Equal.wbin_13_s0 .INIT=1'b0;
DFFC \fifo_inst/Equal.wbin_12_s0  (
	.D(\fifo_inst/Equal.wbinnext [12]),
	.CLK(WrClk),
	.CLEAR(\fifo_inst/reset_w [1]),
	.Q(\fifo_inst/Equal.wbin [12])
);
defparam \fifo_inst/Equal.wbin_12_s0 .INIT=1'b0;
DFFC \fifo_inst/Equal.wbin_11_s0  (
	.D(\fifo_inst/Equal.wbinnext [11]),
	.CLK(WrClk),
	.CLEAR(\fifo_inst/reset_w [1]),
	.Q(\fifo_inst/Equal.wbin [11])
);
defparam \fifo_inst/Equal.wbin_11_s0 .INIT=1'b0;
DFFC \fifo_inst/Equal.wbin_10_s0  (
	.D(\fifo_inst/Equal.wbinnext [10]),
	.CLK(WrClk),
	.CLEAR(\fifo_inst/reset_w [1]),
	.Q(\fifo_inst/Equal.wbin [10])
);
defparam \fifo_inst/Equal.wbin_10_s0 .INIT=1'b0;
DFFC \fifo_inst/Equal.wbin_9_s0  (
	.D(\fifo_inst/Equal.wbinnext [9]),
	.CLK(WrClk),
	.CLEAR(\fifo_inst/reset_w [1]),
	.Q(\fifo_inst/Equal.wbin [9])
);
defparam \fifo_inst/Equal.wbin_9_s0 .INIT=1'b0;
DFFC \fifo_inst/Equal.wbin_8_s0  (
	.D(\fifo_inst/Equal.wbinnext [8]),
	.CLK(WrClk),
	.CLEAR(\fifo_inst/reset_w [1]),
	.Q(\fifo_inst/Equal.wbin [8])
);
defparam \fifo_inst/Equal.wbin_8_s0 .INIT=1'b0;
DFFC \fifo_inst/Equal.wbin_7_s0  (
	.D(\fifo_inst/Equal.wbinnext [7]),
	.CLK(WrClk),
	.CLEAR(\fifo_inst/reset_w [1]),
	.Q(\fifo_inst/Equal.wbin [7])
);
defparam \fifo_inst/Equal.wbin_7_s0 .INIT=1'b0;
DFFC \fifo_inst/Equal.wbin_6_s0  (
	.D(\fifo_inst/Equal.wbinnext [6]),
	.CLK(WrClk),
	.CLEAR(\fifo_inst/reset_w [1]),
	.Q(\fifo_inst/Equal.wbin [6])
);
defparam \fifo_inst/Equal.wbin_6_s0 .INIT=1'b0;
DFFC \fifo_inst/Equal.wbin_5_s0  (
	.D(\fifo_inst/Equal.wbinnext [5]),
	.CLK(WrClk),
	.CLEAR(\fifo_inst/reset_w [1]),
	.Q(\fifo_inst/Equal.wbin [5])
);
defparam \fifo_inst/Equal.wbin_5_s0 .INIT=1'b0;
DFFC \fifo_inst/Equal.wbin_4_s0  (
	.D(\fifo_inst/Equal.wbinnext [4]),
	.CLK(WrClk),
	.CLEAR(\fifo_inst/reset_w [1]),
	.Q(\fifo_inst/Equal.wbin [4])
);
defparam \fifo_inst/Equal.wbin_4_s0 .INIT=1'b0;
DFFC \fifo_inst/Equal.wbin_3_s0  (
	.D(\fifo_inst/Equal.wbinnext [3]),
	.CLK(WrClk),
	.CLEAR(\fifo_inst/reset_w [1]),
	.Q(\fifo_inst/Equal.wbin [3])
);
defparam \fifo_inst/Equal.wbin_3_s0 .INIT=1'b0;
DFFC \fifo_inst/Equal.wbin_2_s0  (
	.D(\fifo_inst/Equal.wbinnext [2]),
	.CLK(WrClk),
	.CLEAR(\fifo_inst/reset_w [1]),
	.Q(\fifo_inst/Equal.wbin [2])
);
defparam \fifo_inst/Equal.wbin_2_s0 .INIT=1'b0;
DFFC \fifo_inst/Equal.wbin_1_s0  (
	.D(\fifo_inst/Equal.wbinnext [1]),
	.CLK(WrClk),
	.CLEAR(\fifo_inst/reset_w [1]),
	.Q(\fifo_inst/Equal.wbin [1])
);
defparam \fifo_inst/Equal.wbin_1_s0 .INIT=1'b0;
DFFC \fifo_inst/Equal.wbin_0_s0  (
	.D(\fifo_inst/Equal.wbinnext_0_7 ),
	.CLK(WrClk),
	.CLEAR(\fifo_inst/reset_w [1]),
	.Q(\fifo_inst/Equal.wbin [0])
);
defparam \fifo_inst/Equal.wbin_0_s0 .INIT=1'b0;
DFFP \fifo_inst/rempty_val1_s0  (
	.D(\fifo_inst/rempty_val ),
	.CLK(RdClk),
	.PRESET(\fifo_inst/n537_3 ),
	.Q(\fifo_inst/rempty_val1 )
);
defparam \fifo_inst/rempty_val1_s0 .INIT=1'b1;
DFFP \fifo_inst/Empty_s0  (
	.D(\fifo_inst/rempty_val1 ),
	.CLK(RdClk),
	.PRESET(\fifo_inst/n537_3 ),
	.Q(Empty)
);
defparam \fifo_inst/Empty_s0 .INIT=1'b1;
DFFC \fifo_inst/wfull_val1_s0  (
	.D(\fifo_inst/wfull_val ),
	.CLK(WrClk),
	.CLEAR(\fifo_inst/reset_w [1]),
	.Q(\fifo_inst/wfull_val1_2 )
);
defparam \fifo_inst/wfull_val1_s0 .INIT=1'b0;
DFFP \fifo_inst/wfull_val1_s1  (
	.D(\fifo_inst/wfull_val ),
	.CLK(WrClk),
	.PRESET(\fifo_inst/n760_3 ),
	.Q(\fifo_inst/wfull_val1_3 )
);
defparam \fifo_inst/wfull_val1_s1 .INIT=1'b1;
DFFC \fifo_inst/Full_s0  (
	.D(\fifo_inst/wfull_val1 ),
	.CLK(WrClk),
	.CLEAR(\fifo_inst/reset_w [1]),
	.Q(\fifo_inst/Full_1 )
);
defparam \fifo_inst/Full_s0 .INIT=1'b0;
DFFP \fifo_inst/Full_s1  (
	.D(\fifo_inst/wfull_val1 ),
	.CLK(WrClk),
	.PRESET(\fifo_inst/n760_3 ),
	.Q(\fifo_inst/Full_2 )
);
defparam \fifo_inst/Full_s1 .INIT=1'b1;
DLCE \fifo_inst/wfull_val1_s4  (
	.D(\fifo_inst/n760_3 ),
	.G(\fifo_inst/wfull_val ),
	.CLEAR(\fifo_inst/reset_w [1]),
	.CE(\fifo_inst/wfull_val1_14 ),
	.Q(\fifo_inst/wfull_val1_9 )
);
defparam \fifo_inst/wfull_val1_s4 .INIT=1'b0;
DFFNP \fifo_inst/reset_r_0_s1  (
	.D(GND),
	.CLK(RdClk),
	.PRESET(Reset),
	.Q(\fifo_inst/reset_r [0])
);
defparam \fifo_inst/reset_r_0_s1 .INIT=1'b1;
DFFNP \fifo_inst/reset_w_1_s1  (
	.D(\fifo_inst/reset_w [0]),
	.CLK(WrClk),
	.PRESET(Reset),
	.Q(\fifo_inst/reset_w [1])
);
defparam \fifo_inst/reset_w_1_s1 .INIT=1'b1;
DFFNP \fifo_inst/reset_w_0_s1  (
	.D(GND),
	.CLK(WrClk),
	.PRESET(Reset),
	.Q(\fifo_inst/reset_w [0])
);
defparam \fifo_inst/reset_w_0_s1 .INIT=1'b1;
DFFNP \fifo_inst/reset_r_1_s1  (
	.D(\fifo_inst/reset_r [0]),
	.CLK(RdClk),
	.PRESET(Reset),
	.Q(\fifo_inst/reset_r [1])
);
defparam \fifo_inst/reset_r_1_s1 .INIT=1'b1;
SDPB \fifo_inst/Equal.mem_Equal.mem_0_0_s  (
	.CLKA(WrClk),
	.CEA(\fifo_inst/n33_5 ),
	.RESETA(GND),
	.CLKB(RdClk),
	.CEB(\fifo_inst/n37_3 ),
	.RESETB(\fifo_inst/reset_r [1]),
	.OCE(\fifo_inst/n37_3 ),
	.BLKSELA({GND, GND, GND}),
	.BLKSELB({GND, GND, GND}),
	.DI({GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, Data[0]}),
	.ADA({\fifo_inst/Equal.wbin [13:0]}),
	.ADB({\fifo_inst/rbin_num [13:0]}),
	.DO({\fifo_inst/DO [31:1], Q[0]})
);
defparam \fifo_inst/Equal.mem_Equal.mem_0_0_s .READ_MODE=1'b1;
defparam \fifo_inst/Equal.mem_Equal.mem_0_0_s .BIT_WIDTH_0=1;
defparam \fifo_inst/Equal.mem_Equal.mem_0_0_s .BIT_WIDTH_1=1;
defparam \fifo_inst/Equal.mem_Equal.mem_0_0_s .RESET_MODE="ASYNC";
defparam \fifo_inst/Equal.mem_Equal.mem_0_0_s .BLK_SEL_0=3'b000;
defparam \fifo_inst/Equal.mem_Equal.mem_0_0_s .BLK_SEL_1=3'b000;
SDPB \fifo_inst/Equal.mem_Equal.mem_0_1_s  (
	.CLKA(WrClk),
	.CEA(\fifo_inst/n33_5 ),
	.RESETA(GND),
	.CLKB(RdClk),
	.CEB(\fifo_inst/n37_3 ),
	.RESETB(\fifo_inst/reset_r [1]),
	.OCE(\fifo_inst/n37_3 ),
	.BLKSELA({GND, GND, GND}),
	.BLKSELB({GND, GND, GND}),
	.DI({GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, Data[1]}),
	.ADA({\fifo_inst/Equal.wbin [13:0]}),
	.ADB({\fifo_inst/rbin_num [13:0]}),
	.DO({\fifo_inst/DO_0 [31:1], Q[1]})
);
defparam \fifo_inst/Equal.mem_Equal.mem_0_1_s .READ_MODE=1'b1;
defparam \fifo_inst/Equal.mem_Equal.mem_0_1_s .BIT_WIDTH_0=1;
defparam \fifo_inst/Equal.mem_Equal.mem_0_1_s .BIT_WIDTH_1=1;
defparam \fifo_inst/Equal.mem_Equal.mem_0_1_s .RESET_MODE="ASYNC";
defparam \fifo_inst/Equal.mem_Equal.mem_0_1_s .BLK_SEL_0=3'b000;
defparam \fifo_inst/Equal.mem_Equal.mem_0_1_s .BLK_SEL_1=3'b000;
SDPB \fifo_inst/Equal.mem_Equal.mem_0_2_s  (
	.CLKA(WrClk),
	.CEA(\fifo_inst/n33_5 ),
	.RESETA(GND),
	.CLKB(RdClk),
	.CEB(\fifo_inst/n37_3 ),
	.RESETB(\fifo_inst/reset_r [1]),
	.OCE(\fifo_inst/n37_3 ),
	.BLKSELA({GND, GND, GND}),
	.BLKSELB({GND, GND, GND}),
	.DI({GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, Data[2]}),
	.ADA({\fifo_inst/Equal.wbin [13:0]}),
	.ADB({\fifo_inst/rbin_num [13:0]}),
	.DO({\fifo_inst/DO_1 [31:1], Q[2]})
);
defparam \fifo_inst/Equal.mem_Equal.mem_0_2_s .READ_MODE=1'b1;
defparam \fifo_inst/Equal.mem_Equal.mem_0_2_s .BIT_WIDTH_0=1;
defparam \fifo_inst/Equal.mem_Equal.mem_0_2_s .BIT_WIDTH_1=1;
defparam \fifo_inst/Equal.mem_Equal.mem_0_2_s .RESET_MODE="ASYNC";
defparam \fifo_inst/Equal.mem_Equal.mem_0_2_s .BLK_SEL_0=3'b000;
defparam \fifo_inst/Equal.mem_Equal.mem_0_2_s .BLK_SEL_1=3'b000;
SDPB \fifo_inst/Equal.mem_Equal.mem_0_3_s  (
	.CLKA(WrClk),
	.CEA(\fifo_inst/n33_5 ),
	.RESETA(GND),
	.CLKB(RdClk),
	.CEB(\fifo_inst/n37_3 ),
	.RESETB(\fifo_inst/reset_r [1]),
	.OCE(\fifo_inst/n37_3 ),
	.BLKSELA({GND, GND, GND}),
	.BLKSELB({GND, GND, GND}),
	.DI({GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, Data[3]}),
	.ADA({\fifo_inst/Equal.wbin [13:0]}),
	.ADB({\fifo_inst/rbin_num [13:0]}),
	.DO({\fifo_inst/DO_2 [31:1], Q[3]})
);
defparam \fifo_inst/Equal.mem_Equal.mem_0_3_s .READ_MODE=1'b1;
defparam \fifo_inst/Equal.mem_Equal.mem_0_3_s .BIT_WIDTH_0=1;
defparam \fifo_inst/Equal.mem_Equal.mem_0_3_s .BIT_WIDTH_1=1;
defparam \fifo_inst/Equal.mem_Equal.mem_0_3_s .RESET_MODE="ASYNC";
defparam \fifo_inst/Equal.mem_Equal.mem_0_3_s .BLK_SEL_0=3'b000;
defparam \fifo_inst/Equal.mem_Equal.mem_0_3_s .BLK_SEL_1=3'b000;
SDPB \fifo_inst/Equal.mem_Equal.mem_0_4_s  (
	.CLKA(WrClk),
	.CEA(\fifo_inst/n33_5 ),
	.RESETA(GND),
	.CLKB(RdClk),
	.CEB(\fifo_inst/n37_3 ),
	.RESETB(\fifo_inst/reset_r [1]),
	.OCE(\fifo_inst/n37_3 ),
	.BLKSELA({GND, GND, GND}),
	.BLKSELB({GND, GND, GND}),
	.DI({GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, Data[4]}),
	.ADA({\fifo_inst/Equal.wbin [13:0]}),
	.ADB({\fifo_inst/rbin_num [13:0]}),
	.DO({\fifo_inst/DO_3 [31:1], Q[4]})
);
defparam \fifo_inst/Equal.mem_Equal.mem_0_4_s .READ_MODE=1'b1;
defparam \fifo_inst/Equal.mem_Equal.mem_0_4_s .BIT_WIDTH_0=1;
defparam \fifo_inst/Equal.mem_Equal.mem_0_4_s .BIT_WIDTH_1=1;
defparam \fifo_inst/Equal.mem_Equal.mem_0_4_s .RESET_MODE="ASYNC";
defparam \fifo_inst/Equal.mem_Equal.mem_0_4_s .BLK_SEL_0=3'b000;
defparam \fifo_inst/Equal.mem_Equal.mem_0_4_s .BLK_SEL_1=3'b000;
SDPB \fifo_inst/Equal.mem_Equal.mem_0_5_s  (
	.CLKA(WrClk),
	.CEA(\fifo_inst/n33_5 ),
	.RESETA(GND),
	.CLKB(RdClk),
	.CEB(\fifo_inst/n37_3 ),
	.RESETB(\fifo_inst/reset_r [1]),
	.OCE(\fifo_inst/n37_3 ),
	.BLKSELA({GND, GND, GND}),
	.BLKSELB({GND, GND, GND}),
	.DI({GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, Data[5]}),
	.ADA({\fifo_inst/Equal.wbin [13:0]}),
	.ADB({\fifo_inst/rbin_num [13:0]}),
	.DO({\fifo_inst/DO_4 [31:1], Q[5]})
);
defparam \fifo_inst/Equal.mem_Equal.mem_0_5_s .READ_MODE=1'b1;
defparam \fifo_inst/Equal.mem_Equal.mem_0_5_s .BIT_WIDTH_0=1;
defparam \fifo_inst/Equal.mem_Equal.mem_0_5_s .BIT_WIDTH_1=1;
defparam \fifo_inst/Equal.mem_Equal.mem_0_5_s .RESET_MODE="ASYNC";
defparam \fifo_inst/Equal.mem_Equal.mem_0_5_s .BLK_SEL_0=3'b000;
defparam \fifo_inst/Equal.mem_Equal.mem_0_5_s .BLK_SEL_1=3'b000;
SDPB \fifo_inst/Equal.mem_Equal.mem_0_6_s  (
	.CLKA(WrClk),
	.CEA(\fifo_inst/n33_5 ),
	.RESETA(GND),
	.CLKB(RdClk),
	.CEB(\fifo_inst/n37_3 ),
	.RESETB(\fifo_inst/reset_r [1]),
	.OCE(\fifo_inst/n37_3 ),
	.BLKSELA({GND, GND, GND}),
	.BLKSELB({GND, GND, GND}),
	.DI({GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, Data[6]}),
	.ADA({\fifo_inst/Equal.wbin [13:0]}),
	.ADB({\fifo_inst/rbin_num [13:0]}),
	.DO({\fifo_inst/DO_5 [31:1], Q[6]})
);
defparam \fifo_inst/Equal.mem_Equal.mem_0_6_s .READ_MODE=1'b1;
defparam \fifo_inst/Equal.mem_Equal.mem_0_6_s .BIT_WIDTH_0=1;
defparam \fifo_inst/Equal.mem_Equal.mem_0_6_s .BIT_WIDTH_1=1;
defparam \fifo_inst/Equal.mem_Equal.mem_0_6_s .RESET_MODE="ASYNC";
defparam \fifo_inst/Equal.mem_Equal.mem_0_6_s .BLK_SEL_0=3'b000;
defparam \fifo_inst/Equal.mem_Equal.mem_0_6_s .BLK_SEL_1=3'b000;
SDPB \fifo_inst/Equal.mem_Equal.mem_0_7_s  (
	.CLKA(WrClk),
	.CEA(\fifo_inst/n33_5 ),
	.RESETA(GND),
	.CLKB(RdClk),
	.CEB(\fifo_inst/n37_3 ),
	.RESETB(\fifo_inst/reset_r [1]),
	.OCE(\fifo_inst/n37_3 ),
	.BLKSELA({GND, GND, GND}),
	.BLKSELB({GND, GND, GND}),
	.DI({GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, Data[7]}),
	.ADA({\fifo_inst/Equal.wbin [13:0]}),
	.ADB({\fifo_inst/rbin_num [13:0]}),
	.DO({\fifo_inst/DO_6 [31:1], Q[7]})
);
defparam \fifo_inst/Equal.mem_Equal.mem_0_7_s .READ_MODE=1'b1;
defparam \fifo_inst/Equal.mem_Equal.mem_0_7_s .BIT_WIDTH_0=1;
defparam \fifo_inst/Equal.mem_Equal.mem_0_7_s .BIT_WIDTH_1=1;
defparam \fifo_inst/Equal.mem_Equal.mem_0_7_s .RESET_MODE="ASYNC";
defparam \fifo_inst/Equal.mem_Equal.mem_0_7_s .BLK_SEL_0=3'b000;
defparam \fifo_inst/Equal.mem_Equal.mem_0_7_s .BLK_SEL_1=3'b000;
SDPB \fifo_inst/Equal.mem_Equal.mem_0_8_s  (
	.CLKA(WrClk),
	.CEA(\fifo_inst/n33_5 ),
	.RESETA(GND),
	.CLKB(RdClk),
	.CEB(\fifo_inst/n37_3 ),
	.RESETB(\fifo_inst/reset_r [1]),
	.OCE(\fifo_inst/n37_3 ),
	.BLKSELA({GND, GND, GND}),
	.BLKSELB({GND, GND, GND}),
	.DI({GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, Data[8]}),
	.ADA({\fifo_inst/Equal.wbin [13:0]}),
	.ADB({\fifo_inst/rbin_num [13:0]}),
	.DO({\fifo_inst/DO_7 [31:1], Q[8]})
);
defparam \fifo_inst/Equal.mem_Equal.mem_0_8_s .READ_MODE=1'b1;
defparam \fifo_inst/Equal.mem_Equal.mem_0_8_s .BIT_WIDTH_0=1;
defparam \fifo_inst/Equal.mem_Equal.mem_0_8_s .BIT_WIDTH_1=1;
defparam \fifo_inst/Equal.mem_Equal.mem_0_8_s .RESET_MODE="ASYNC";
defparam \fifo_inst/Equal.mem_Equal.mem_0_8_s .BLK_SEL_0=3'b000;
defparam \fifo_inst/Equal.mem_Equal.mem_0_8_s .BLK_SEL_1=3'b000;
SDPB \fifo_inst/Equal.mem_Equal.mem_0_9_s  (
	.CLKA(WrClk),
	.CEA(\fifo_inst/n33_5 ),
	.RESETA(GND),
	.CLKB(RdClk),
	.CEB(\fifo_inst/n37_3 ),
	.RESETB(\fifo_inst/reset_r [1]),
	.OCE(\fifo_inst/n37_3 ),
	.BLKSELA({GND, GND, GND}),
	.BLKSELB({GND, GND, GND}),
	.DI({GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, Data[9]}),
	.ADA({\fifo_inst/Equal.wbin [13:0]}),
	.ADB({\fifo_inst/rbin_num [13:0]}),
	.DO({\fifo_inst/DO_8 [31:1], Q[9]})
);
defparam \fifo_inst/Equal.mem_Equal.mem_0_9_s .READ_MODE=1'b1;
defparam \fifo_inst/Equal.mem_Equal.mem_0_9_s .BIT_WIDTH_0=1;
defparam \fifo_inst/Equal.mem_Equal.mem_0_9_s .BIT_WIDTH_1=1;
defparam \fifo_inst/Equal.mem_Equal.mem_0_9_s .RESET_MODE="ASYNC";
defparam \fifo_inst/Equal.mem_Equal.mem_0_9_s .BLK_SEL_0=3'b000;
defparam \fifo_inst/Equal.mem_Equal.mem_0_9_s .BLK_SEL_1=3'b000;
SDPB \fifo_inst/Equal.mem_Equal.mem_0_10_s  (
	.CLKA(WrClk),
	.CEA(\fifo_inst/n33_5 ),
	.RESETA(GND),
	.CLKB(RdClk),
	.CEB(\fifo_inst/n37_3 ),
	.RESETB(\fifo_inst/reset_r [1]),
	.OCE(\fifo_inst/n37_3 ),
	.BLKSELA({GND, GND, GND}),
	.BLKSELB({GND, GND, GND}),
	.DI({GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, Data[10]}),
	.ADA({\fifo_inst/Equal.wbin [13:0]}),
	.ADB({\fifo_inst/rbin_num [13:0]}),
	.DO({\fifo_inst/DO_9 [31:1], Q[10]})
);
defparam \fifo_inst/Equal.mem_Equal.mem_0_10_s .READ_MODE=1'b1;
defparam \fifo_inst/Equal.mem_Equal.mem_0_10_s .BIT_WIDTH_0=1;
defparam \fifo_inst/Equal.mem_Equal.mem_0_10_s .BIT_WIDTH_1=1;
defparam \fifo_inst/Equal.mem_Equal.mem_0_10_s .RESET_MODE="ASYNC";
defparam \fifo_inst/Equal.mem_Equal.mem_0_10_s .BLK_SEL_0=3'b000;
defparam \fifo_inst/Equal.mem_Equal.mem_0_10_s .BLK_SEL_1=3'b000;
SDPB \fifo_inst/Equal.mem_Equal.mem_0_11_s  (
	.CLKA(WrClk),
	.CEA(\fifo_inst/n33_5 ),
	.RESETA(GND),
	.CLKB(RdClk),
	.CEB(\fifo_inst/n37_3 ),
	.RESETB(\fifo_inst/reset_r [1]),
	.OCE(\fifo_inst/n37_3 ),
	.BLKSELA({GND, GND, GND}),
	.BLKSELB({GND, GND, GND}),
	.DI({GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, Data[11]}),
	.ADA({\fifo_inst/Equal.wbin [13:0]}),
	.ADB({\fifo_inst/rbin_num [13:0]}),
	.DO({\fifo_inst/DO_10 [31:1], Q[11]})
);
defparam \fifo_inst/Equal.mem_Equal.mem_0_11_s .READ_MODE=1'b1;
defparam \fifo_inst/Equal.mem_Equal.mem_0_11_s .BIT_WIDTH_0=1;
defparam \fifo_inst/Equal.mem_Equal.mem_0_11_s .BIT_WIDTH_1=1;
defparam \fifo_inst/Equal.mem_Equal.mem_0_11_s .RESET_MODE="ASYNC";
defparam \fifo_inst/Equal.mem_Equal.mem_0_11_s .BLK_SEL_0=3'b000;
defparam \fifo_inst/Equal.mem_Equal.mem_0_11_s .BLK_SEL_1=3'b000;
SDPB \fifo_inst/Equal.mem_Equal.mem_0_12_s  (
	.CLKA(WrClk),
	.CEA(\fifo_inst/n33_5 ),
	.RESETA(GND),
	.CLKB(RdClk),
	.CEB(\fifo_inst/n37_3 ),
	.RESETB(\fifo_inst/reset_r [1]),
	.OCE(\fifo_inst/n37_3 ),
	.BLKSELA({GND, GND, GND}),
	.BLKSELB({GND, GND, GND}),
	.DI({GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, Data[12]}),
	.ADA({\fifo_inst/Equal.wbin [13:0]}),
	.ADB({\fifo_inst/rbin_num [13:0]}),
	.DO({\fifo_inst/DO_11 [31:1], Q[12]})
);
defparam \fifo_inst/Equal.mem_Equal.mem_0_12_s .READ_MODE=1'b1;
defparam \fifo_inst/Equal.mem_Equal.mem_0_12_s .BIT_WIDTH_0=1;
defparam \fifo_inst/Equal.mem_Equal.mem_0_12_s .BIT_WIDTH_1=1;
defparam \fifo_inst/Equal.mem_Equal.mem_0_12_s .RESET_MODE="ASYNC";
defparam \fifo_inst/Equal.mem_Equal.mem_0_12_s .BLK_SEL_0=3'b000;
defparam \fifo_inst/Equal.mem_Equal.mem_0_12_s .BLK_SEL_1=3'b000;
SDPB \fifo_inst/Equal.mem_Equal.mem_0_13_s  (
	.CLKA(WrClk),
	.CEA(\fifo_inst/n33_5 ),
	.RESETA(GND),
	.CLKB(RdClk),
	.CEB(\fifo_inst/n37_3 ),
	.RESETB(\fifo_inst/reset_r [1]),
	.OCE(\fifo_inst/n37_3 ),
	.BLKSELA({GND, GND, GND}),
	.BLKSELB({GND, GND, GND}),
	.DI({GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, Data[13]}),
	.ADA({\fifo_inst/Equal.wbin [13:0]}),
	.ADB({\fifo_inst/rbin_num [13:0]}),
	.DO({\fifo_inst/DO_12 [31:1], Q[13]})
);
defparam \fifo_inst/Equal.mem_Equal.mem_0_13_s .READ_MODE=1'b1;
defparam \fifo_inst/Equal.mem_Equal.mem_0_13_s .BIT_WIDTH_0=1;
defparam \fifo_inst/Equal.mem_Equal.mem_0_13_s .BIT_WIDTH_1=1;
defparam \fifo_inst/Equal.mem_Equal.mem_0_13_s .RESET_MODE="ASYNC";
defparam \fifo_inst/Equal.mem_Equal.mem_0_13_s .BLK_SEL_0=3'b000;
defparam \fifo_inst/Equal.mem_Equal.mem_0_13_s .BLK_SEL_1=3'b000;
SDPB \fifo_inst/Equal.mem_Equal.mem_0_14_s  (
	.CLKA(WrClk),
	.CEA(\fifo_inst/n33_5 ),
	.RESETA(GND),
	.CLKB(RdClk),
	.CEB(\fifo_inst/n37_3 ),
	.RESETB(\fifo_inst/reset_r [1]),
	.OCE(\fifo_inst/n37_3 ),
	.BLKSELA({GND, GND, GND}),
	.BLKSELB({GND, GND, GND}),
	.DI({GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, Data[14]}),
	.ADA({\fifo_inst/Equal.wbin [13:0]}),
	.ADB({\fifo_inst/rbin_num [13:0]}),
	.DO({\fifo_inst/DO_13 [31:1], Q[14]})
);
defparam \fifo_inst/Equal.mem_Equal.mem_0_14_s .READ_MODE=1'b1;
defparam \fifo_inst/Equal.mem_Equal.mem_0_14_s .BIT_WIDTH_0=1;
defparam \fifo_inst/Equal.mem_Equal.mem_0_14_s .BIT_WIDTH_1=1;
defparam \fifo_inst/Equal.mem_Equal.mem_0_14_s .RESET_MODE="ASYNC";
defparam \fifo_inst/Equal.mem_Equal.mem_0_14_s .BLK_SEL_0=3'b000;
defparam \fifo_inst/Equal.mem_Equal.mem_0_14_s .BLK_SEL_1=3'b000;
SDPB \fifo_inst/Equal.mem_Equal.mem_0_15_s  (
	.CLKA(WrClk),
	.CEA(\fifo_inst/n33_5 ),
	.RESETA(GND),
	.CLKB(RdClk),
	.CEB(\fifo_inst/n37_3 ),
	.RESETB(\fifo_inst/reset_r [1]),
	.OCE(\fifo_inst/n37_3 ),
	.BLKSELA({GND, GND, GND}),
	.BLKSELB({GND, GND, GND}),
	.DI({GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, Data[15]}),
	.ADA({\fifo_inst/Equal.wbin [13:0]}),
	.ADB({\fifo_inst/rbin_num [13:0]}),
	.DO({\fifo_inst/DO_14 [31:1], Q[15]})
);
defparam \fifo_inst/Equal.mem_Equal.mem_0_15_s .READ_MODE=1'b1;
defparam \fifo_inst/Equal.mem_Equal.mem_0_15_s .BIT_WIDTH_0=1;
defparam \fifo_inst/Equal.mem_Equal.mem_0_15_s .BIT_WIDTH_1=1;
defparam \fifo_inst/Equal.mem_Equal.mem_0_15_s .RESET_MODE="ASYNC";
defparam \fifo_inst/Equal.mem_Equal.mem_0_15_s .BLK_SEL_0=3'b000;
defparam \fifo_inst/Equal.mem_Equal.mem_0_15_s .BLK_SEL_1=3'b000;
ALU \fifo_inst/n148_s0  (
	.I0(\fifo_inst/wptr [0]),
	.I1(\fifo_inst/rptr [0]),
	.I3(GND),
	.CIN(GND),
	.COUT(\fifo_inst/n148_3 ),
	.SUM(\fifo_inst/n148_1_SUM )
);
defparam \fifo_inst/n148_s0 .ALU_MODE=3;
ALU \fifo_inst/n149_s0  (
	.I0(\fifo_inst/wptr [1]),
	.I1(\fifo_inst/rptr [1]),
	.I3(GND),
	.CIN(\fifo_inst/n148_3 ),
	.COUT(\fifo_inst/n149_3 ),
	.SUM(\fifo_inst/n149_1_SUM )
);
defparam \fifo_inst/n149_s0 .ALU_MODE=3;
ALU \fifo_inst/n150_s0  (
	.I0(\fifo_inst/wptr [2]),
	.I1(\fifo_inst/rptr [2]),
	.I3(GND),
	.CIN(\fifo_inst/n149_3 ),
	.COUT(\fifo_inst/n150_3 ),
	.SUM(\fifo_inst/n150_1_SUM )
);
defparam \fifo_inst/n150_s0 .ALU_MODE=3;
ALU \fifo_inst/n151_s0  (
	.I0(\fifo_inst/wptr [3]),
	.I1(\fifo_inst/rptr [3]),
	.I3(GND),
	.CIN(\fifo_inst/n150_3 ),
	.COUT(\fifo_inst/n151_3 ),
	.SUM(\fifo_inst/n151_1_SUM )
);
defparam \fifo_inst/n151_s0 .ALU_MODE=3;
ALU \fifo_inst/n152_s0  (
	.I0(\fifo_inst/wptr [4]),
	.I1(\fifo_inst/rptr [4]),
	.I3(GND),
	.CIN(\fifo_inst/n151_3 ),
	.COUT(\fifo_inst/n152_3 ),
	.SUM(\fifo_inst/n152_1_SUM )
);
defparam \fifo_inst/n152_s0 .ALU_MODE=3;
ALU \fifo_inst/n153_s0  (
	.I0(\fifo_inst/wptr [5]),
	.I1(\fifo_inst/rptr [5]),
	.I3(GND),
	.CIN(\fifo_inst/n152_3 ),
	.COUT(\fifo_inst/n153_3 ),
	.SUM(\fifo_inst/n153_1_SUM )
);
defparam \fifo_inst/n153_s0 .ALU_MODE=3;
ALU \fifo_inst/n154_s0  (
	.I0(\fifo_inst/wptr [6]),
	.I1(\fifo_inst/rptr [6]),
	.I3(GND),
	.CIN(\fifo_inst/n153_3 ),
	.COUT(\fifo_inst/n154_3 ),
	.SUM(\fifo_inst/n154_1_SUM )
);
defparam \fifo_inst/n154_s0 .ALU_MODE=3;
ALU \fifo_inst/n155_s0  (
	.I0(\fifo_inst/wptr [7]),
	.I1(\fifo_inst/rptr [7]),
	.I3(GND),
	.CIN(\fifo_inst/n154_3 ),
	.COUT(\fifo_inst/n155_3 ),
	.SUM(\fifo_inst/n155_1_SUM )
);
defparam \fifo_inst/n155_s0 .ALU_MODE=3;
ALU \fifo_inst/n156_s0  (
	.I0(\fifo_inst/wptr [8]),
	.I1(\fifo_inst/rptr [8]),
	.I3(GND),
	.CIN(\fifo_inst/n155_3 ),
	.COUT(\fifo_inst/n156_3 ),
	.SUM(\fifo_inst/n156_1_SUM )
);
defparam \fifo_inst/n156_s0 .ALU_MODE=3;
ALU \fifo_inst/n157_s0  (
	.I0(\fifo_inst/wptr [9]),
	.I1(\fifo_inst/rptr [9]),
	.I3(GND),
	.CIN(\fifo_inst/n156_3 ),
	.COUT(\fifo_inst/n157_3 ),
	.SUM(\fifo_inst/n157_1_SUM )
);
defparam \fifo_inst/n157_s0 .ALU_MODE=3;
ALU \fifo_inst/n158_s0  (
	.I0(\fifo_inst/wptr [10]),
	.I1(\fifo_inst/rptr [10]),
	.I3(GND),
	.CIN(\fifo_inst/n157_3 ),
	.COUT(\fifo_inst/n158_3 ),
	.SUM(\fifo_inst/n158_1_SUM )
);
defparam \fifo_inst/n158_s0 .ALU_MODE=3;
ALU \fifo_inst/n159_s0  (
	.I0(\fifo_inst/wptr [11]),
	.I1(\fifo_inst/rptr [11]),
	.I3(GND),
	.CIN(\fifo_inst/n158_3 ),
	.COUT(\fifo_inst/n159_3 ),
	.SUM(\fifo_inst/n159_1_SUM )
);
defparam \fifo_inst/n159_s0 .ALU_MODE=3;
ALU \fifo_inst/n160_s0  (
	.I0(\fifo_inst/wptr [12]),
	.I1(\fifo_inst/rptr [12]),
	.I3(GND),
	.CIN(\fifo_inst/n159_3 ),
	.COUT(\fifo_inst/n160_3 ),
	.SUM(\fifo_inst/n160_1_SUM )
);
defparam \fifo_inst/n160_s0 .ALU_MODE=3;
ALU \fifo_inst/n161_s0  (
	.I0(\fifo_inst/wptr [13]),
	.I1(\fifo_inst/rptr [13]),
	.I3(GND),
	.CIN(\fifo_inst/n160_3 ),
	.COUT(\fifo_inst/n161_3 ),
	.SUM(\fifo_inst/n161_1_SUM )
);
defparam \fifo_inst/n161_s0 .ALU_MODE=3;
INV \fifo_inst/wfull_val1_s8  (
	.I(\fifo_inst/reset_w [1]),
	.O(\fifo_inst/wfull_val1_14 )
);
endmodule
