GowinSynthesis start
Running parser ...
Analyzing Verilog file 'G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\Documents\FFT\src\FFT_Top\fft.v'
Analyzing Verilog file 'G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\Documents\FFT\src\counter.v'
Analyzing Verilog file 'G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\Documents\FFT\src\deUstb.v'
Analyzing Verilog file 'G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\Documents\FFT\src\downCnt.v'
Analyzing Verilog file 'G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\Documents\FFT\src\gowin_rpll.v'
Analyzing Verilog file 'G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\Documents\FFT\src\testfft.v'
Analyzing Verilog file 'G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\Documents\FFT\src\testfft_top.v'
Analyzing included file 'G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\Documents\FFT\src\defile.v'("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\Documents\FFT\src\testfft_top.v":1)
Back to file 'G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\Documents\FFT\src\testfft_top.v'("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\Documents\FFT\src\testfft_top.v":1)
Undeclared symbol 'fftClk', assumed default net type 'wire'("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\Documents\FFT\src\testfft_top.v":42)
Undeclared symbol 'fftStart', assumed default net type 'wire'("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\Documents\FFT\src\testfft_top.v":57)
Undeclared symbol 'fftRst', assumed default net type 'wire'("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\Documents\FFT\src\testfft_top.v":62)
Compiling module 'testfft_top'("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\Documents\FFT\src\testfft_top.v":2)
Compiling module 'Gowin_rPLL'("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\Documents\FFT\src\gowin_rpll.v":2)
Compiling module 'counter(CNT=2)'("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\Documents\FFT\src\counter.v":1)
Compiling module 'deUstb'("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\Documents\FFT\src\deUstb.v":1)
Compiling module 'testfft'("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\Documents\FFT\src\testfft.v":2)
Extracting RAM for identifier 'data'("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\Documents\FFT\src\testfft.v":16)
Compiling module 'GW_ROM'("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\Documents\FFT\src\testfft.v":30)
Compiling module '**'("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\Documents\FFT\src\FFT_Top\fft.v":3162)
Compiling module 'downCnt(DW=3,INIT=5)'("G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\Documents\FFT\src\downCnt.v":1)
NOTE  (EX0101) : Current top module is "testfft_top"
[5%] Running netlist conversion ...
Running device independent optimization ...
[10%] Optimizing Phase 0 completed
[15%] Optimizing Phase 1 completed
[25%] Optimizing Phase 2 completed
Running inference ...
[30%] Inferring Phase 0 completed
[40%] Inferring Phase 1 completed
[50%] Inferring Phase 2 completed
[55%] Inferring Phase 3 completed
Running technical mapping ...
[60%] Tech-Mapping Phase 0 completed
[65%] Tech-Mapping Phase 1 completed
[75%] Tech-Mapping Phase 2 completed
[80%] Tech-Mapping Phase 3 completed
[90%] Tech-Mapping Phase 4 completed
[95%] Generate netlist file "G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\Documents\FFT\impl\gwsynthesis\FFT.vg" completed
[100%] Generate report file "G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\Documents\FFT\impl\gwsynthesis\FFT_syn.rpt.html" completed
GowinSynthesis finish
