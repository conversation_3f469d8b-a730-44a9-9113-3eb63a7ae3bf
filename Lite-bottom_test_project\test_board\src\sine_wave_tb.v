`timescale 1ns / 1ps

module sine_wave_tb;

    // 测试信号
    reg clk;
    reg rst_n;
    wire sine_out;
    wire [7:0] sine_amplitude;
    
    // 实例化正弦波生成器
    sine_wave_generator #(
        .FREQ_DIV(100),      // 快速测试用的小分频系数
        .PHASE_WIDTH(8),
        .PWM_WIDTH(8)
    ) uut (
        .clk(clk),
        .rst_n(rst_n),
        .sine_out(sine_out),
        .sine_amplitude(sine_amplitude)
    );
    
    // 时钟生成
    initial begin
        clk = 0;
        forever #18.5 clk = ~clk; // 27MHz时钟
    end
    
    // 复位和测试序列
    initial begin
        rst_n = 0;
        #100;
        rst_n = 1;
        
        // 运行足够长的时间来观察几个正弦波周期
        #1000000;
        
        $finish;
    end
    
    // 监控输出
    initial begin
        $monitor("Time: %t, sine_amplitude: %d, sine_out: %b", $time, sine_amplitude, sine_out);
    end

endmodule
