<?xml version="1" encoding="UTF-8"?>
<!DOCTYPE ProjectUserData>
<UserConfig>
    <Version>1.0</Version>
    <FlowState>
        <Process ID="Synthesis" State="3"/>
        <Process ID="Pnr" State="4"/>
        <Process ID="Gao" State="4"/>
        <Process ID="Rtl_Gao" State="2"/>
        <Process ID="Gvio" State="0"/>
        <Process ID="Place" State="2"/>
    </FlowState>
    <ResultFileList>
        <ResultFile ResultFileType="RES.pnr.bitstream" ResultFilePath="impl/pnr/bottom_board_test.fs"/>
        <ResultFile ResultFileType="RES.pnr.pin.rpt" ResultFilePath="impl/pnr/bottom_board_test.pin.html"/>
        <ResultFile ResultFileType="RES.pnr.posp.bin" ResultFilePath="impl/pnr/bottom_board_test.db"/>
        <ResultFile ResultFileType="RES.pnr.pwr.rpt" ResultFilePath="impl/pnr/bottom_board_test.power.html"/>
        <ResultFile ResultFileType="RES.pnr.report" ResultFilePath="impl/pnr/bottom_board_test.rpt.html"/>
        <ResultFile ResultFileType="RES.pnr.timing.paths" ResultFilePath="impl/pnr/bottom_board_test.timing_paths"/>
        <ResultFile ResultFileType="RES.pnr.timing.rpt" ResultFilePath="impl/pnr/bottom_board_test.tr.html"/>
    </ResultFileList>
    <Ui>000000ff00000001fd0000000200000000000001c300000285fc0200000002fc00000039000001b90000000000fffffffa000000000200000001fb00000030004600700067006100500072006f006a006500630074002e00500061006e0065006c002e00440065007300690067006e0100000000ffffffff0000000000000000fc00000039000002850000000000fffffffaffffffff0200000003fb00000030004600700067006100500072006f006a006500630074002e00500061006e0065006c002e00440065007300690067006e0100000000ffffffff0000000000000000fb00000032004600700067006100500072006f006a006500630074002e00500061006e0065006c002e00500072006f00630065007300730100000000ffffffff0000000000000000fb00000036004600700067006100500072006f006a006500630074002e00500061006e0065006c002e0048006900650072006100720063006800790100000000ffffffff000000000000000000000003000004fe00000110fc0100000001fc00000000000004fe000000a400fffffffa000000000100000003fb00000032004600700067006100500072006f006a006500630074002e00500061006e0065006c002e00470065006e006500720061006c0100000000ffffffff0000005300fffffffb00000032004600700067006100500072006f006a006500630074002e00500061006e0065006c002e00470065006e006500720061006c0100000000ffffffff0000000000000000fb0000002e004600700067006100500072006f006a006500630074002e00500061006e0065006c002e004900730073007500650100000000ffffffff000000a400ffffff000003370000028500000004000000040000000800000008fc000000010000000200000004000000220043006f00720065002e0054006f006f006c006200610072002e00460069006c00650100000000ffffffff0000000000000000000000220043006f00720065002e0054006f006f006c006200610072002e004500640069007401000000adffffffff0000000000000000000000240043006f00720065002e0054006f006f006c006200610072002e0054006f006f006c0073010000017fffffffff0000000000000000000000280043006f00720065002e0054006f006f006c006200610072002e00500072006f00630065007300730100000245ffffffff0000000000000000</Ui>
    <FpUi>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</FpUi>
</UserConfig>
