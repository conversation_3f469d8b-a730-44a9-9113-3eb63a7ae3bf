-d G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\impl\gwsynthesis\sipeed_tang_primer_20k.vg
-p GW2A-18C-PBGA256-8
-pn GW2A-LV18PG256C8/I7
-cst G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.cst
-cfg G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\impl\pnr\device.cfg
-sdc G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.sdc
-bit
-tr
-ph
-timing
-cst_error
-convert_sdp32_36_to_sdp16_18
-place_option 0
-route_option 0
-clock_route_order 0
-correct_hold 1
-route_maxfan 23
-global_freq 100.000
