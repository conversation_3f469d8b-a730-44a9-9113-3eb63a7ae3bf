<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>synthesis Report</title>
<style type="text/css">
body { font-family: Verdana, Arial, sans-serif; font-size: 12px; }
div#main_wrapper{ width: 100%; }
div#content { margin-left: 350px; margin-right: 30px; }
div#catalog_wrapper {position: fixed; top: 30px; width: 350px; float: left; }
div#catalog ul { list-style-type: none; }
div#catalog li { text-align: left; list-style-type:circle; color: #0084ff; margin-top: 3px; margin-bottom: 3px; }
div#catalog a { display:inline-block; text-decoration: none; color: #0084ff; font-weight: bold; padding: 3px; }
div#catalog a:visited { color: #0084ff; }
div#catalog a:hover { color: #fff; background: #0084ff; }
hr { margin-top: 30px; margin-bottom: 30px; }
h1, h3 { text-align: center; }
h1 {margin-top: 50px; }
table, th, td { border: 1px solid #aaa; }
table { border-collapse:collapse; margin-top: 10px; margin-bottom: 20px; width: 100%; }
th, td { padding: 5px 5px 5px 5px; }
th { color: #fff; font-weight: bold; background-color: #0084ff; }
table.summary_table td.label { width: 24%; min-width: 200px; background-color: #dee8f4; }
table.detail_table td.label { min-width: 100px; width: 8%;}
</style>
</head>
<body>
<div id="main_wrapper">
<div id="catalog_wrapper">
<div id="catalog">
<ul>
<li><a href="#about" style=" font-size: 16px;">Synthesis Messages</a></li>
<li><a href="#summary" style=" font-size: 16px;">Synthesis Details</a></li>
<li><a href="#resource" style=" font-size: 16px;">Resource</a>
<ul>
<li><a href="#usage" style=" font-size: 14px;">Resource Usage Summary</a></li>
<li><a href="#utilization" style=" font-size: 14px;">Resource Utilization Summary</a></li>
</ul>
</li>
<li><a href="#timing" style=" font-size: 16px;">Timing</a>
<ul>
<li><a href="#clock" style=" font-size: 14px;">Clock Summary</a></li>
<li><a href="#performance" style=" font-size: 14px;">Max Frequency Summary</a></li>
<li><a href="#detail timing" style=" font-size: 14px;">Detail Timing Paths Informations</a></li>
</ul>
</li>
</ul>
</div><!-- catalog -->
</div><!-- catalog_wrapper -->
<div id="content">
<h1><a name="about">Synthesis Messages</a></h1>
<table class="summary_table">
<tr>
<td class="label">Report Title</td>
<td>GowinSynthesis Report</td>
</tr>
<tr>
<td class="label">Design File</td>
<td>G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\Documents\FFT\src\FFT_Top\fft.v<br>
G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\Documents\FFT\src\counter.v<br>
G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\Documents\FFT\src\deUstb.v<br>
G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\Documents\FFT\src\downCnt.v<br>
G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\Documents\FFT\src\gowin_rpll.v<br>
G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\Documents\FFT\src\testfft.v<br>
G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\Documents\FFT\src\testfft_top.v<br>
</td>
</tr>
<tr>
<td class="label">GowinSynthesis Constraints File</td>
<td>---</td>
</tr>
<tr>
<td class="label">Tool Version</td>
<td>V1.9.11.03 Education</td>
</tr>
<tr>
<td class="label">Part Number</td>
<td>GW2A-LV18PG256C8/I7</td>
</tr>
<tr>
<td class="label">Device</td>
<td>GW2A-18</td>
</tr>
<tr>
<td class="label">Device Version</td>
<td>C</td>
</tr>
<tr>
<td class="label">Created Time</td>
<td>Wed Jul 30 11:38:48 2025
</td>
</tr>
<tr>
<td class="label">Legal Announcement</td>
<td>Copyright (C)2014-2025 Gowin Semiconductor Corporation. ALL rights reserved.</td>
</tr>
</table>
<h1><a name="summary">Synthesis Details</a></h1>
<table class="summary_table">
<tr>
<td class="label">Top Level Module</td>
<td>testfft_top</td>
</tr>
<tr>
<td class="label">Synthesis Process</td>
<td>Running parser:<br/>&nbsp;&nbsp;&nbsp;&nbsp;CPU time = 0h 0m 0.109s, Elapsed time = 0h 0m 0.123s, Peak memory usage = 693.008MB<br/>Running netlist conversion:<br/>&nbsp;&nbsp;&nbsp;&nbsp;CPU time = 0h 0m 0s, Elapsed time = 0h 0m 0s, Peak memory usage = 0MB<br/>Running device independent optimization:<br/>&nbsp;&nbsp;&nbsp;&nbsp;Optimizing Phase 0: CPU time = 0h 0m 0s, Elapsed time = 0h 0m 0.006s, Peak memory usage = 693.008MB<br/>&nbsp;&nbsp;&nbsp;&nbsp;Optimizing Phase 1: CPU time = 0h 0m 0.015s, Elapsed time = 0h 0m 0.006s, Peak memory usage = 693.008MB<br/>&nbsp;&nbsp;&nbsp;&nbsp;Optimizing Phase 2: CPU time = 0h 0m 0s, Elapsed time = 0h 0m 0.004s, Peak memory usage = 693.008MB<br/>Running inference:<br/>&nbsp;&nbsp;&nbsp;&nbsp;Inferring Phase 0: CPU time = 0h 0m 0s, Elapsed time = 0h 0m 0.005s, Peak memory usage = 693.008MB<br/>&nbsp;&nbsp;&nbsp;&nbsp;Inferring Phase 1: CPU time = 0h 0m 0s, Elapsed time = 0h 0m 0s, Peak memory usage = 693.008MB<br/>&nbsp;&nbsp;&nbsp;&nbsp;Inferring Phase 2: CPU time = 0h 0m 0s, Elapsed time = 0h 0m 0.001s, Peak memory usage = 693.008MB<br/>&nbsp;&nbsp;&nbsp;&nbsp;Inferring Phase 3: CPU time = 0h 0m 0s, Elapsed time = 0h 0m 0s, Peak memory usage = 693.008MB<br/>Running technical mapping:<br/>&nbsp;&nbsp;&nbsp;&nbsp;Tech-Mapping Phase 0: CPU time = 0h 0m 0.015s, Elapsed time = 0h 0m 0.003s, Peak memory usage = 693.008MB<br/>&nbsp;&nbsp;&nbsp;&nbsp;Tech-Mapping Phase 1: CPU time = 0h 0m 0s, Elapsed time = 0h 0m 0.002s, Peak memory usage = 693.008MB<br/>&nbsp;&nbsp;&nbsp;&nbsp;Tech-Mapping Phase 2: CPU time = 0h 0m 0s, Elapsed time = 0h 0m 0.001s, Peak memory usage = 693.008MB<br/>&nbsp;&nbsp;&nbsp;&nbsp;Tech-Mapping Phase 3: CPU time = 0h 0m 0.078s, Elapsed time = 0h 0m 0.099s, Peak memory usage = 693.008MB<br/>&nbsp;&nbsp;&nbsp;&nbsp;Tech-Mapping Phase 4: CPU time = 0h 0m 0.046s, Elapsed time = 0h 0m 0.055s, Peak memory usage = 693.008MB<br/>Generate output files:<br/>&nbsp;&nbsp;&nbsp;&nbsp;CPU time = 0h 0m 0.046s, Elapsed time = 0h 0m 0.036s, Peak memory usage = 693.008MB<br/></td>
</tr>
<tr>
<td class="label">Total Time and Memory Usage</td>
<td>CPU time = 0h 0m 0.309s, Elapsed time = 0h 0m 0.34s, Peak memory usage = 693.008MB</td>
</tr>
</table>
<h1><a name="resource">Resource</a></h1>
<h2><a name="usage">Resource Usage Summary</a></h2>
<table class="summary_table">
<tr>
<td class="label"><b>Resource</b></td>
<td><b>Usage</b></td>
</tr>
<tr>
<td class="label"><b>I/O Port </b></td>
<td>71</td>
</tr>
<tr>
<td class="label"><b>I/O Buf </b></td>
<td>71</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspIBUF</td>
<td>13</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspOBUF</td>
<td>58</td>
</tr>
<tr>
<td class="label"><b>Register </b></td>
<td>269</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspDFF</td>
<td>6</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspDFFS</td>
<td>9</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspDFFR</td>
<td>212</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspDFFRE</td>
<td>32</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspDFFCE</td>
<td>10</td>
</tr>
<tr>
<td class="label"><b>LUT </b></td>
<td>580</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspLUT2</td>
<td>144</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspLUT3</td>
<td>178</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspLUT4</td>
<td>258</td>
</tr>
<tr>
<td class="label"><b>ALU </b></td>
<td>128</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspALU</td>
<td>128</td>
</tr>
<tr>
<td class="label"><b>INV </b></td>
<td>3</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspINV</td>
<td>3</td>
</tr>
<tr>
<td class="label"><b>DSP </b></td>
<td></td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspMULTADDALU18X18</td>
<td>2</td>
</tr>
<tr>
<td class="label"><b>BSRAM </b></td>
<td>7</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspROM</td>
<td>1</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspDPB</td>
<td>4</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbsppROM</td>
<td>2</td>
</tr>
<tr>
<td class="label"><b>CLOCK </b></td>
<td>1</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbsprPLL</td>
<td>1</td>
</tr>
</table>
<h2><a name="utilization">Resource Utilization Summary</a></h2>
<table class="summary_table">
<tr>
<td class="label"><b>Resource</b></td>
<td><b>Usage</b></td>
<td><b>Utilization</b></td>
</tr>
<tr>
<td class="label">Logic</td>
<td>711(583 LUT, 128 ALU) / 20736</td>
<td>4%</td>
</tr>
<tr>
<td class="label">Register</td>
<td>269 / 16173</td>
<td>2%</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp--Register as Latch</td>
<td>0 / 16173</td>
<td>0%</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp--Register as FF</td>
<td>269 / 16173</td>
<td>2%</td>
</tr>
<tr>
<td class="label">BSRAM</td>
<td>7 / 46</td>
<td>16%</td>
</tr>
</table>
<h1><a name="timing">Timing</a></h1>
<h2><a name="clock">Clock Summary:</a></h2>
<table class="summary_table">
<tr>
<th>NO.</th>
<th>Clock Name</th>
<th>Type</th>
<th>Period</th>
<th>Frequency(MHz)</th>
<th>Rise</th>
<th>Fall</th>
<th>Source</th>
<th>Master</th>
<th>Object</th>
</tr>
<tr>
<td>1</td>
<td>clk</td>
<td>Base</td>
<td>20.000</td>
<td>50.000</td>
<td>0.000</td>
<td>10.000</td>
<td> </td>
<td> </td>
<td>clk_ibuf/I </td>
</tr>
<tr>
<td>2</td>
<td>cntInst2/sysClk</td>
<td>Base</td>
<td>10.000</td>
<td>100.000</td>
<td>0.000</td>
<td>5.000</td>
<td> </td>
<td> </td>
<td>cntInst2/clko_s2/Q </td>
</tr>
<tr>
<td>3</td>
<td>pll_fft/rpll_inst/CLKOUT.default_gen_clk</td>
<td>Generated</td>
<td>100.000</td>
<td>10.000</td>
<td>0.000</td>
<td>50.000</td>
<td>clk_ibuf/I</td>
<td>clk</td>
<td>pll_fft/rpll_inst/CLKOUT </td>
</tr>
<tr>
<td>4</td>
<td>pll_fft/rpll_inst/CLKOUTP.default_gen_clk</td>
<td>Generated</td>
<td>100.000</td>
<td>10.000</td>
<td>0.000</td>
<td>50.000</td>
<td>clk_ibuf/I</td>
<td>clk</td>
<td>pll_fft/rpll_inst/CLKOUTP </td>
</tr>
<tr>
<td>5</td>
<td>pll_fft/rpll_inst/CLKOUTD.default_gen_clk</td>
<td>Generated</td>
<td>200.000</td>
<td>5.000</td>
<td>0.000</td>
<td>100.000</td>
<td>clk_ibuf/I</td>
<td>clk</td>
<td>pll_fft/rpll_inst/CLKOUTD </td>
</tr>
<tr>
<td>6</td>
<td>pll_fft/rpll_inst/CLKOUTD3.default_gen_clk</td>
<td>Generated</td>
<td>300.000</td>
<td>3.333</td>
<td>0.000</td>
<td>150.000</td>
<td>clk_ibuf/I</td>
<td>clk</td>
<td>pll_fft/rpll_inst/CLKOUTD3 </td>
</tr>
</table>
<h2><a name="performance">Max Frequency Summary:</a></h2>
<table class="summary_table">
<tr>
<th>NO.</th>
<th>Clock Name</th>
<th>Constraint</th>
<th>Actual Fmax</th>
<th>Logic Level</th>
<th>Entity</th>
</tr>
<tr>
<td>1</td>
<td>clk</td>
<td>50.000(MHz)</td>
<td>261.233(MHz)</td>
<td>4</td>
<td>TOP</td>
</tr>
<tr>
<td>2</td>
<td>cntInst2/sysClk</td>
<td>100.000(MHz)</td>
<td>564.972(MHz)</td>
<td>2</td>
<td>TOP</td>
</tr>
<tr>
<td>3</td>
<td>pll_fft/rpll_inst/CLKOUT.default_gen_clk</td>
<td>10.000(MHz)</td>
<td>104.428(MHz)</td>
<td>10</td>
<td>TOP</td>
</tr>
</table>
<h2><a name="detail timing">Detail Timing Paths Information</a></h2>
<h3>Path&nbsp1</h3>
<b>Path Summary:</b></br>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>3.889</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>16.401</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>20.290</td>
</tr>
<tr>
<td class="label">From</td>
<td>cntInst2/n72_s3</td>
</tr>
<tr>
<td class="label">To</td>
<td>cntInst2/clko_s2</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>cntInst2/sysClk[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>clk[F]</td>
</tr>
</table>
<b>Data Arrival Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>15.000</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>cntInst2/sysClk</td>
</tr>
<tr>
<td>15.000</td>
<td>0.000</td>
<td>tCL</td>
<td>FF</td>
<td>4</td>
<td>cntInst2/clko_s2/Q</td>
</tr>
<tr>
<td>15.474</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>cntInst2/n72_s3/I2</td>
</tr>
<tr>
<td>15.927</td>
<td>0.453</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>cntInst2/n72_s3/F</td>
</tr>
<tr>
<td>16.401</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>cntInst2/clko_s2/D</td>
</tr>
</table>
<b>Data Required Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>20.000</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>clk</td>
</tr>
<tr>
<td>20.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>20.000</td>
<td>0.000</td>
<td>tINS</td>
<td>RR</td>
<td>34</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>20.360</td>
<td>0.360</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>cntInst2/clko_s2/CLK</td>
</tr>
<tr>
<td>20.325</td>
<td>-0.035</td>
<td>tUnc</td>
<td> </td>
<td> </td>
<td>cntInst2/clko_s2</td>
</tr>
<tr>
<td>20.290</td>
<td>-0.035</td>
<td>tSu</td>
<td> </td>
<td>1</td>
<td>cntInst2/clko_s2</td>
</tr>
</table>
<b>Path Statistics:</b>
<table class="summary_table">
<tr>
<td class="label">Clock Skew:</td>
<td>0.360</td>
</tr>
<tr>
<td class="label">Setup Relationship:</td>
<td>5.000</td>
</tr>
<tr>
<td class="label">Logic Level:</td>
<td>2</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay:</td><td> cell: 0.000, 100.000%; route: 0.000, 0.000%</td></tr>
<tr>
<td class="label">Arrival Data Path Delay:</td><td> cell: 0.453, 32.334%; route: 0.474, 33.833%; tC2Q: 0.474, 33.833%</td></tr>
<tr>
<td class="label">Required Clock Path Delay:</td><td> cell: 0.000, 100.000%; route: 0.000, 0.000%</td></tr>
</table>
<br/>
<h3>Path&nbsp2</h3>
<b>Path Summary:</b></br>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>5.134</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>5.156</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>10.290</td>
</tr>
<tr>
<td class="label">From</td>
<td>myfft/fft_top_inst/ld_inst/xn_cnt_1_s1</td>
</tr>
<tr>
<td class="label">To</td>
<td>outCrt/cnt_r_2_s0</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>pll_fft/rpll_inst/CLKOUT.default_gen_clk[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>cntInst2/sysClk[R]</td>
</tr>
</table>
<b>Data Arrival Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>pll_fft/rpll_inst/CLKOUT.default_gen_clk</td>
</tr>
<tr>
<td>0.500</td>
<td>0.500</td>
<td>tCL</td>
<td>RR</td>
<td>248</td>
<td>pll_fft/rpll_inst/CLKOUT</td>
</tr>
<tr>
<td>0.860</td>
<td>0.360</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>myfft/fft_top_inst/ld_inst/xn_cnt_1_s1/CLK</td>
</tr>
<tr>
<td>1.092</td>
<td>0.232</td>
<td>tC2Q</td>
<td>RF</td>
<td>4</td>
<td>myfft/fft_top_inst/ld_inst/xn_cnt_1_s1/Q</td>
</tr>
<tr>
<td>1.566</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>myfft/fft_top_inst/ld_inst/n42_s1/I1</td>
</tr>
<tr>
<td>2.121</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>2</td>
<td>myfft/fft_top_inst/ld_inst/n42_s1/F</td>
</tr>
<tr>
<td>2.595</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>myfft/fft_top_inst/ld_inst/isLstXn_s2/I3</td>
</tr>
<tr>
<td>2.966</td>
<td>0.371</td>
<td>tINS</td>
<td>FF</td>
<td>4</td>
<td>myfft/fft_top_inst/ld_inst/isLstXn_s2/F</td>
</tr>
<tr>
<td>3.440</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>myfft/fft_top_inst/ld_inst/isLstXn_s1/I1</td>
</tr>
<tr>
<td>3.995</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>14</td>
<td>myfft/fft_top_inst/ld_inst/isLstXn_s1/F</td>
</tr>
<tr>
<td>4.469</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>outCrt/n6_s0/I3</td>
</tr>
<tr>
<td>4.796</td>
<td>0.327</td>
<td>tINS</td>
<td>FR</td>
<td>3</td>
<td>outCrt/n6_s0/F</td>
</tr>
<tr>
<td>5.156</td>
<td>0.360</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>outCrt/cnt_r_2_s0/SET</td>
</tr>
</table>
<b>Data Required Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>cntInst2/sysClk</td>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>4</td>
<td>cntInst2/clko_s2/Q</td>
</tr>
<tr>
<td>10.360</td>
<td>0.360</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>outCrt/cnt_r_2_s0/CLK</td>
</tr>
<tr>
<td>10.325</td>
<td>-0.035</td>
<td>tUnc</td>
<td> </td>
<td> </td>
<td>outCrt/cnt_r_2_s0</td>
</tr>
<tr>
<td>10.290</td>
<td>-0.035</td>
<td>tSu</td>
<td> </td>
<td>1</td>
<td>outCrt/cnt_r_2_s0</td>
</tr>
</table>
<b>Path Statistics:</b>
<table class="summary_table">
<tr>
<td class="label">Clock Skew:</td>
<td>-0.500</td>
</tr>
<tr>
<td class="label">Setup Relationship:</td>
<td>10.000</td>
</tr>
<tr>
<td class="label">Logic Level:</td>
<td>5</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay:</td><td> cell: 0.000, 0.000%; route: 0.360, 100.000%</td></tr>
<tr>
<td class="label">Arrival Data Path Delay:</td><td> cell: 1.808, 42.086%; route: 2.256, 52.514%; tC2Q: 0.232, 5.400%</td></tr>
<tr>
<td class="label">Required Clock Path Delay:</td><td> cell: 0.000, 0.000%; route: 0.360, 100.000%</td></tr>
</table>
<br/>
<h3>Path&nbsp3</h3>
<b>Path Summary:</b></br>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>5.134</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>5.156</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>10.290</td>
</tr>
<tr>
<td class="label">From</td>
<td>myfft/fft_top_inst/ld_inst/xn_cnt_1_s1</td>
</tr>
<tr>
<td class="label">To</td>
<td>outCrt/cnt_r_0_s0</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>pll_fft/rpll_inst/CLKOUT.default_gen_clk[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>cntInst2/sysClk[R]</td>
</tr>
</table>
<b>Data Arrival Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>pll_fft/rpll_inst/CLKOUT.default_gen_clk</td>
</tr>
<tr>
<td>0.500</td>
<td>0.500</td>
<td>tCL</td>
<td>RR</td>
<td>248</td>
<td>pll_fft/rpll_inst/CLKOUT</td>
</tr>
<tr>
<td>0.860</td>
<td>0.360</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>myfft/fft_top_inst/ld_inst/xn_cnt_1_s1/CLK</td>
</tr>
<tr>
<td>1.092</td>
<td>0.232</td>
<td>tC2Q</td>
<td>RF</td>
<td>4</td>
<td>myfft/fft_top_inst/ld_inst/xn_cnt_1_s1/Q</td>
</tr>
<tr>
<td>1.566</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>myfft/fft_top_inst/ld_inst/n42_s1/I1</td>
</tr>
<tr>
<td>2.121</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>2</td>
<td>myfft/fft_top_inst/ld_inst/n42_s1/F</td>
</tr>
<tr>
<td>2.595</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>myfft/fft_top_inst/ld_inst/isLstXn_s2/I3</td>
</tr>
<tr>
<td>2.966</td>
<td>0.371</td>
<td>tINS</td>
<td>FF</td>
<td>4</td>
<td>myfft/fft_top_inst/ld_inst/isLstXn_s2/F</td>
</tr>
<tr>
<td>3.440</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>myfft/fft_top_inst/ld_inst/isLstXn_s1/I1</td>
</tr>
<tr>
<td>3.995</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>14</td>
<td>myfft/fft_top_inst/ld_inst/isLstXn_s1/F</td>
</tr>
<tr>
<td>4.469</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>outCrt/n6_s0/I3</td>
</tr>
<tr>
<td>4.796</td>
<td>0.327</td>
<td>tINS</td>
<td>FR</td>
<td>3</td>
<td>outCrt/n6_s0/F</td>
</tr>
<tr>
<td>5.156</td>
<td>0.360</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>outCrt/cnt_r_0_s0/RESET</td>
</tr>
</table>
<b>Data Required Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>cntInst2/sysClk</td>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>4</td>
<td>cntInst2/clko_s2/Q</td>
</tr>
<tr>
<td>10.360</td>
<td>0.360</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>outCrt/cnt_r_0_s0/CLK</td>
</tr>
<tr>
<td>10.325</td>
<td>-0.035</td>
<td>tUnc</td>
<td> </td>
<td> </td>
<td>outCrt/cnt_r_0_s0</td>
</tr>
<tr>
<td>10.290</td>
<td>-0.035</td>
<td>tSu</td>
<td> </td>
<td>1</td>
<td>outCrt/cnt_r_0_s0</td>
</tr>
</table>
<b>Path Statistics:</b>
<table class="summary_table">
<tr>
<td class="label">Clock Skew:</td>
<td>-0.500</td>
</tr>
<tr>
<td class="label">Setup Relationship:</td>
<td>10.000</td>
</tr>
<tr>
<td class="label">Logic Level:</td>
<td>5</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay:</td><td> cell: 0.000, 0.000%; route: 0.360, 100.000%</td></tr>
<tr>
<td class="label">Arrival Data Path Delay:</td><td> cell: 1.808, 42.086%; route: 2.256, 52.514%; tC2Q: 0.232, 5.400%</td></tr>
<tr>
<td class="label">Required Clock Path Delay:</td><td> cell: 0.000, 0.000%; route: 0.360, 100.000%</td></tr>
</table>
<br/>
<h3>Path&nbsp4</h3>
<b>Path Summary:</b></br>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>5.134</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>5.156</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>10.290</td>
</tr>
<tr>
<td class="label">From</td>
<td>myfft/fft_top_inst/ld_inst/xn_cnt_1_s1</td>
</tr>
<tr>
<td class="label">To</td>
<td>outCrt/cnt_r_1_s0</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>pll_fft/rpll_inst/CLKOUT.default_gen_clk[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>cntInst2/sysClk[R]</td>
</tr>
</table>
<b>Data Arrival Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>pll_fft/rpll_inst/CLKOUT.default_gen_clk</td>
</tr>
<tr>
<td>0.500</td>
<td>0.500</td>
<td>tCL</td>
<td>RR</td>
<td>248</td>
<td>pll_fft/rpll_inst/CLKOUT</td>
</tr>
<tr>
<td>0.860</td>
<td>0.360</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>myfft/fft_top_inst/ld_inst/xn_cnt_1_s1/CLK</td>
</tr>
<tr>
<td>1.092</td>
<td>0.232</td>
<td>tC2Q</td>
<td>RF</td>
<td>4</td>
<td>myfft/fft_top_inst/ld_inst/xn_cnt_1_s1/Q</td>
</tr>
<tr>
<td>1.566</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>myfft/fft_top_inst/ld_inst/n42_s1/I1</td>
</tr>
<tr>
<td>2.121</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>2</td>
<td>myfft/fft_top_inst/ld_inst/n42_s1/F</td>
</tr>
<tr>
<td>2.595</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>myfft/fft_top_inst/ld_inst/isLstXn_s2/I3</td>
</tr>
<tr>
<td>2.966</td>
<td>0.371</td>
<td>tINS</td>
<td>FF</td>
<td>4</td>
<td>myfft/fft_top_inst/ld_inst/isLstXn_s2/F</td>
</tr>
<tr>
<td>3.440</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>myfft/fft_top_inst/ld_inst/isLstXn_s1/I1</td>
</tr>
<tr>
<td>3.995</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>14</td>
<td>myfft/fft_top_inst/ld_inst/isLstXn_s1/F</td>
</tr>
<tr>
<td>4.469</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>outCrt/n6_s0/I3</td>
</tr>
<tr>
<td>4.796</td>
<td>0.327</td>
<td>tINS</td>
<td>FR</td>
<td>3</td>
<td>outCrt/n6_s0/F</td>
</tr>
<tr>
<td>5.156</td>
<td>0.360</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>outCrt/cnt_r_1_s0/RESET</td>
</tr>
</table>
<b>Data Required Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>cntInst2/sysClk</td>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>4</td>
<td>cntInst2/clko_s2/Q</td>
</tr>
<tr>
<td>10.360</td>
<td>0.360</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>outCrt/cnt_r_1_s0/CLK</td>
</tr>
<tr>
<td>10.325</td>
<td>-0.035</td>
<td>tUnc</td>
<td> </td>
<td> </td>
<td>outCrt/cnt_r_1_s0</td>
</tr>
<tr>
<td>10.290</td>
<td>-0.035</td>
<td>tSu</td>
<td> </td>
<td>1</td>
<td>outCrt/cnt_r_1_s0</td>
</tr>
</table>
<b>Path Statistics:</b>
<table class="summary_table">
<tr>
<td class="label">Clock Skew:</td>
<td>-0.500</td>
</tr>
<tr>
<td class="label">Setup Relationship:</td>
<td>10.000</td>
</tr>
<tr>
<td class="label">Logic Level:</td>
<td>5</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay:</td><td> cell: 0.000, 0.000%; route: 0.360, 100.000%</td></tr>
<tr>
<td class="label">Arrival Data Path Delay:</td><td> cell: 1.808, 42.086%; route: 2.256, 52.514%; tC2Q: 0.232, 5.400%</td></tr>
<tr>
<td class="label">Required Clock Path Delay:</td><td> cell: 0.000, 0.000%; route: 0.360, 100.000%</td></tr>
</table>
<br/>
<h3>Path&nbsp5</h3>
<b>Path Summary:</b></br>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>8.230</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>2.095</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>10.325</td>
</tr>
<tr>
<td class="label">From</td>
<td>outCrt/cnt_r_1_s0</td>
</tr>
<tr>
<td class="label">To</td>
<td>outCrt/cnt_r_2_s0</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>cntInst2/sysClk[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>cntInst2/sysClk[R]</td>
</tr>
</table>
<b>Data Arrival Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>cntInst2/sysClk</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>4</td>
<td>cntInst2/clko_s2/Q</td>
</tr>
<tr>
<td>0.360</td>
<td>0.360</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>outCrt/cnt_r_1_s0/CLK</td>
</tr>
<tr>
<td>0.592</td>
<td>0.232</td>
<td>tC2Q</td>
<td>RF</td>
<td>4</td>
<td>outCrt/cnt_r_1_s0/Q</td>
</tr>
<tr>
<td>1.066</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>outCrt/n8_s0/I1</td>
</tr>
<tr>
<td>1.621</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>outCrt/n8_s0/F</td>
</tr>
<tr>
<td>2.095</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>outCrt/cnt_r_2_s0/D</td>
</tr>
</table>
<b>Data Required Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>cntInst2/sysClk</td>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>4</td>
<td>cntInst2/clko_s2/Q</td>
</tr>
<tr>
<td>10.360</td>
<td>0.360</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>outCrt/cnt_r_2_s0/CLK</td>
</tr>
<tr>
<td>10.325</td>
<td>-0.035</td>
<td>tSu</td>
<td> </td>
<td>1</td>
<td>outCrt/cnt_r_2_s0</td>
</tr>
</table>
<b>Path Statistics:</b>
<table class="summary_table">
<tr>
<td class="label">Clock Skew:</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Setup Relationship:</td>
<td>10.000</td>
</tr>
<tr>
<td class="label">Logic Level:</td>
<td>2</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay:</td><td> cell: 0.000, 0.000%; route: 0.360, 100.000%</td></tr>
<tr>
<td class="label">Arrival Data Path Delay:</td><td> cell: 0.555, 31.988%; route: 0.948, 54.640%; tC2Q: 0.232, 13.372%</td></tr>
<tr>
<td class="label">Required Clock Path Delay:</td><td> cell: 0.000, 0.000%; route: 0.360, 100.000%</td></tr>
</table>
<br/>
</div><!-- content -->
</div><!-- main_wrapper -->
</body>
</html>
