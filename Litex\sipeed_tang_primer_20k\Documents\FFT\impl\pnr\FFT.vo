//Copyright (C)2014-2025 Gowin Semiconductor Corporation.
//All rights reserved.
//File Title: Post-PnR Verilog Simulation Model file
//Tool Version: V1.9.11.03 Education
//Created Time: Wed Jul 30 11:38:53 2025

`timescale 100 ps/100 ps
module testfft_top(
	clk,
	key,
	in,
	led,
	out
);
input clk;
input [6:3] key;
input [10:3] in;
output [16:1] led;
output [52:11] out;
wire GND;
wire VCC;
wire clk;
wire clk_d;
wire fftRst;
wire fftStart;
wire [10:3] in;
wire [6:3] key;
wire [6:3] key_d;
wire [16:1] led;
wire [16:4] led_d;
wire [2:0] outState;
wire [52:11] out;
wire [36:36] out_d;
wire [35:35] out_d_11;
wire [33:27] out_d_12;
wire [29:28] out_d_13;
wire [52:11] out_d_14;
wire [15:0] sXnRe;
wire sysClk;
wire \pll_fft/clkoutp_o ;
wire \pll_fft/clkoutd_o ;
wire \pll_fft/clkoutd3_o ;
wire \pll_fft/lock_o ;
wire \cntInst2/n5_4 ;
wire \cntInst2/n5_5 ;
wire \cntInst2/n5_6 ;
wire \cntInst2/n5_7 ;
wire \cntInst2/n5_8 ;
wire \cntInst2/n5_9 ;
wire \cntInst2/n5_10 ;
wire \cntInst2/n5_11 ;
wire \cntInst2/n5_12 ;
wire \cntInst2/n5_13 ;
wire \cntInst2/n72_7 ;
wire \cntInst2/n5_15 ;
wire \cntInst2/n38_1 ;
wire \cntInst2/n38_2 ;
wire \cntInst2/n37_1 ;
wire \cntInst2/n37_2 ;
wire \cntInst2/n36_1 ;
wire \cntInst2/n36_2 ;
wire \cntInst2/n35_1 ;
wire \cntInst2/n35_2 ;
wire \cntInst2/n34_1 ;
wire \cntInst2/n34_2 ;
wire \cntInst2/n33_1 ;
wire \cntInst2/n33_2 ;
wire \cntInst2/n32_1 ;
wire \cntInst2/n32_2 ;
wire \cntInst2/n31_1 ;
wire \cntInst2/n31_2 ;
wire \cntInst2/n30_1 ;
wire \cntInst2/n30_2 ;
wire \cntInst2/n29_1 ;
wire \cntInst2/n29_2 ;
wire \cntInst2/n28_1 ;
wire \cntInst2/n28_2 ;
wire \cntInst2/n27_1 ;
wire \cntInst2/n27_2 ;
wire \cntInst2/n26_1 ;
wire \cntInst2/n26_2 ;
wire \cntInst2/n25_1 ;
wire \cntInst2/n25_2 ;
wire \cntInst2/n24_1 ;
wire \cntInst2/n24_2 ;
wire \cntInst2/n23_1 ;
wire \cntInst2/n23_2 ;
wire \cntInst2/n22_1 ;
wire \cntInst2/n22_2 ;
wire \cntInst2/n21_1 ;
wire \cntInst2/n21_2 ;
wire \cntInst2/n20_1 ;
wire \cntInst2/n20_2 ;
wire \cntInst2/n19_1 ;
wire \cntInst2/n19_2 ;
wire \cntInst2/n18_1 ;
wire \cntInst2/n18_2 ;
wire \cntInst2/n17_1 ;
wire \cntInst2/n17_2 ;
wire \cntInst2/n16_1 ;
wire \cntInst2/n16_2 ;
wire \cntInst2/n15_1 ;
wire \cntInst2/n15_2 ;
wire \cntInst2/n14_1 ;
wire \cntInst2/n14_2 ;
wire \cntInst2/n13_1 ;
wire \cntInst2/n13_2 ;
wire \cntInst2/n12_1 ;
wire \cntInst2/n12_2 ;
wire \cntInst2/n11_1 ;
wire \cntInst2/n11_2 ;
wire \cntInst2/n10_1 ;
wire \cntInst2/n10_2 ;
wire \cntInst2/n9_1 ;
wire \cntInst2/n9_2 ;
wire \cntInst2/n8_1 ;
wire \cntInst2/n8_0_COUT ;
wire \cntInst2/n39_6 ;
wire [31:0] \cntInst2/cnt_r ;
wire \deUstb_inst/isOk ;
wire \deUstb_inst/n8_10 ;
wire \deUstb_inst/n9_7 ;
wire \deUstb_inst/n10_8 ;
wire \deUstb_inst/n7_7 ;
wire [1:0] \deUstb_inst/isOk_r ;
wire [3:0] \deUstb_inst/cnt_r ;
wire \deUstb_rst/isOk ;
wire \deUstb_rst/n8_7 ;
wire \deUstb_rst/n9_7 ;
wire \deUstb_rst/n10_8 ;
wire \deUstb_rst/n7_7 ;
wire [1:0] \deUstb_rst/isOk_r ;
wire [3:0] \deUstb_rst/cnt_r ;
wire \u_testfft/n14_1 ;
wire \u_testfft/n14_2 ;
wire \u_testfft/n13_1 ;
wire \u_testfft/n13_2 ;
wire \u_testfft/n12_1 ;
wire \u_testfft/n12_2 ;
wire \u_testfft/n11_1 ;
wire \u_testfft/n11_2 ;
wire \u_testfft/n10_1 ;
wire \u_testfft/n10_2 ;
wire \u_testfft/n9_1 ;
wire \u_testfft/n9_2 ;
wire \u_testfft/n8_1 ;
wire \u_testfft/n8_2 ;
wire \u_testfft/n7_1 ;
wire \u_testfft/n7_2 ;
wire \u_testfft/n6_1 ;
wire \u_testfft/n6_0_COUT ;
wire \u_testfft/n15_6 ;
wire [9:0] \u_testfft/addr ;
wire [15:0] \u_testfft/U_GW_ROM/DO ;
wire \myfft/fft_top_inst/n941_2 ;
wire \myfft/fft_top_inst/n945_2 ;
wire \myfft/fft_top_inst/slfRst ;
wire \myfft/fft_top_inst/pxna_wre ;
wire \myfft/fft_top_inst/n939_3 ;
wire \myfft/fft_top_inst/n940_3 ;
wire \myfft/fft_top_inst/isLdxn ;
wire \myfft/fft_top_inst/pxna_ad_9_7 ;
wire \myfft/fft_top_inst/pxna_ad_0_8 ;
wire \myfft/fft_top_inst/pxna_ad_8_7 ;
wire \myfft/fft_top_inst/pxna_ad_7_7 ;
wire \myfft/fft_top_inst/pxna_ad_6_7 ;
wire \myfft/fft_top_inst/pxna_ad_5_7 ;
wire \myfft/fft_top_inst/pxna_ad_4_7 ;
wire \myfft/fft_top_inst/pxna_ad_3_7 ;
wire \myfft/fft_top_inst/pxna_ad_2_7 ;
wire \myfft/fft_top_inst/pxna_ad_1_7 ;
wire \myfft/fft_top_inst/pxna_ad_0_7 ;
wire \myfft/fft_top_inst/qxna_ad_9_4 ;
wire \myfft/fft_top_inst/qxna_ad_8_4 ;
wire \myfft/fft_top_inst/qxna_ad_7_4 ;
wire \myfft/fft_top_inst/qxna_ad_6_4 ;
wire \myfft/fft_top_inst/qxna_ad_5_4 ;
wire \myfft/fft_top_inst/qxna_ad_4_4 ;
wire \myfft/fft_top_inst/qxna_ad_3_4 ;
wire \myfft/fft_top_inst/qxna_ad_2_4 ;
wire \myfft/fft_top_inst/qxna_ad_1_4 ;
wire \myfft/fft_top_inst/qxna_ad_0_4 ;
wire \myfft/fft_top_inst/qxna_wre ;
wire \myfft/fft_top_inst/soud_reg0 ;
wire \myfft/fft_top_inst/n944_3 ;
wire \myfft/fft_top_inst/pxna_ad_9_5 ;
wire \myfft/fft_top_inst/pxna_ad_8_5 ;
wire \myfft/fft_top_inst/pxna_ad_7_5 ;
wire \myfft/fft_top_inst/pxna_ad_6_5 ;
wire \myfft/fft_top_inst/pxna_ad_5_5 ;
wire \myfft/fft_top_inst/pxna_ad_4_5 ;
wire \myfft/fft_top_inst/pxna_ad_3_5 ;
wire \myfft/fft_top_inst/pxna_ad_2_5 ;
wire \myfft/fft_top_inst/pxna_ad_1_5 ;
wire \myfft/fft_top_inst/pxna_ad_0_5 ;
wire \myfft/fft_top_inst/qxnb_do_re_0_40 ;
wire \myfft/fft_top_inst/ftCtl_ea ;
wire \myfft/fft_top_inst/corePa_wre ;
wire \myfft/fft_top_inst/qxnb_wre ;
wire \myfft/fft_top_inst/fft_ok ;
wire \myfft/fft_top_inst/dSrc ;
wire \myfft/fft_top_inst/ud_ok ;
wire [15:0] \myfft/fft_top_inst/pxna_di_re ;
wire [15:0] \myfft/fft_top_inst/pxna_di_im ;
wire [9:0] \myfft/fft_top_inst/qxna_ad ;
wire [9:0] \myfft/fft_top_inst/corePa_ad ;
wire [1:0] \myfft/fft_top_inst/stage ;
wire [15:0] \myfft/fft_top_inst/ud_xkm_re ;
wire [15:0] \myfft/fft_top_inst/pxnb_do_re_b ;
wire [15:0] \myfft/fft_top_inst/pxna_do_re_b ;
wire [15:0] \myfft/fft_top_inst/pxnb_do_im_b ;
wire [15:0] \myfft/fft_top_inst/pxna_do_im_b ;
wire [15:0] \myfft/fft_top_inst/ud_xkm_im ;
wire [15:0] \myfft/fft_top_inst/coreQb_do_re ;
wire [15:0] \myfft/fft_top_inst/qxna_do_re_b ;
wire [15:0] \myfft/fft_top_inst/qxna_do_im_b ;
wire [15:0] \myfft/fft_top_inst/coreQb_do_im ;
wire [9:0] \myfft/fft_top_inst/ld_xnm_ad ;
wire [15:0] \myfft/fft_top_inst/btfy_xkare ;
wire [15:0] \myfft/fft_top_inst/btfy_xkaim ;
wire [15:0] \myfft/fft_top_inst/qxnb_di_re ;
wire [15:0] \myfft/fft_top_inst/qxnb_di_im ;
wire [9:0] \myfft/fft_top_inst/twBtfy_upwad ;
wire [9:0] \myfft/fft_top_inst/corePb_ad ;
wire [9:0] \myfft/fft_top_inst/qxnb_ad ;
wire [9:0] \myfft/fft_top_inst/cclt_upwad_2 ;
wire [9:0] \myfft/fft_top_inst/ud_xkm_ad ;
wire [15:0] \myfft/fft_top_inst/mem_inst/qmem/qxnb_do_im_b ;
wire [15:0] \myfft/fft_top_inst/mem_inst/qmem/reMem/qxnb_do_re_b ;
wire \myfft/fft_top_inst/ld_inst/slfRst_0 ;
wire \myfft/fft_top_inst/ld_inst/n35_3 ;
wire \myfft/fft_top_inst/ld_inst/n37_3 ;
wire \myfft/fft_top_inst/ld_inst/n39_3 ;
wire \myfft/fft_top_inst/ld_inst/n40_3 ;
wire \myfft/fft_top_inst/ld_inst/n42_3 ;
wire \myfft/fft_top_inst/ld_inst/n43_3 ;
wire \myfft/fft_top_inst/ld_inst/n44_3 ;
wire \myfft/fft_top_inst/ld_inst/n9_5 ;
wire \myfft/fft_top_inst/ld_inst/isLstXn_5 ;
wire \myfft/fft_top_inst/ld_inst/n35_4 ;
wire \myfft/fft_top_inst/ld_inst/n35_5 ;
wire \myfft/fft_top_inst/ld_inst/n37_4 ;
wire \myfft/fft_top_inst/ld_inst/n42_4 ;
wire \myfft/fft_top_inst/ld_inst/isLstXn_7 ;
wire \myfft/fft_top_inst/ld_inst/isLstXn_9 ;
wire \myfft/fft_top_inst/ld_inst/n40_6 ;
wire \myfft/fft_top_inst/ld_inst/n41_5 ;
wire \myfft/fft_top_inst/ld_inst/n38_5 ;
wire \myfft/fft_top_inst/ld_inst/n36_5 ;
wire \myfft/fft_top_inst/tf_inst/slfRst_3 ;
wire \myfft/fft_top_inst/tf_inst/n417_5 ;
wire \myfft/fft_top_inst/tf_inst/btfy_ea ;
wire \myfft/fft_top_inst/tf_inst/twBtfy_ea ;
wire [15:0] \myfft/fft_top_inst/tf_inst/btfy_twre ;
wire [15:0] \myfft/fft_top_inst/tf_inst/btfy_twim ;
wire [9:0] \myfft/fft_top_inst/tf_inst/tw_ad ;
wire [9:0] \myfft/fft_top_inst/tf_inst/twBtfy_dwnad ;
wire [15:0] \myfft/fft_top_inst/tf_inst/btfy_xnare ;
wire [15:0] \myfft/fft_top_inst/tf_inst/btfy_xnaim ;
wire [15:0] \myfft/fft_top_inst/tf_inst/btfy_xnbre ;
wire [15:0] \myfft/fft_top_inst/tf_inst/btfy_xnbim ;
wire [31:16] \myfft/fft_top_inst/tf_inst/tw_inst/DO ;
wire [31:16] \myfft/fft_top_inst/tf_inst/tw_inst/DO_0 ;
wire \myfft/fft_top_inst/tf_inst/btfy_inst/constructG_0_4 ;
wire \myfft/fft_top_inst/tf_inst/btfy_inst/constructG_1_4 ;
wire \myfft/fft_top_inst/tf_inst/btfy_inst/constructG_2_4 ;
wire \myfft/fft_top_inst/tf_inst/btfy_inst/constructG_3_4 ;
wire \myfft/fft_top_inst/tf_inst/btfy_inst/constructG_4_4 ;
wire \myfft/fft_top_inst/tf_inst/btfy_inst/constructG_5_4 ;
wire \myfft/fft_top_inst/tf_inst/btfy_inst/constructG_6_4 ;
wire \myfft/fft_top_inst/tf_inst/btfy_inst/constructG_7_4 ;
wire \myfft/fft_top_inst/tf_inst/btfy_inst/constructG_8_4 ;
wire \myfft/fft_top_inst/tf_inst/btfy_inst/constructG_9_4 ;
wire \myfft/fft_top_inst/tf_inst/btfy_inst/constructG_10_4 ;
wire \myfft/fft_top_inst/tf_inst/btfy_inst/constructG_11_4 ;
wire \myfft/fft_top_inst/tf_inst/btfy_inst/constructG_12_4 ;
wire \myfft/fft_top_inst/tf_inst/btfy_inst/constructG_13_4 ;
wire \myfft/fft_top_inst/tf_inst/btfy_inst/constructG_14_4 ;
wire \myfft/fft_top_inst/tf_inst/btfy_inst/constructG_15_4 ;
wire \myfft/fft_top_inst/tf_inst/btfy_inst/constructG_16_1_COUT ;
wire \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_0_4 ;
wire \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_1_4 ;
wire \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_2_4 ;
wire \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_3_4 ;
wire \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_4_4 ;
wire \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_5_4 ;
wire \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_6_4 ;
wire \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_7_4 ;
wire \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_8_4 ;
wire \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_9_4 ;
wire \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_10_4 ;
wire \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_11_4 ;
wire \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_12_4 ;
wire \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_13_4 ;
wire \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_14_4 ;
wire \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_15_4 ;
wire \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_16_1_COUT ;
wire \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_0_5 ;
wire \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_1_5 ;
wire \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_2_5 ;
wire \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_3_5 ;
wire \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_4_5 ;
wire \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_5_5 ;
wire \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_6_5 ;
wire \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_7_5 ;
wire \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_8_5 ;
wire \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_9_5 ;
wire \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_10_5 ;
wire \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_11_5 ;
wire \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_12_5 ;
wire \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_13_5 ;
wire \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_14_5 ;
wire \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_15_5 ;
wire \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_16_1_COUT ;
wire \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_0_5 ;
wire \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_1_5 ;
wire \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_2_5 ;
wire \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_3_5 ;
wire \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_4_5 ;
wire \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_5_5 ;
wire \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_6_5 ;
wire \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_7_5 ;
wire \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_8_5 ;
wire \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_9_5 ;
wire \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_10_5 ;
wire \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_11_5 ;
wire \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_12_5 ;
wire \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_13_5 ;
wire \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_14_5 ;
wire \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_15_5 ;
wire \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_16_1_COUT ;
wire [15:0] \myfft/fft_top_inst/tf_inst/btfy_inst/mltA ;
wire [15:0] \myfft/fft_top_inst/tf_inst/btfy_inst/mltB ;
wire [0:0] \myfft/fft_top_inst/tf_inst/btfy_inst/constructG ;
wire [0:0] \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH ;
wire [0:0] \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI ;
wire [0:0] \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ ;
wire [15:0] \myfft/fft_top_inst/tf_inst/btfy_inst/reconstructX ;
wire [15:0] \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.reconstructY ;
wire [53:0] \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.multX/DOUT ;
wire [54:0] \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.multX/CASO ;
wire [17:0] \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.multX/SOA ;
wire [17:0] \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.multX/SOB ;
wire [53:0] \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.multY/DOUT ;
wire [54:0] \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.multY/CASO ;
wire [17:0] \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.multY/SOA ;
wire [17:0] \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.multY/SOB ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstLvl ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/lcRst ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n148_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n150_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n164_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n165_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n166_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n167_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n168_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n169_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n170_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n171_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n172_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n217_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n218_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n219_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n220_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n221_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n222_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n223_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n224_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n225_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n250_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n251_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n252_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n253_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n254_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n255_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n256_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n257_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n258_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n344_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n345_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n347_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n349_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n350_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n352_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n353_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n354_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n357_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n358_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n359_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n360_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n361_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n362_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n363_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n407_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n408_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n409_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n410_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n411_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n412_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n413_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n414_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n415_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n441_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n442_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n443_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n444_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n445_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n446_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n447_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n448_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n449_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n492_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n493_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n494_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n495_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n496_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n497_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n498_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n499_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n500_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n501_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_5 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_6 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_7 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_8 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_5 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_6 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_7 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_8 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n148_5 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n217_4 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n219_4 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n221_4 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n221_5 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n222_4 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n223_4 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n224_4 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n344_4 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n344_5 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n349_4 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n349_5 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n354_4 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n354_5 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n355_5 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n356_4 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n356_5 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n357_4 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n357_5 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n358_4 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n358_5 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n359_4 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n360_4 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n361_4 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n361_5 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n362_4 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n363_4 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_10 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_11 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_12 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_13 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_14 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_15 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_16 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_9 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_10 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_11 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_12 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_13 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_14 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_15 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_16 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_17 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_19 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_20 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n217_5 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n354_7 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n357_6 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n357_7 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n357_8 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n359_5 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n360_5 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n360_6 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n362_5 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_17 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_18 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_19 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_21 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_22 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_23 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_24 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_26 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_21 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n149_5 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n347_6 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n355_7 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n354_9 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n148_7 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n356_7 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n355_9 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n351_5 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n348_5 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n346_5 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n226_5 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n151_5 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n440_7 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n416_7 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n259_7 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n163_7 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/dwnad_w_0_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/dwnad_w_1_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/dwnad_w_2_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/dwnad_w_3_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/dwnad_w_4_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/dwnad_w_5_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/dwnad_w_6_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/dwnad_w_7_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/dwnad_w_8_3 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/dwnad_w_9_1_COUT ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n481_1 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n481_2 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n480_1 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n480_2 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n479_1 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n479_2 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n478_1 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n478_2 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n477_1 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n477_2 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n476_1 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n476_2 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n475_1 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n475_2 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n474_1 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n474_2 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n473_1 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n473_2 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n472_1 ;
wire \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n472_0_COUT ;
wire [3:0] \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/l_r ;
wire [9:0] \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl ;
wire [9:0] \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r ;
wire [9:0] \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg ;
wire [9:0] \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r ;
wire [9:0] \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/wspan ;
wire [9:0] \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/tstep ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/isBS2 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/isLE ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/slfRst_2 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/n53_3 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/n54_3 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/n55_3 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/n56_3 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/n58_3 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/n60_3 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/n61_3 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/n62_3 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/n95_3 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/n111_3 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/n112_3 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/n113_3 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/n114_3 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/n115_3 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/n116_3 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/n117_3 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/n118_3 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/n119_3 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/n120_3 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/n121_3 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/n122_3 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/n123_3 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/n124_3 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/n125_3 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/n126_3 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/n127_3 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/n128_3 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/n129_3 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/n130_3 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/n131_3 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/n132_3 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/n133_3 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/n134_3 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/n135_3 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/n136_3 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/n137_3 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/n138_3 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/n139_3 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/n140_3 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/n141_3 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/n142_3 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/n143_3 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/n144_3 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/n145_3 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/n146_3 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/n147_3 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/n148_3 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/n149_3 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/n150_3 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/isBS0_5 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/isBS2_5 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/n53_5 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/n53_6 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/n55_4 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/n60_4 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/n95_4 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/twBtfy_ea_3 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/isBS0_6 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/isBS0_7 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/n96_5 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/n54_6 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/n53_8 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/n58_6 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/lcRst ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/isBS1 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/isBS0 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/n99_5 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/n98_5 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/n97_5 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/n59_5 ;
wire \myfft/fft_top_inst/tf_inst/ctrl_inst/n57_5 ;
wire [9:0] \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt ;
wire [3:0] \myfft/fft_top_inst/tf_inst/ctrl_inst/lvl ;
wire [9:0] \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_1 ;
wire [9:0] \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_1 ;
wire [9:0] \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_2 ;
wire \myfft/fft_top_inst/udxk_inst/slfRst_4 ;
wire \myfft/fft_top_inst/udxk_inst/n45_3 ;
wire \myfft/fft_top_inst/udxk_inst/n47_3 ;
wire \myfft/fft_top_inst/udxk_inst/n48_3 ;
wire \myfft/fft_top_inst/udxk_inst/n50_3 ;
wire \myfft/fft_top_inst/udxk_inst/n52_3 ;
wire \myfft/fft_top_inst/udxk_inst/n53_3 ;
wire \myfft/fft_top_inst/udxk_inst/n54_3 ;
wire \myfft/fft_top_inst/udxk_inst/n9_5 ;
wire \myfft/fft_top_inst/udxk_inst/n202_4 ;
wire \myfft/fft_top_inst/udxk_inst/isLstXk_5 ;
wire \myfft/fft_top_inst/udxk_inst/isLstXk_6 ;
wire \myfft/fft_top_inst/udxk_inst/n47_4 ;
wire \myfft/fft_top_inst/udxk_inst/n47_5 ;
wire \myfft/fft_top_inst/udxk_inst/n50_4 ;
wire \myfft/fft_top_inst/udxk_inst/n52_4 ;
wire \myfft/fft_top_inst/udxk_inst/n45_6 ;
wire \myfft/fft_top_inst/udxk_inst/n51_5 ;
wire \myfft/fft_top_inst/udxk_inst/n49_5 ;
wire \myfft/fft_top_inst/udxk_inst/n46_5 ;
wire \myfft/fft_top_inst/udxk_inst/n202_6 ;
wire \myfft/fft_top_inst/udxk_inst/start_reg ;
wire [9:0] \myfft/fft_top_inst/udxk_inst/ud_xk_id ;
wire \outCrt/n6_3 ;
wire \outCrt/n9_5 ;
wire \outCrt/n8_5 ;
wire \outCrt/n10_7 ;
VCC VCC_cZ (
  .V(VCC)
);
GND GND_cZ (
  .G(GND)
);
GSR GSR (
	.GSRI(VCC)
);
IBUF clk_ibuf (
	.I(clk),
	.O(clk_d)
);
IBUF key_3_ibuf (
	.I(key[3]),
	.O(key_d[3])
);
IBUF key_4_ibuf (
	.I(key[4]),
	.O(key_d[4])
);
IBUF key_5_ibuf (
	.I(key[5]),
	.O(key_d[5])
);
IBUF key_6_ibuf (
	.I(key[6]),
	.O(key_d[6])
);
IBUF in_3_ibuf (
	.I(in[3]),
	.O(led_d[4])
);
IBUF in_4_ibuf (
	.I(in[4]),
	.O(led_d[5])
);
IBUF in_5_ibuf (
	.I(in[5]),
	.O(led_d[6])
);
IBUF in_6_ibuf (
	.I(in[6]),
	.O(led_d[7])
);
IBUF in_7_ibuf (
	.I(in[7]),
	.O(led_d[8])
);
IBUF in_8_ibuf (
	.I(in[8]),
	.O(led_d[9])
);
IBUF in_9_ibuf (
	.I(in[9]),
	.O(led_d[10])
);
IBUF in_10_ibuf (
	.I(in[10]),
	.O(led_d[11])
);
OBUF led_1_obuf (
	.I(out_d_13[28]),
	.O(led[1])
);
OBUF led_2_obuf (
	.I(out_d_12[30]),
	.O(led[2])
);
OBUF led_3_obuf (
	.I(out_d_14[32]),
	.O(led[3])
);
OBUF led_4_obuf (
	.I(led_d[4]),
	.O(led[4])
);
OBUF led_5_obuf (
	.I(led_d[5]),
	.O(led[5])
);
OBUF led_6_obuf (
	.I(led_d[6]),
	.O(led[6])
);
OBUF led_7_obuf (
	.I(led_d[7]),
	.O(led[7])
);
OBUF led_8_obuf (
	.I(led_d[8]),
	.O(led[8])
);
OBUF led_9_obuf (
	.I(led_d[9]),
	.O(led[9])
);
OBUF led_10_obuf (
	.I(led_d[10]),
	.O(led[10])
);
OBUF led_11_obuf (
	.I(led_d[11]),
	.O(led[11])
);
OBUF led_12_obuf (
	.I(GND),
	.O(led[12])
);
OBUF led_13_obuf (
	.I(GND),
	.O(led[13])
);
OBUF led_14_obuf (
	.I(GND),
	.O(led[14])
);
OBUF led_15_obuf (
	.I(GND),
	.O(led[15])
);
OBUF led_16_obuf (
	.I(led_d[16]),
	.O(led[16])
);
OBUF out_11_obuf (
	.I(out_d_14[11]),
	.O(out[11])
);
OBUF out_12_obuf (
	.I(out_d_14[12]),
	.O(out[12])
);
OBUF out_13_obuf (
	.I(out_d_14[13]),
	.O(out[13])
);
OBUF out_14_obuf (
	.I(out_d_14[14]),
	.O(out[14])
);
OBUF out_15_obuf (
	.I(out_d_14[15]),
	.O(out[15])
);
OBUF out_16_obuf (
	.I(out_d_14[16]),
	.O(out[16])
);
OBUF out_17_obuf (
	.I(out_d_14[17]),
	.O(out[17])
);
OBUF out_18_obuf (
	.I(out_d_14[18]),
	.O(out[18])
);
OBUF out_19_obuf (
	.I(out_d_14[19]),
	.O(out[19])
);
OBUF out_20_obuf (
	.I(out_d_14[20]),
	.O(out[20])
);
OBUF out_21_obuf (
	.I(out_d_14[21]),
	.O(out[21])
);
OBUF out_22_obuf (
	.I(out_d_14[22]),
	.O(out[22])
);
OBUF out_23_obuf (
	.I(out_d_14[23]),
	.O(out[23])
);
OBUF out_24_obuf (
	.I(out_d_14[24]),
	.O(out[24])
);
OBUF out_25_obuf (
	.I(out_d_14[25]),
	.O(out[25])
);
OBUF out_26_obuf (
	.I(out_d_14[26]),
	.O(out[26])
);
OBUF out_27_obuf (
	.I(out_d_12[27]),
	.O(out[27])
);
OBUF out_28_obuf (
	.I(out_d_13[28]),
	.O(out[28])
);
OBUF out_29_obuf (
	.I(out_d_13[29]),
	.O(out[29])
);
OBUF out_30_obuf (
	.I(out_d_12[30]),
	.O(out[30])
);
OBUF out_31_obuf (
	.I(out_d_12[31]),
	.O(out[31])
);
OBUF out_32_obuf (
	.I(out_d_14[32]),
	.O(out[32])
);
OBUF out_33_obuf (
	.I(out_d_12[33]),
	.O(out[33])
);
OBUF out_34_obuf (
	.I(out_d_11[35]),
	.O(out[34])
);
OBUF out_35_obuf (
	.I(out_d_11[35]),
	.O(out[35])
);
OBUF out_36_obuf (
	.I(out_d[36]),
	.O(out[36])
);
OBUF out_37_obuf (
	.I(out_d_14[37]),
	.O(out[37])
);
OBUF out_38_obuf (
	.I(out_d_14[38]),
	.O(out[38])
);
OBUF out_39_obuf (
	.I(out_d_14[39]),
	.O(out[39])
);
OBUF out_40_obuf (
	.I(out_d_14[40]),
	.O(out[40])
);
OBUF out_41_obuf (
	.I(out_d_14[41]),
	.O(out[41])
);
OBUF out_42_obuf (
	.I(out_d_14[42]),
	.O(out[42])
);
OBUF out_43_obuf (
	.I(out_d_14[43]),
	.O(out[43])
);
OBUF out_44_obuf (
	.I(out_d_14[44]),
	.O(out[44])
);
OBUF out_45_obuf (
	.I(out_d_14[45]),
	.O(out[45])
);
OBUF out_46_obuf (
	.I(out_d_14[46]),
	.O(out[46])
);
OBUF out_47_obuf (
	.I(out_d_14[47]),
	.O(out[47])
);
OBUF out_48_obuf (
	.I(out_d_14[48]),
	.O(out[48])
);
OBUF out_49_obuf (
	.I(out_d_14[49]),
	.O(out[49])
);
OBUF out_50_obuf (
	.I(out_d_14[50]),
	.O(out[50])
);
OBUF out_51_obuf (
	.I(out_d_14[51]),
	.O(out[51])
);
OBUF out_52_obuf (
	.I(out_d_14[52]),
	.O(out[52])
);
LUT3 out_d_36_s (
	.I0(outState[0]),
	.I1(outState[1]),
	.I2(outState[2]),
	.F(out_d[36])
);
defparam out_d_36_s.INIT=8'h10;
LUT4 led_d_16_s (
	.I0(key_d[3]),
	.I1(key_d[4]),
	.I2(key_d[5]),
	.I3(key_d[6]),
	.F(led_d[16])
);
defparam led_d_16_s.INIT=16'h7FFF;
rPLL \pll_fft/rpll_inst  (
	.RESET(GND),
	.RESET_P(GND),
	.CLKIN(clk_d),
	.CLKFB(GND),
	.PSDA({GND, GND, GND, GND}),
	.FDLY({GND, GND, GND, GND}),
	.DUTYDA({GND, GND, GND, GND}),
	.ODSEL({GND, GND, GND, GND, GND, GND}),
	.FBDSEL({GND, GND, GND, GND, GND, GND}),
	.IDSEL({GND, GND, GND, GND, GND, GND}),
	.CLKOUT(out_d_11[35]),
	.LOCK(\pll_fft/lock_o ),
	.CLKOUTP(\pll_fft/clkoutp_o ),
	.CLKOUTD(\pll_fft/clkoutd_o ),
	.CLKOUTD3(\pll_fft/clkoutd3_o )
);
defparam \pll_fft/rpll_inst .CLKFB_SEL="internal";
defparam \pll_fft/rpll_inst .DYN_SDIV_SEL=2;
defparam \pll_fft/rpll_inst .DYN_IDIV_SEL="false";
defparam \pll_fft/rpll_inst .DYN_ODIV_SEL="false";
defparam \pll_fft/rpll_inst .IDIV_SEL=4;
defparam \pll_fft/rpll_inst .DYN_FBDIV_SEL="false";
defparam \pll_fft/rpll_inst .FBDIV_SEL=0;
defparam \pll_fft/rpll_inst .DYN_DA_EN="true";
defparam \pll_fft/rpll_inst .PSDA_SEL="0000";
defparam \pll_fft/rpll_inst .DUTYDA_SEL="1000";
defparam \pll_fft/rpll_inst .CLKOUT_BYPASS="false";
defparam \pll_fft/rpll_inst .CLKOUTD_BYPASS="false";
defparam \pll_fft/rpll_inst .CLKOUTP_BYPASS="false";
defparam \pll_fft/rpll_inst .CLKOUTD_SRC="CLKOUT";
defparam \pll_fft/rpll_inst .CLKOUTD3_SRC="CLKOUT";
defparam \pll_fft/rpll_inst .CLKOUT_FT_DIR=1'b1;
defparam \pll_fft/rpll_inst .CLKOUTP_FT_DIR=1'b1;
defparam \pll_fft/rpll_inst .CLKOUT_DLY_STEP=0;
defparam \pll_fft/rpll_inst .CLKOUTP_DLY_STEP=0;
defparam \pll_fft/rpll_inst .FCLKIN="50";
defparam \pll_fft/rpll_inst .ODIV_SEL=112;
defparam \pll_fft/rpll_inst .DEVICE="GW2A-18C";
LUT4 \cntInst2/n5_s1  (
	.I0(\cntInst2/n5_6 ),
	.I1(\cntInst2/n5_7 ),
	.I2(\cntInst2/n5_8 ),
	.I3(\cntInst2/n5_9 ),
	.F(\cntInst2/n5_4 )
);
defparam \cntInst2/n5_s1 .INIT=16'h8000;
LUT4 \cntInst2/n5_s2  (
	.I0(\cntInst2/n5_10 ),
	.I1(\cntInst2/n5_11 ),
	.I2(\cntInst2/n5_12 ),
	.I3(\cntInst2/n5_13 ),
	.F(\cntInst2/n5_5 )
);
defparam \cntInst2/n5_s2 .INIT=16'h8000;
LUT4 \cntInst2/n5_s3  (
	.I0(\cntInst2/cnt_r [17]),
	.I1(\cntInst2/cnt_r [18]),
	.I2(\cntInst2/cnt_r [20]),
	.I3(\cntInst2/cnt_r [23]),
	.F(\cntInst2/n5_6 )
);
defparam \cntInst2/n5_s3 .INIT=16'h0001;
LUT4 \cntInst2/n5_s4  (
	.I0(\cntInst2/cnt_r [24]),
	.I1(\cntInst2/cnt_r [27]),
	.I2(\cntInst2/cnt_r [29]),
	.I3(\cntInst2/cnt_r [30]),
	.F(\cntInst2/n5_7 )
);
defparam \cntInst2/n5_s4 .INIT=16'h0001;
LUT4 \cntInst2/n5_s5  (
	.I0(\cntInst2/cnt_r [0]),
	.I1(\cntInst2/cnt_r [3]),
	.I2(\cntInst2/cnt_r [5]),
	.I3(\cntInst2/cnt_r [6]),
	.F(\cntInst2/n5_8 )
);
defparam \cntInst2/n5_s5 .INIT=16'h0001;
LUT4 \cntInst2/n5_s6  (
	.I0(\cntInst2/cnt_r [9]),
	.I1(\cntInst2/cnt_r [10]),
	.I2(\cntInst2/cnt_r [12]),
	.I3(\cntInst2/cnt_r [15]),
	.F(\cntInst2/n5_9 )
);
defparam \cntInst2/n5_s6 .INIT=16'h0001;
LUT4 \cntInst2/n5_s7  (
	.I0(\cntInst2/cnt_r [16]),
	.I1(\cntInst2/cnt_r [19]),
	.I2(\cntInst2/cnt_r [21]),
	.I3(\cntInst2/cnt_r [22]),
	.F(\cntInst2/n5_10 )
);
defparam \cntInst2/n5_s7 .INIT=16'h0001;
LUT4 \cntInst2/n5_s8  (
	.I0(\cntInst2/cnt_r [25]),
	.I1(\cntInst2/cnt_r [26]),
	.I2(\cntInst2/cnt_r [28]),
	.I3(\cntInst2/cnt_r [31]),
	.F(\cntInst2/n5_11 )
);
defparam \cntInst2/n5_s8 .INIT=16'h0001;
LUT4 \cntInst2/n5_s9  (
	.I0(\cntInst2/cnt_r [1]),
	.I1(\cntInst2/cnt_r [2]),
	.I2(\cntInst2/cnt_r [4]),
	.I3(\cntInst2/cnt_r [7]),
	.F(\cntInst2/n5_12 )
);
defparam \cntInst2/n5_s9 .INIT=16'h0001;
LUT4 \cntInst2/n5_s10  (
	.I0(\cntInst2/cnt_r [8]),
	.I1(\cntInst2/cnt_r [11]),
	.I2(\cntInst2/cnt_r [13]),
	.I3(\cntInst2/cnt_r [14]),
	.F(\cntInst2/n5_13 )
);
defparam \cntInst2/n5_s10 .INIT=16'h0001;
LUT3 \cntInst2/n72_s3  (
	.I0(\cntInst2/n5_4 ),
	.I1(\cntInst2/n5_5 ),
	.I2(sysClk),
	.F(\cntInst2/n72_7 )
);
defparam \cntInst2/n72_s3 .INIT=8'h78;
LUT2 \cntInst2/n5_s11  (
	.I0(\cntInst2/n5_4 ),
	.I1(\cntInst2/n5_5 ),
	.F(\cntInst2/n5_15 )
);
defparam \cntInst2/n5_s11 .INIT=4'h8;
DFFR \cntInst2/cnt_r_30_s0  (
	.D(\cntInst2/n9_1 ),
	.CLK(clk_d),
	.RESET(\cntInst2/n5_15 ),
	.Q(\cntInst2/cnt_r [30])
);
defparam \cntInst2/cnt_r_30_s0 .INIT=1'b0;
DFFR \cntInst2/cnt_r_29_s0  (
	.D(\cntInst2/n10_1 ),
	.CLK(clk_d),
	.RESET(\cntInst2/n5_15 ),
	.Q(\cntInst2/cnt_r [29])
);
defparam \cntInst2/cnt_r_29_s0 .INIT=1'b0;
DFFR \cntInst2/cnt_r_28_s0  (
	.D(\cntInst2/n11_1 ),
	.CLK(clk_d),
	.RESET(\cntInst2/n5_15 ),
	.Q(\cntInst2/cnt_r [28])
);
defparam \cntInst2/cnt_r_28_s0 .INIT=1'b0;
DFFR \cntInst2/cnt_r_27_s0  (
	.D(\cntInst2/n12_1 ),
	.CLK(clk_d),
	.RESET(\cntInst2/n5_15 ),
	.Q(\cntInst2/cnt_r [27])
);
defparam \cntInst2/cnt_r_27_s0 .INIT=1'b0;
DFFR \cntInst2/cnt_r_26_s0  (
	.D(\cntInst2/n13_1 ),
	.CLK(clk_d),
	.RESET(\cntInst2/n5_15 ),
	.Q(\cntInst2/cnt_r [26])
);
defparam \cntInst2/cnt_r_26_s0 .INIT=1'b0;
DFFR \cntInst2/cnt_r_25_s0  (
	.D(\cntInst2/n14_1 ),
	.CLK(clk_d),
	.RESET(\cntInst2/n5_15 ),
	.Q(\cntInst2/cnt_r [25])
);
defparam \cntInst2/cnt_r_25_s0 .INIT=1'b0;
DFFR \cntInst2/cnt_r_24_s0  (
	.D(\cntInst2/n15_1 ),
	.CLK(clk_d),
	.RESET(\cntInst2/n5_15 ),
	.Q(\cntInst2/cnt_r [24])
);
defparam \cntInst2/cnt_r_24_s0 .INIT=1'b0;
DFFR \cntInst2/cnt_r_23_s0  (
	.D(\cntInst2/n16_1 ),
	.CLK(clk_d),
	.RESET(\cntInst2/n5_15 ),
	.Q(\cntInst2/cnt_r [23])
);
defparam \cntInst2/cnt_r_23_s0 .INIT=1'b0;
DFFR \cntInst2/cnt_r_22_s0  (
	.D(\cntInst2/n17_1 ),
	.CLK(clk_d),
	.RESET(\cntInst2/n5_15 ),
	.Q(\cntInst2/cnt_r [22])
);
defparam \cntInst2/cnt_r_22_s0 .INIT=1'b0;
DFFR \cntInst2/cnt_r_21_s0  (
	.D(\cntInst2/n18_1 ),
	.CLK(clk_d),
	.RESET(\cntInst2/n5_15 ),
	.Q(\cntInst2/cnt_r [21])
);
defparam \cntInst2/cnt_r_21_s0 .INIT=1'b0;
DFFR \cntInst2/cnt_r_20_s0  (
	.D(\cntInst2/n19_1 ),
	.CLK(clk_d),
	.RESET(\cntInst2/n5_15 ),
	.Q(\cntInst2/cnt_r [20])
);
defparam \cntInst2/cnt_r_20_s0 .INIT=1'b0;
DFFR \cntInst2/cnt_r_19_s0  (
	.D(\cntInst2/n20_1 ),
	.CLK(clk_d),
	.RESET(\cntInst2/n5_15 ),
	.Q(\cntInst2/cnt_r [19])
);
defparam \cntInst2/cnt_r_19_s0 .INIT=1'b0;
DFFR \cntInst2/cnt_r_18_s0  (
	.D(\cntInst2/n21_1 ),
	.CLK(clk_d),
	.RESET(\cntInst2/n5_15 ),
	.Q(\cntInst2/cnt_r [18])
);
defparam \cntInst2/cnt_r_18_s0 .INIT=1'b0;
DFFR \cntInst2/cnt_r_17_s0  (
	.D(\cntInst2/n22_1 ),
	.CLK(clk_d),
	.RESET(\cntInst2/n5_15 ),
	.Q(\cntInst2/cnt_r [17])
);
defparam \cntInst2/cnt_r_17_s0 .INIT=1'b0;
DFFR \cntInst2/cnt_r_16_s0  (
	.D(\cntInst2/n23_1 ),
	.CLK(clk_d),
	.RESET(\cntInst2/n5_15 ),
	.Q(\cntInst2/cnt_r [16])
);
defparam \cntInst2/cnt_r_16_s0 .INIT=1'b0;
DFFR \cntInst2/cnt_r_15_s0  (
	.D(\cntInst2/n24_1 ),
	.CLK(clk_d),
	.RESET(\cntInst2/n5_15 ),
	.Q(\cntInst2/cnt_r [15])
);
defparam \cntInst2/cnt_r_15_s0 .INIT=1'b0;
DFFR \cntInst2/cnt_r_14_s0  (
	.D(\cntInst2/n25_1 ),
	.CLK(clk_d),
	.RESET(\cntInst2/n5_15 ),
	.Q(\cntInst2/cnt_r [14])
);
defparam \cntInst2/cnt_r_14_s0 .INIT=1'b0;
DFFR \cntInst2/cnt_r_13_s0  (
	.D(\cntInst2/n26_1 ),
	.CLK(clk_d),
	.RESET(\cntInst2/n5_15 ),
	.Q(\cntInst2/cnt_r [13])
);
defparam \cntInst2/cnt_r_13_s0 .INIT=1'b0;
DFFR \cntInst2/cnt_r_12_s0  (
	.D(\cntInst2/n27_1 ),
	.CLK(clk_d),
	.RESET(\cntInst2/n5_15 ),
	.Q(\cntInst2/cnt_r [12])
);
defparam \cntInst2/cnt_r_12_s0 .INIT=1'b0;
DFFR \cntInst2/cnt_r_11_s0  (
	.D(\cntInst2/n28_1 ),
	.CLK(clk_d),
	.RESET(\cntInst2/n5_15 ),
	.Q(\cntInst2/cnt_r [11])
);
defparam \cntInst2/cnt_r_11_s0 .INIT=1'b0;
DFFR \cntInst2/cnt_r_10_s0  (
	.D(\cntInst2/n29_1 ),
	.CLK(clk_d),
	.RESET(\cntInst2/n5_15 ),
	.Q(\cntInst2/cnt_r [10])
);
defparam \cntInst2/cnt_r_10_s0 .INIT=1'b0;
DFFR \cntInst2/cnt_r_9_s0  (
	.D(\cntInst2/n30_1 ),
	.CLK(clk_d),
	.RESET(\cntInst2/n5_15 ),
	.Q(\cntInst2/cnt_r [9])
);
defparam \cntInst2/cnt_r_9_s0 .INIT=1'b0;
DFFR \cntInst2/cnt_r_8_s0  (
	.D(\cntInst2/n31_1 ),
	.CLK(clk_d),
	.RESET(\cntInst2/n5_15 ),
	.Q(\cntInst2/cnt_r [8])
);
defparam \cntInst2/cnt_r_8_s0 .INIT=1'b0;
DFFR \cntInst2/cnt_r_7_s0  (
	.D(\cntInst2/n32_1 ),
	.CLK(clk_d),
	.RESET(\cntInst2/n5_15 ),
	.Q(\cntInst2/cnt_r [7])
);
defparam \cntInst2/cnt_r_7_s0 .INIT=1'b0;
DFFR \cntInst2/cnt_r_6_s0  (
	.D(\cntInst2/n33_1 ),
	.CLK(clk_d),
	.RESET(\cntInst2/n5_15 ),
	.Q(\cntInst2/cnt_r [6])
);
defparam \cntInst2/cnt_r_6_s0 .INIT=1'b0;
DFFR \cntInst2/cnt_r_5_s0  (
	.D(\cntInst2/n34_1 ),
	.CLK(clk_d),
	.RESET(\cntInst2/n5_15 ),
	.Q(\cntInst2/cnt_r [5])
);
defparam \cntInst2/cnt_r_5_s0 .INIT=1'b0;
DFFR \cntInst2/cnt_r_4_s0  (
	.D(\cntInst2/n35_1 ),
	.CLK(clk_d),
	.RESET(\cntInst2/n5_15 ),
	.Q(\cntInst2/cnt_r [4])
);
defparam \cntInst2/cnt_r_4_s0 .INIT=1'b0;
DFFR \cntInst2/cnt_r_3_s0  (
	.D(\cntInst2/n36_1 ),
	.CLK(clk_d),
	.RESET(\cntInst2/n5_15 ),
	.Q(\cntInst2/cnt_r [3])
);
defparam \cntInst2/cnt_r_3_s0 .INIT=1'b0;
DFFR \cntInst2/cnt_r_2_s0  (
	.D(\cntInst2/n37_1 ),
	.CLK(clk_d),
	.RESET(\cntInst2/n5_15 ),
	.Q(\cntInst2/cnt_r [2])
);
defparam \cntInst2/cnt_r_2_s0 .INIT=1'b0;
DFFR \cntInst2/cnt_r_1_s0  (
	.D(\cntInst2/n38_1 ),
	.CLK(clk_d),
	.RESET(\cntInst2/n5_15 ),
	.Q(\cntInst2/cnt_r [1])
);
defparam \cntInst2/cnt_r_1_s0 .INIT=1'b0;
DFFR \cntInst2/cnt_r_0_s0  (
	.D(\cntInst2/n39_6 ),
	.CLK(clk_d),
	.RESET(\cntInst2/n5_15 ),
	.Q(\cntInst2/cnt_r [0])
);
defparam \cntInst2/cnt_r_0_s0 .INIT=1'b0;
DFFR \cntInst2/cnt_r_31_s0  (
	.D(\cntInst2/n8_1 ),
	.CLK(clk_d),
	.RESET(\cntInst2/n5_15 ),
	.Q(\cntInst2/cnt_r [31])
);
defparam \cntInst2/cnt_r_31_s0 .INIT=1'b0;
DFF \cntInst2/clko_s2  (
	.D(\cntInst2/n72_7 ),
	.CLK(clk_d),
	.Q(sysClk)
);
defparam \cntInst2/clko_s2 .INIT=1'b0;
ALU \cntInst2/n38_s  (
	.I0(\cntInst2/cnt_r [1]),
	.I1(\cntInst2/cnt_r [0]),
	.I3(GND),
	.CIN(GND),
	.COUT(\cntInst2/n38_2 ),
	.SUM(\cntInst2/n38_1 )
);
defparam \cntInst2/n38_s .ALU_MODE=0;
ALU \cntInst2/n37_s  (
	.I0(GND),
	.I1(\cntInst2/cnt_r [2]),
	.I3(GND),
	.CIN(\cntInst2/n38_2 ),
	.COUT(\cntInst2/n37_2 ),
	.SUM(\cntInst2/n37_1 )
);
defparam \cntInst2/n37_s .ALU_MODE=0;
ALU \cntInst2/n36_s  (
	.I0(GND),
	.I1(\cntInst2/cnt_r [3]),
	.I3(GND),
	.CIN(\cntInst2/n37_2 ),
	.COUT(\cntInst2/n36_2 ),
	.SUM(\cntInst2/n36_1 )
);
defparam \cntInst2/n36_s .ALU_MODE=0;
ALU \cntInst2/n35_s  (
	.I0(GND),
	.I1(\cntInst2/cnt_r [4]),
	.I3(GND),
	.CIN(\cntInst2/n36_2 ),
	.COUT(\cntInst2/n35_2 ),
	.SUM(\cntInst2/n35_1 )
);
defparam \cntInst2/n35_s .ALU_MODE=0;
ALU \cntInst2/n34_s  (
	.I0(GND),
	.I1(\cntInst2/cnt_r [5]),
	.I3(GND),
	.CIN(\cntInst2/n35_2 ),
	.COUT(\cntInst2/n34_2 ),
	.SUM(\cntInst2/n34_1 )
);
defparam \cntInst2/n34_s .ALU_MODE=0;
ALU \cntInst2/n33_s  (
	.I0(GND),
	.I1(\cntInst2/cnt_r [6]),
	.I3(GND),
	.CIN(\cntInst2/n34_2 ),
	.COUT(\cntInst2/n33_2 ),
	.SUM(\cntInst2/n33_1 )
);
defparam \cntInst2/n33_s .ALU_MODE=0;
ALU \cntInst2/n32_s  (
	.I0(GND),
	.I1(\cntInst2/cnt_r [7]),
	.I3(GND),
	.CIN(\cntInst2/n33_2 ),
	.COUT(\cntInst2/n32_2 ),
	.SUM(\cntInst2/n32_1 )
);
defparam \cntInst2/n32_s .ALU_MODE=0;
ALU \cntInst2/n31_s  (
	.I0(GND),
	.I1(\cntInst2/cnt_r [8]),
	.I3(GND),
	.CIN(\cntInst2/n32_2 ),
	.COUT(\cntInst2/n31_2 ),
	.SUM(\cntInst2/n31_1 )
);
defparam \cntInst2/n31_s .ALU_MODE=0;
ALU \cntInst2/n30_s  (
	.I0(GND),
	.I1(\cntInst2/cnt_r [9]),
	.I3(GND),
	.CIN(\cntInst2/n31_2 ),
	.COUT(\cntInst2/n30_2 ),
	.SUM(\cntInst2/n30_1 )
);
defparam \cntInst2/n30_s .ALU_MODE=0;
ALU \cntInst2/n29_s  (
	.I0(GND),
	.I1(\cntInst2/cnt_r [10]),
	.I3(GND),
	.CIN(\cntInst2/n30_2 ),
	.COUT(\cntInst2/n29_2 ),
	.SUM(\cntInst2/n29_1 )
);
defparam \cntInst2/n29_s .ALU_MODE=0;
ALU \cntInst2/n28_s  (
	.I0(GND),
	.I1(\cntInst2/cnt_r [11]),
	.I3(GND),
	.CIN(\cntInst2/n29_2 ),
	.COUT(\cntInst2/n28_2 ),
	.SUM(\cntInst2/n28_1 )
);
defparam \cntInst2/n28_s .ALU_MODE=0;
ALU \cntInst2/n27_s  (
	.I0(GND),
	.I1(\cntInst2/cnt_r [12]),
	.I3(GND),
	.CIN(\cntInst2/n28_2 ),
	.COUT(\cntInst2/n27_2 ),
	.SUM(\cntInst2/n27_1 )
);
defparam \cntInst2/n27_s .ALU_MODE=0;
ALU \cntInst2/n26_s  (
	.I0(GND),
	.I1(\cntInst2/cnt_r [13]),
	.I3(GND),
	.CIN(\cntInst2/n27_2 ),
	.COUT(\cntInst2/n26_2 ),
	.SUM(\cntInst2/n26_1 )
);
defparam \cntInst2/n26_s .ALU_MODE=0;
ALU \cntInst2/n25_s  (
	.I0(GND),
	.I1(\cntInst2/cnt_r [14]),
	.I3(GND),
	.CIN(\cntInst2/n26_2 ),
	.COUT(\cntInst2/n25_2 ),
	.SUM(\cntInst2/n25_1 )
);
defparam \cntInst2/n25_s .ALU_MODE=0;
ALU \cntInst2/n24_s  (
	.I0(GND),
	.I1(\cntInst2/cnt_r [15]),
	.I3(GND),
	.CIN(\cntInst2/n25_2 ),
	.COUT(\cntInst2/n24_2 ),
	.SUM(\cntInst2/n24_1 )
);
defparam \cntInst2/n24_s .ALU_MODE=0;
ALU \cntInst2/n23_s  (
	.I0(GND),
	.I1(\cntInst2/cnt_r [16]),
	.I3(GND),
	.CIN(\cntInst2/n24_2 ),
	.COUT(\cntInst2/n23_2 ),
	.SUM(\cntInst2/n23_1 )
);
defparam \cntInst2/n23_s .ALU_MODE=0;
ALU \cntInst2/n22_s  (
	.I0(GND),
	.I1(\cntInst2/cnt_r [17]),
	.I3(GND),
	.CIN(\cntInst2/n23_2 ),
	.COUT(\cntInst2/n22_2 ),
	.SUM(\cntInst2/n22_1 )
);
defparam \cntInst2/n22_s .ALU_MODE=0;
ALU \cntInst2/n21_s  (
	.I0(GND),
	.I1(\cntInst2/cnt_r [18]),
	.I3(GND),
	.CIN(\cntInst2/n22_2 ),
	.COUT(\cntInst2/n21_2 ),
	.SUM(\cntInst2/n21_1 )
);
defparam \cntInst2/n21_s .ALU_MODE=0;
ALU \cntInst2/n20_s  (
	.I0(GND),
	.I1(\cntInst2/cnt_r [19]),
	.I3(GND),
	.CIN(\cntInst2/n21_2 ),
	.COUT(\cntInst2/n20_2 ),
	.SUM(\cntInst2/n20_1 )
);
defparam \cntInst2/n20_s .ALU_MODE=0;
ALU \cntInst2/n19_s  (
	.I0(GND),
	.I1(\cntInst2/cnt_r [20]),
	.I3(GND),
	.CIN(\cntInst2/n20_2 ),
	.COUT(\cntInst2/n19_2 ),
	.SUM(\cntInst2/n19_1 )
);
defparam \cntInst2/n19_s .ALU_MODE=0;
ALU \cntInst2/n18_s  (
	.I0(GND),
	.I1(\cntInst2/cnt_r [21]),
	.I3(GND),
	.CIN(\cntInst2/n19_2 ),
	.COUT(\cntInst2/n18_2 ),
	.SUM(\cntInst2/n18_1 )
);
defparam \cntInst2/n18_s .ALU_MODE=0;
ALU \cntInst2/n17_s  (
	.I0(GND),
	.I1(\cntInst2/cnt_r [22]),
	.I3(GND),
	.CIN(\cntInst2/n18_2 ),
	.COUT(\cntInst2/n17_2 ),
	.SUM(\cntInst2/n17_1 )
);
defparam \cntInst2/n17_s .ALU_MODE=0;
ALU \cntInst2/n16_s  (
	.I0(GND),
	.I1(\cntInst2/cnt_r [23]),
	.I3(GND),
	.CIN(\cntInst2/n17_2 ),
	.COUT(\cntInst2/n16_2 ),
	.SUM(\cntInst2/n16_1 )
);
defparam \cntInst2/n16_s .ALU_MODE=0;
ALU \cntInst2/n15_s  (
	.I0(GND),
	.I1(\cntInst2/cnt_r [24]),
	.I3(GND),
	.CIN(\cntInst2/n16_2 ),
	.COUT(\cntInst2/n15_2 ),
	.SUM(\cntInst2/n15_1 )
);
defparam \cntInst2/n15_s .ALU_MODE=0;
ALU \cntInst2/n14_s  (
	.I0(GND),
	.I1(\cntInst2/cnt_r [25]),
	.I3(GND),
	.CIN(\cntInst2/n15_2 ),
	.COUT(\cntInst2/n14_2 ),
	.SUM(\cntInst2/n14_1 )
);
defparam \cntInst2/n14_s .ALU_MODE=0;
ALU \cntInst2/n13_s  (
	.I0(GND),
	.I1(\cntInst2/cnt_r [26]),
	.I3(GND),
	.CIN(\cntInst2/n14_2 ),
	.COUT(\cntInst2/n13_2 ),
	.SUM(\cntInst2/n13_1 )
);
defparam \cntInst2/n13_s .ALU_MODE=0;
ALU \cntInst2/n12_s  (
	.I0(GND),
	.I1(\cntInst2/cnt_r [27]),
	.I3(GND),
	.CIN(\cntInst2/n13_2 ),
	.COUT(\cntInst2/n12_2 ),
	.SUM(\cntInst2/n12_1 )
);
defparam \cntInst2/n12_s .ALU_MODE=0;
ALU \cntInst2/n11_s  (
	.I0(GND),
	.I1(\cntInst2/cnt_r [28]),
	.I3(GND),
	.CIN(\cntInst2/n12_2 ),
	.COUT(\cntInst2/n11_2 ),
	.SUM(\cntInst2/n11_1 )
);
defparam \cntInst2/n11_s .ALU_MODE=0;
ALU \cntInst2/n10_s  (
	.I0(GND),
	.I1(\cntInst2/cnt_r [29]),
	.I3(GND),
	.CIN(\cntInst2/n11_2 ),
	.COUT(\cntInst2/n10_2 ),
	.SUM(\cntInst2/n10_1 )
);
defparam \cntInst2/n10_s .ALU_MODE=0;
ALU \cntInst2/n9_s  (
	.I0(GND),
	.I1(\cntInst2/cnt_r [30]),
	.I3(GND),
	.CIN(\cntInst2/n10_2 ),
	.COUT(\cntInst2/n9_2 ),
	.SUM(\cntInst2/n9_1 )
);
defparam \cntInst2/n9_s .ALU_MODE=0;
ALU \cntInst2/n8_s  (
	.I0(GND),
	.I1(\cntInst2/cnt_r [31]),
	.I3(GND),
	.CIN(\cntInst2/n9_2 ),
	.COUT(\cntInst2/n8_0_COUT ),
	.SUM(\cntInst2/n8_1 )
);
defparam \cntInst2/n8_s .ALU_MODE=0;
LUT1 \cntInst2/n39_s2  (
	.I0(\cntInst2/cnt_r [0]),
	.F(\cntInst2/n39_6 )
);
defparam \cntInst2/n39_s2 .INIT=2'h1;
LUT4 \deUstb_inst/isOk_s0  (
	.I0(\deUstb_inst/cnt_r [0]),
	.I1(\deUstb_inst/cnt_r [1]),
	.I2(\deUstb_inst/cnt_r [2]),
	.I3(\deUstb_inst/cnt_r [3]),
	.F(\deUstb_inst/isOk )
);
defparam \deUstb_inst/isOk_s0 .INIT=16'h8000;
LUT2 \deUstb_inst/fftStart_s  (
	.I0(\deUstb_inst/isOk_r [1]),
	.I1(\deUstb_inst/isOk ),
	.F(fftStart)
);
defparam \deUstb_inst/fftStart_s .INIT=4'h4;
LUT4 \deUstb_inst/n8_s2  (
	.I0(\deUstb_inst/cnt_r [3]),
	.I1(\deUstb_inst/cnt_r [0]),
	.I2(\deUstb_inst/cnt_r [1]),
	.I3(\deUstb_inst/cnt_r [2]),
	.F(\deUstb_inst/n8_10 )
);
defparam \deUstb_inst/n8_s2 .INIT=16'hBFC0;
LUT4 \deUstb_inst/n9_s2  (
	.I0(\deUstb_inst/cnt_r [2]),
	.I1(\deUstb_inst/cnt_r [3]),
	.I2(\deUstb_inst/cnt_r [0]),
	.I3(\deUstb_inst/cnt_r [1]),
	.F(\deUstb_inst/n9_7 )
);
defparam \deUstb_inst/n9_s2 .INIT=16'h8FF0;
LUT4 \deUstb_inst/n10_s3  (
	.I0(\deUstb_inst/cnt_r [2]),
	.I1(\deUstb_inst/cnt_r [1]),
	.I2(\deUstb_inst/cnt_r [3]),
	.I3(\deUstb_inst/cnt_r [0]),
	.F(\deUstb_inst/n10_8 )
);
defparam \deUstb_inst/n10_s3 .INIT=16'h80FF;
LUT4 \deUstb_inst/n7_s2  (
	.I0(\deUstb_inst/cnt_r [0]),
	.I1(\deUstb_inst/cnt_r [1]),
	.I2(\deUstb_inst/cnt_r [2]),
	.I3(\deUstb_inst/cnt_r [3]),
	.F(\deUstb_inst/n7_7 )
);
defparam \deUstb_inst/n7_s2 .INIT=16'hFF80;
DFF \deUstb_inst/isOk_r_1_s0  (
	.D(\deUstb_inst/isOk_r [0]),
	.CLK(out_d_11[35]),
	.Q(\deUstb_inst/isOk_r [1])
);
defparam \deUstb_inst/isOk_r_1_s0 .INIT=1'b0;
DFF \deUstb_inst/isOk_r_0_s0  (
	.D(\deUstb_inst/isOk ),
	.CLK(out_d_11[35]),
	.Q(\deUstb_inst/isOk_r [0])
);
defparam \deUstb_inst/isOk_r_0_s0 .INIT=1'b0;
DFFR \deUstb_inst/cnt_r_2_s4  (
	.D(\deUstb_inst/n8_10 ),
	.CLK(out_d_11[35]),
	.RESET(key_d[4]),
	.Q(\deUstb_inst/cnt_r [2])
);
defparam \deUstb_inst/cnt_r_2_s4 .INIT=1'b0;
DFFR \deUstb_inst/cnt_r_1_s2  (
	.D(\deUstb_inst/n9_7 ),
	.CLK(out_d_11[35]),
	.RESET(key_d[4]),
	.Q(\deUstb_inst/cnt_r [1])
);
defparam \deUstb_inst/cnt_r_1_s2 .INIT=1'b0;
DFFR \deUstb_inst/cnt_r_0_s2  (
	.D(\deUstb_inst/n10_8 ),
	.CLK(out_d_11[35]),
	.RESET(key_d[4]),
	.Q(\deUstb_inst/cnt_r [0])
);
defparam \deUstb_inst/cnt_r_0_s2 .INIT=1'b0;
DFFR \deUstb_inst/cnt_r_3_s2  (
	.D(\deUstb_inst/n7_7 ),
	.CLK(out_d_11[35]),
	.RESET(key_d[4]),
	.Q(\deUstb_inst/cnt_r [3])
);
defparam \deUstb_inst/cnt_r_3_s2 .INIT=1'b0;
LUT4 \deUstb_rst/isOk_s0  (
	.I0(\deUstb_rst/cnt_r [0]),
	.I1(\deUstb_rst/cnt_r [1]),
	.I2(\deUstb_rst/cnt_r [2]),
	.I3(\deUstb_rst/cnt_r [3]),
	.F(\deUstb_rst/isOk )
);
defparam \deUstb_rst/isOk_s0 .INIT=16'h8000;
LUT2 \deUstb_rst/fftRst_s  (
	.I0(\deUstb_rst/isOk_r [1]),
	.I1(\deUstb_rst/isOk ),
	.F(fftRst)
);
defparam \deUstb_rst/fftRst_s .INIT=4'h4;
LUT4 \deUstb_rst/n8_s2  (
	.I0(\deUstb_rst/cnt_r [3]),
	.I1(\deUstb_rst/cnt_r [0]),
	.I2(\deUstb_rst/cnt_r [1]),
	.I3(\deUstb_rst/cnt_r [2]),
	.F(\deUstb_rst/n8_7 )
);
defparam \deUstb_rst/n8_s2 .INIT=16'hBFC0;
LUT4 \deUstb_rst/n9_s2  (
	.I0(\deUstb_rst/cnt_r [2]),
	.I1(\deUstb_rst/cnt_r [3]),
	.I2(\deUstb_rst/cnt_r [0]),
	.I3(\deUstb_rst/cnt_r [1]),
	.F(\deUstb_rst/n9_7 )
);
defparam \deUstb_rst/n9_s2 .INIT=16'h8FF0;
LUT4 \deUstb_rst/n10_s3  (
	.I0(\deUstb_rst/cnt_r [2]),
	.I1(\deUstb_rst/cnt_r [1]),
	.I2(\deUstb_rst/cnt_r [3]),
	.I3(\deUstb_rst/cnt_r [0]),
	.F(\deUstb_rst/n10_8 )
);
defparam \deUstb_rst/n10_s3 .INIT=16'h80FF;
LUT4 \deUstb_rst/n7_s2  (
	.I0(\deUstb_rst/cnt_r [0]),
	.I1(\deUstb_rst/cnt_r [1]),
	.I2(\deUstb_rst/cnt_r [2]),
	.I3(\deUstb_rst/cnt_r [3]),
	.F(\deUstb_rst/n7_7 )
);
defparam \deUstb_rst/n7_s2 .INIT=16'hFF80;
DFF \deUstb_rst/isOk_r_1_s0  (
	.D(\deUstb_rst/isOk_r [0]),
	.CLK(out_d_11[35]),
	.Q(\deUstb_rst/isOk_r [1])
);
defparam \deUstb_rst/isOk_r_1_s0 .INIT=1'b0;
DFF \deUstb_rst/isOk_r_0_s0  (
	.D(\deUstb_rst/isOk ),
	.CLK(out_d_11[35]),
	.Q(\deUstb_rst/isOk_r [0])
);
defparam \deUstb_rst/isOk_r_0_s0 .INIT=1'b0;
DFFR \deUstb_rst/cnt_r_2_s4  (
	.D(\deUstb_rst/n8_7 ),
	.CLK(out_d_11[35]),
	.RESET(key_d[3]),
	.Q(\deUstb_rst/cnt_r [2])
);
defparam \deUstb_rst/cnt_r_2_s4 .INIT=1'b0;
DFFR \deUstb_rst/cnt_r_1_s2  (
	.D(\deUstb_rst/n9_7 ),
	.CLK(out_d_11[35]),
	.RESET(key_d[3]),
	.Q(\deUstb_rst/cnt_r [1])
);
defparam \deUstb_rst/cnt_r_1_s2 .INIT=1'b0;
DFFR \deUstb_rst/cnt_r_0_s2  (
	.D(\deUstb_rst/n10_8 ),
	.CLK(out_d_11[35]),
	.RESET(key_d[3]),
	.Q(\deUstb_rst/cnt_r [0])
);
defparam \deUstb_rst/cnt_r_0_s2 .INIT=1'b0;
DFFR \deUstb_rst/cnt_r_3_s2  (
	.D(\deUstb_rst/n7_7 ),
	.CLK(out_d_11[35]),
	.RESET(key_d[3]),
	.Q(\deUstb_rst/cnt_r [3])
);
defparam \deUstb_rst/cnt_r_3_s2 .INIT=1'b0;
DFFCE \u_testfft/addr_8_s0  (
	.D(\u_testfft/n7_1 ),
	.CLK(out_d_11[35]),
	.CE(out_d_13[28]),
	.CLEAR(fftRst),
	.Q(\u_testfft/addr [8])
);
defparam \u_testfft/addr_8_s0 .INIT=1'b0;
DFFCE \u_testfft/addr_7_s0  (
	.D(\u_testfft/n8_1 ),
	.CLK(out_d_11[35]),
	.CE(out_d_13[28]),
	.CLEAR(fftRst),
	.Q(\u_testfft/addr [7])
);
defparam \u_testfft/addr_7_s0 .INIT=1'b0;
DFFCE \u_testfft/addr_6_s0  (
	.D(\u_testfft/n9_1 ),
	.CLK(out_d_11[35]),
	.CE(out_d_13[28]),
	.CLEAR(fftRst),
	.Q(\u_testfft/addr [6])
);
defparam \u_testfft/addr_6_s0 .INIT=1'b0;
DFFCE \u_testfft/addr_5_s0  (
	.D(\u_testfft/n10_1 ),
	.CLK(out_d_11[35]),
	.CE(out_d_13[28]),
	.CLEAR(fftRst),
	.Q(\u_testfft/addr [5])
);
defparam \u_testfft/addr_5_s0 .INIT=1'b0;
DFFCE \u_testfft/addr_4_s0  (
	.D(\u_testfft/n11_1 ),
	.CLK(out_d_11[35]),
	.CE(out_d_13[28]),
	.CLEAR(fftRst),
	.Q(\u_testfft/addr [4])
);
defparam \u_testfft/addr_4_s0 .INIT=1'b0;
DFFCE \u_testfft/addr_3_s0  (
	.D(\u_testfft/n12_1 ),
	.CLK(out_d_11[35]),
	.CE(out_d_13[28]),
	.CLEAR(fftRst),
	.Q(\u_testfft/addr [3])
);
defparam \u_testfft/addr_3_s0 .INIT=1'b0;
DFFCE \u_testfft/addr_2_s0  (
	.D(\u_testfft/n13_1 ),
	.CLK(out_d_11[35]),
	.CE(out_d_13[28]),
	.CLEAR(fftRst),
	.Q(\u_testfft/addr [2])
);
defparam \u_testfft/addr_2_s0 .INIT=1'b0;
DFFCE \u_testfft/addr_1_s0  (
	.D(\u_testfft/n14_1 ),
	.CLK(out_d_11[35]),
	.CE(out_d_13[28]),
	.CLEAR(fftRst),
	.Q(\u_testfft/addr [1])
);
defparam \u_testfft/addr_1_s0 .INIT=1'b0;
DFFCE \u_testfft/addr_0_s0  (
	.D(\u_testfft/n15_6 ),
	.CLK(out_d_11[35]),
	.CE(out_d_13[28]),
	.CLEAR(fftRst),
	.Q(\u_testfft/addr [0])
);
defparam \u_testfft/addr_0_s0 .INIT=1'b0;
DFFCE \u_testfft/addr_9_s0  (
	.D(\u_testfft/n6_1 ),
	.CLK(out_d_11[35]),
	.CE(out_d_13[28]),
	.CLEAR(fftRst),
	.Q(\u_testfft/addr [9])
);
defparam \u_testfft/addr_9_s0 .INIT=1'b0;
ALU \u_testfft/n14_s  (
	.I0(\u_testfft/addr [1]),
	.I1(\u_testfft/addr [0]),
	.I3(GND),
	.CIN(GND),
	.COUT(\u_testfft/n14_2 ),
	.SUM(\u_testfft/n14_1 )
);
defparam \u_testfft/n14_s .ALU_MODE=0;
ALU \u_testfft/n13_s  (
	.I0(GND),
	.I1(\u_testfft/addr [2]),
	.I3(GND),
	.CIN(\u_testfft/n14_2 ),
	.COUT(\u_testfft/n13_2 ),
	.SUM(\u_testfft/n13_1 )
);
defparam \u_testfft/n13_s .ALU_MODE=0;
ALU \u_testfft/n12_s  (
	.I0(GND),
	.I1(\u_testfft/addr [3]),
	.I3(GND),
	.CIN(\u_testfft/n13_2 ),
	.COUT(\u_testfft/n12_2 ),
	.SUM(\u_testfft/n12_1 )
);
defparam \u_testfft/n12_s .ALU_MODE=0;
ALU \u_testfft/n11_s  (
	.I0(GND),
	.I1(\u_testfft/addr [4]),
	.I3(GND),
	.CIN(\u_testfft/n12_2 ),
	.COUT(\u_testfft/n11_2 ),
	.SUM(\u_testfft/n11_1 )
);
defparam \u_testfft/n11_s .ALU_MODE=0;
ALU \u_testfft/n10_s  (
	.I0(GND),
	.I1(\u_testfft/addr [5]),
	.I3(GND),
	.CIN(\u_testfft/n11_2 ),
	.COUT(\u_testfft/n10_2 ),
	.SUM(\u_testfft/n10_1 )
);
defparam \u_testfft/n10_s .ALU_MODE=0;
ALU \u_testfft/n9_s  (
	.I0(GND),
	.I1(\u_testfft/addr [6]),
	.I3(GND),
	.CIN(\u_testfft/n10_2 ),
	.COUT(\u_testfft/n9_2 ),
	.SUM(\u_testfft/n9_1 )
);
defparam \u_testfft/n9_s .ALU_MODE=0;
ALU \u_testfft/n8_s  (
	.I0(GND),
	.I1(\u_testfft/addr [7]),
	.I3(GND),
	.CIN(\u_testfft/n9_2 ),
	.COUT(\u_testfft/n8_2 ),
	.SUM(\u_testfft/n8_1 )
);
defparam \u_testfft/n8_s .ALU_MODE=0;
ALU \u_testfft/n7_s  (
	.I0(GND),
	.I1(\u_testfft/addr [8]),
	.I3(GND),
	.CIN(\u_testfft/n8_2 ),
	.COUT(\u_testfft/n7_2 ),
	.SUM(\u_testfft/n7_1 )
);
defparam \u_testfft/n7_s .ALU_MODE=0;
ALU \u_testfft/n6_s  (
	.I0(GND),
	.I1(\u_testfft/addr [9]),
	.I3(GND),
	.CIN(\u_testfft/n7_2 ),
	.COUT(\u_testfft/n6_0_COUT ),
	.SUM(\u_testfft/n6_1 )
);
defparam \u_testfft/n6_s .ALU_MODE=0;
LUT1 \u_testfft/n15_s2  (
	.I0(\u_testfft/addr [0]),
	.F(\u_testfft/n15_6 )
);
defparam \u_testfft/n15_s2 .INIT=2'h1;
ROM \u_testfft/U_GW_ROM/bram_rom_0  (
	.CLK(out_d_11[35]),
	.OCE(VCC),
	.CE(out_d_13[28]),
	.RESET(fftRst),
	.WRE(GND),
	.BLKSEL({GND, GND, GND}),
	.AD({\u_testfft/addr [9:0], GND, GND, GND, GND}),
	.DO({sXnRe[15:0], \u_testfft/U_GW_ROM/DO [15:0]})
);
defparam \u_testfft/U_GW_ROM/bram_rom_0 .INIT_RAM_00=256'hF780E580F68013001380F780E580F68013001380F780E580F68013001380F780;
defparam \u_testfft/U_GW_ROM/bram_rom_0 .INIT_RAM_01=256'h1380F780E580F68013001380F780E580F68013001380F780E580F68013001380;
defparam \u_testfft/U_GW_ROM/bram_rom_0 .INIT_RAM_02=256'h13001380F780E580F68013001380F780E580F68013001380F780E580F6801300;
defparam \u_testfft/U_GW_ROM/bram_rom_0 .INIT_RAM_03=256'hF68013001380F780E580F68013001380F780E580F68013001380F780E580F680;
defparam \u_testfft/U_GW_ROM/bram_rom_0 .INIT_RAM_04=256'hE580F68013001380F780E580F68013001380F780E580F68013001380F780E580;
defparam \u_testfft/U_GW_ROM/bram_rom_0 .INIT_RAM_05=256'hF780E580F68013001380F780E580F68013001380F780E580F68013001380F780;
defparam \u_testfft/U_GW_ROM/bram_rom_0 .INIT_RAM_06=256'h1380F780E580F68013001380F780E580F68013001380F780E580F68013001380;
defparam \u_testfft/U_GW_ROM/bram_rom_0 .INIT_RAM_07=256'h13001380F780E580F68013001380F780E580F68013001380F780E580F6801300;
defparam \u_testfft/U_GW_ROM/bram_rom_0 .INIT_RAM_08=256'hF68013001380F780E580F68012801380F780E580F68013001380F780E580F680;
defparam \u_testfft/U_GW_ROM/bram_rom_0 .INIT_RAM_09=256'hE580F68013001380F780E580F68013001380F780E580F68013001380F780E580;
defparam \u_testfft/U_GW_ROM/bram_rom_0 .INIT_RAM_0A=256'hF780E580F68013001380F780E580F68013001380F780E580F68013001380F780;
defparam \u_testfft/U_GW_ROM/bram_rom_0 .INIT_RAM_0B=256'h1380F780E580F68013001380F780E580F68013001380F780E580F68013001380;
defparam \u_testfft/U_GW_ROM/bram_rom_0 .INIT_RAM_0C=256'h13001380F780E600F68013001380F780E580F68013001380F780E580F6801300;
defparam \u_testfft/U_GW_ROM/bram_rom_0 .INIT_RAM_0D=256'hF68013001380F780E580F68013001380F780E580F68013001380F780E580F680;
defparam \u_testfft/U_GW_ROM/bram_rom_0 .INIT_RAM_0E=256'hE600F68013001380F780E580F68013001380F780E580F68013001380F780E580;
defparam \u_testfft/U_GW_ROM/bram_rom_0 .INIT_RAM_0F=256'hF780E580F68012801380F780E580F68013001380F780E580F68013001380F780;
defparam \u_testfft/U_GW_ROM/bram_rom_0 .INIT_RAM_10=256'h1380F780E580F68013001380F780E580F68013001380F780E580F68013001380;
defparam \u_testfft/U_GW_ROM/bram_rom_0 .INIT_RAM_11=256'h13001380F780E580F68013001380F780E580F68013001380F780E580F6801300;
defparam \u_testfft/U_GW_ROM/bram_rom_0 .INIT_RAM_12=256'hF68013001380F780E580F68013001380F780E580F68013001380F780E580F680;
defparam \u_testfft/U_GW_ROM/bram_rom_0 .INIT_RAM_13=256'hE580F68013001380F780E580F68013001380F780E580F68013001380F780E580;
defparam \u_testfft/U_GW_ROM/bram_rom_0 .INIT_RAM_14=256'hF780E580F68013001380F780E580F68013001380F780E580F68013001380F780;
defparam \u_testfft/U_GW_ROM/bram_rom_0 .INIT_RAM_15=256'h1380F780E580F68013001380F780E580F68013001380F780E580F68013001380;
defparam \u_testfft/U_GW_ROM/bram_rom_0 .INIT_RAM_16=256'h13001380F780E580F68013001380F780E580F68013001380F780E580F6801300;
defparam \u_testfft/U_GW_ROM/bram_rom_0 .INIT_RAM_17=256'hF68013001380F780E580F68013001380F780E580F68013001380F780E580F680;
defparam \u_testfft/U_GW_ROM/bram_rom_0 .INIT_RAM_18=256'hE580F68013001380F780E580F68013001380F780E580F68013001380F780E580;
defparam \u_testfft/U_GW_ROM/bram_rom_0 .INIT_RAM_19=256'hF780E580F68013001380F780E580F68013001380F780E580F68013001380F780;
defparam \u_testfft/U_GW_ROM/bram_rom_0 .INIT_RAM_1A=256'h1380F800E580F68013001380F780E580F68013001380F780E600F68013001380;
defparam \u_testfft/U_GW_ROM/bram_rom_0 .INIT_RAM_1B=256'h13001380F780E580F68013001380F780E580F68013001380F780E600F6801300;
defparam \u_testfft/U_GW_ROM/bram_rom_0 .INIT_RAM_1C=256'hF68013001380F780E580F68013001380F780E580F68013001380F780E580F680;
defparam \u_testfft/U_GW_ROM/bram_rom_0 .INIT_RAM_1D=256'hE580F68013001380F780E580F68013001380F780E580F68013001380F780E580;
defparam \u_testfft/U_GW_ROM/bram_rom_0 .INIT_RAM_1E=256'hF780E580F68013001380F780E580F68013001380F780E580F68013001380F780;
defparam \u_testfft/U_GW_ROM/bram_rom_0 .INIT_RAM_1F=256'h1380F780E580F68013001380F780E580F68013001380F780E580F68013001380;
defparam \u_testfft/U_GW_ROM/bram_rom_0 .INIT_RAM_20=256'h13001380F780E580F68013001380F780E580F68013001380F780E580F6801300;
defparam \u_testfft/U_GW_ROM/bram_rom_0 .INIT_RAM_21=256'hF68013001380F780E580F68013001380F780E580F68013001380F780E580F680;
defparam \u_testfft/U_GW_ROM/bram_rom_0 .INIT_RAM_22=256'hE580F68013001380F780E580F68013001380F780E580F68013001380F780E580;
defparam \u_testfft/U_GW_ROM/bram_rom_0 .INIT_RAM_23=256'hF780E580F68013001380F780E580F68013001380F780E580F68013001380F780;
defparam \u_testfft/U_GW_ROM/bram_rom_0 .INIT_RAM_24=256'h1380F780E580F68013001380F780E580F68013001380F780E580F68013001380;
defparam \u_testfft/U_GW_ROM/bram_rom_0 .INIT_RAM_25=256'h13001380F780E580F68013001380F780E580F68013001380F780E580F6801300;
defparam \u_testfft/U_GW_ROM/bram_rom_0 .INIT_RAM_26=256'hF68013001380F780E580F68013001380F780E580F68013001380F780E580F680;
defparam \u_testfft/U_GW_ROM/bram_rom_0 .INIT_RAM_27=256'hE580F68013001380F780E580F68013001380F780E580F68013001380F780E580;
defparam \u_testfft/U_GW_ROM/bram_rom_0 .INIT_RAM_28=256'hF780E580F68013001380F780E580F68013001380F780E580F68013001380F780;
defparam \u_testfft/U_GW_ROM/bram_rom_0 .INIT_RAM_29=256'h1380F780E580F68013001380F780E580F68013001380F780E580F68013001380;
defparam \u_testfft/U_GW_ROM/bram_rom_0 .INIT_RAM_2A=256'h13001380F780E580F68013001380F780E580F68013001380F780E600F6801300;
defparam \u_testfft/U_GW_ROM/bram_rom_0 .INIT_RAM_2B=256'hF68013001380F780E580F68013001380F780E580F68013001380F800E580F680;
defparam \u_testfft/U_GW_ROM/bram_rom_0 .INIT_RAM_2C=256'hE580F68013001380F780E580F68013001380F780E580F68013001380F780E580;
defparam \u_testfft/U_GW_ROM/bram_rom_0 .INIT_RAM_2D=256'hF780E580F68013001380F780E580F68013001380F780E580F68013001400F780;
defparam \u_testfft/U_GW_ROM/bram_rom_0 .INIT_RAM_2E=256'h1380F780E580F68013001380F780E580F68013001380F780E580F68013001380;
defparam \u_testfft/U_GW_ROM/bram_rom_0 .INIT_RAM_2F=256'h12801380F780E580F68013001380F780E580F68013001380F780E580F6801300;
defparam \u_testfft/U_GW_ROM/bram_rom_0 .INIT_RAM_30=256'hF68013001380F780E580F68013001380F780E580F68013001380F780E580F680;
defparam \u_testfft/U_GW_ROM/bram_rom_0 .INIT_RAM_31=256'hE580F68013001380F780E580F68013001380F780E580F68013001380F780E580;
defparam \u_testfft/U_GW_ROM/bram_rom_0 .INIT_RAM_32=256'hF780E580F68013001380F780E580F68013001380F780E580F68013001380F780;
defparam \u_testfft/U_GW_ROM/bram_rom_0 .INIT_RAM_33=256'h1380F780E580F68013001380F780E580F68013001380F780E580F68013001380;
defparam \u_testfft/U_GW_ROM/bram_rom_0 .INIT_RAM_34=256'h13001380F780E580F68013001380F780E580F68013001380F780E580F6801300;
defparam \u_testfft/U_GW_ROM/bram_rom_0 .INIT_RAM_35=256'hF68013001380F780E580F68013001380F780E580F68013001380F800E580F680;
defparam \u_testfft/U_GW_ROM/bram_rom_0 .INIT_RAM_36=256'hE580F68013001380F780E580F68013001380F780E580F68013001380F780E580;
defparam \u_testfft/U_GW_ROM/bram_rom_0 .INIT_RAM_37=256'hF780E580F68013001380F780E580F68013001380F780E580F68012801380F780;
defparam \u_testfft/U_GW_ROM/bram_rom_0 .INIT_RAM_38=256'h1380F800E580F68013001380F780E580F68013001380F780E580F68013001380;
defparam \u_testfft/U_GW_ROM/bram_rom_0 .INIT_RAM_39=256'h13001380F780E580F68013001380F800E580F68013001380F780E580F6801300;
defparam \u_testfft/U_GW_ROM/bram_rom_0 .INIT_RAM_3A=256'hF68013001380F780E580F68013001380F780E580F68013001380F780E580F680;
defparam \u_testfft/U_GW_ROM/bram_rom_0 .INIT_RAM_3B=256'hE580F68013001380F780E580F68013001380F780E580F68013001380F780E580;
defparam \u_testfft/U_GW_ROM/bram_rom_0 .INIT_RAM_3C=256'hF780E580F68013001380F780E600F68013001380F780E580F68012801380F780;
defparam \u_testfft/U_GW_ROM/bram_rom_0 .INIT_RAM_3D=256'h1380F780E580F68013001380F780E580F68013001380F780E580F68013001380;
defparam \u_testfft/U_GW_ROM/bram_rom_0 .INIT_RAM_3E=256'h13001380F780E580F68013001380F780E580F68013001380F780E580F6801300;
defparam \u_testfft/U_GW_ROM/bram_rom_0 .INIT_RAM_3F=256'hF68012801380F780E600F68013001380F780E580F68013001380F780E580F680;
defparam \u_testfft/U_GW_ROM/bram_rom_0 .READ_MODE=1'b0;
defparam \u_testfft/U_GW_ROM/bram_rom_0 .BLK_SEL=3'b000;
defparam \u_testfft/U_GW_ROM/bram_rom_0 .BIT_WIDTH=16;
defparam \u_testfft/U_GW_ROM/bram_rom_0 .RESET_MODE="SYNC";
LUT3 \myfft/fft_top_inst/n944_s1  (
	.I0(fftStart),
	.I1(out_d_13[29]),
	.I2(\myfft/fft_top_inst/stage [0]),
	.F(\myfft/fft_top_inst/n941_2 )
);
defparam \myfft/fft_top_inst/n944_s1 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/n944_s2  (
	.I0(\myfft/fft_top_inst/fft_ok ),
	.I1(\myfft/fft_top_inst/ud_ok ),
	.I2(\myfft/fft_top_inst/stage [0]),
	.F(\myfft/fft_top_inst/n945_2 )
);
defparam \myfft/fft_top_inst/n944_s2 .INIT=8'hCA;
LUT2 \myfft/fft_top_inst/slfRst_s1  (
	.I0(fftRst),
	.I1(\myfft/fft_top_inst/ud_ok ),
	.F(\myfft/fft_top_inst/slfRst )
);
defparam \myfft/fft_top_inst/slfRst_s1 .INIT=4'hE;
LUT2 \myfft/fft_top_inst/eoud_d_s  (
	.I0(\myfft/fft_top_inst/ud_ok ),
	.I1(out_d_14[32]),
	.F(out_d_12[33])
);
defparam \myfft/fft_top_inst/eoud_d_s .INIT=4'h8;
LUT4 \myfft/fft_top_inst/pxna_wre_s0  (
	.I0(out_d_12[30]),
	.I1(\myfft/fft_top_inst/corePa_wre ),
	.I2(out_d_13[28]),
	.I3(\myfft/fft_top_inst/isLdxn ),
	.F(\myfft/fft_top_inst/pxna_wre )
);
defparam \myfft/fft_top_inst/pxna_wre_s0 .INIT=16'hF088;
LUT4 \myfft/fft_top_inst/pxna_di_re_15_s0  (
	.I0(out_d_12[30]),
	.I1(\myfft/fft_top_inst/btfy_xkare [15]),
	.I2(sXnRe[15]),
	.I3(\myfft/fft_top_inst/isLdxn ),
	.F(\myfft/fft_top_inst/pxna_di_re [15])
);
defparam \myfft/fft_top_inst/pxna_di_re_15_s0 .INIT=16'hF088;
LUT4 \myfft/fft_top_inst/pxna_di_re_14_s0  (
	.I0(out_d_12[30]),
	.I1(\myfft/fft_top_inst/btfy_xkare [14]),
	.I2(sXnRe[14]),
	.I3(\myfft/fft_top_inst/isLdxn ),
	.F(\myfft/fft_top_inst/pxna_di_re [14])
);
defparam \myfft/fft_top_inst/pxna_di_re_14_s0 .INIT=16'hF088;
LUT4 \myfft/fft_top_inst/pxna_di_re_13_s0  (
	.I0(out_d_12[30]),
	.I1(\myfft/fft_top_inst/btfy_xkare [13]),
	.I2(sXnRe[13]),
	.I3(\myfft/fft_top_inst/isLdxn ),
	.F(\myfft/fft_top_inst/pxna_di_re [13])
);
defparam \myfft/fft_top_inst/pxna_di_re_13_s0 .INIT=16'hF088;
LUT4 \myfft/fft_top_inst/pxna_di_re_12_s0  (
	.I0(out_d_12[30]),
	.I1(\myfft/fft_top_inst/btfy_xkare [12]),
	.I2(sXnRe[12]),
	.I3(\myfft/fft_top_inst/isLdxn ),
	.F(\myfft/fft_top_inst/pxna_di_re [12])
);
defparam \myfft/fft_top_inst/pxna_di_re_12_s0 .INIT=16'hF088;
LUT4 \myfft/fft_top_inst/pxna_di_re_11_s0  (
	.I0(out_d_12[30]),
	.I1(\myfft/fft_top_inst/btfy_xkare [11]),
	.I2(sXnRe[11]),
	.I3(\myfft/fft_top_inst/isLdxn ),
	.F(\myfft/fft_top_inst/pxna_di_re [11])
);
defparam \myfft/fft_top_inst/pxna_di_re_11_s0 .INIT=16'hF088;
LUT4 \myfft/fft_top_inst/pxna_di_re_10_s0  (
	.I0(out_d_12[30]),
	.I1(\myfft/fft_top_inst/btfy_xkare [10]),
	.I2(sXnRe[10]),
	.I3(\myfft/fft_top_inst/isLdxn ),
	.F(\myfft/fft_top_inst/pxna_di_re [10])
);
defparam \myfft/fft_top_inst/pxna_di_re_10_s0 .INIT=16'hF088;
LUT4 \myfft/fft_top_inst/pxna_di_re_9_s0  (
	.I0(out_d_12[30]),
	.I1(\myfft/fft_top_inst/btfy_xkare [9]),
	.I2(sXnRe[9]),
	.I3(\myfft/fft_top_inst/isLdxn ),
	.F(\myfft/fft_top_inst/pxna_di_re [9])
);
defparam \myfft/fft_top_inst/pxna_di_re_9_s0 .INIT=16'hF088;
LUT4 \myfft/fft_top_inst/pxna_di_re_8_s0  (
	.I0(out_d_12[30]),
	.I1(\myfft/fft_top_inst/btfy_xkare [8]),
	.I2(sXnRe[8]),
	.I3(\myfft/fft_top_inst/isLdxn ),
	.F(\myfft/fft_top_inst/pxna_di_re [8])
);
defparam \myfft/fft_top_inst/pxna_di_re_8_s0 .INIT=16'hF088;
LUT4 \myfft/fft_top_inst/pxna_di_re_7_s0  (
	.I0(out_d_12[30]),
	.I1(\myfft/fft_top_inst/btfy_xkare [7]),
	.I2(sXnRe[7]),
	.I3(\myfft/fft_top_inst/isLdxn ),
	.F(\myfft/fft_top_inst/pxna_di_re [7])
);
defparam \myfft/fft_top_inst/pxna_di_re_7_s0 .INIT=16'hF088;
LUT4 \myfft/fft_top_inst/pxna_di_re_6_s0  (
	.I0(out_d_12[30]),
	.I1(\myfft/fft_top_inst/btfy_xkare [6]),
	.I2(sXnRe[6]),
	.I3(\myfft/fft_top_inst/isLdxn ),
	.F(\myfft/fft_top_inst/pxna_di_re [6])
);
defparam \myfft/fft_top_inst/pxna_di_re_6_s0 .INIT=16'hF088;
LUT4 \myfft/fft_top_inst/pxna_di_re_5_s0  (
	.I0(out_d_12[30]),
	.I1(\myfft/fft_top_inst/btfy_xkare [5]),
	.I2(sXnRe[5]),
	.I3(\myfft/fft_top_inst/isLdxn ),
	.F(\myfft/fft_top_inst/pxna_di_re [5])
);
defparam \myfft/fft_top_inst/pxna_di_re_5_s0 .INIT=16'hF088;
LUT4 \myfft/fft_top_inst/pxna_di_re_4_s0  (
	.I0(out_d_12[30]),
	.I1(\myfft/fft_top_inst/btfy_xkare [4]),
	.I2(sXnRe[4]),
	.I3(\myfft/fft_top_inst/isLdxn ),
	.F(\myfft/fft_top_inst/pxna_di_re [4])
);
defparam \myfft/fft_top_inst/pxna_di_re_4_s0 .INIT=16'hF088;
LUT4 \myfft/fft_top_inst/pxna_di_re_3_s0  (
	.I0(out_d_12[30]),
	.I1(\myfft/fft_top_inst/btfy_xkare [3]),
	.I2(sXnRe[3]),
	.I3(\myfft/fft_top_inst/isLdxn ),
	.F(\myfft/fft_top_inst/pxna_di_re [3])
);
defparam \myfft/fft_top_inst/pxna_di_re_3_s0 .INIT=16'hF088;
LUT4 \myfft/fft_top_inst/pxna_di_re_2_s0  (
	.I0(out_d_12[30]),
	.I1(\myfft/fft_top_inst/btfy_xkare [2]),
	.I2(sXnRe[2]),
	.I3(\myfft/fft_top_inst/isLdxn ),
	.F(\myfft/fft_top_inst/pxna_di_re [2])
);
defparam \myfft/fft_top_inst/pxna_di_re_2_s0 .INIT=16'hF088;
LUT4 \myfft/fft_top_inst/pxna_di_re_1_s0  (
	.I0(out_d_12[30]),
	.I1(\myfft/fft_top_inst/btfy_xkare [1]),
	.I2(sXnRe[1]),
	.I3(\myfft/fft_top_inst/isLdxn ),
	.F(\myfft/fft_top_inst/pxna_di_re [1])
);
defparam \myfft/fft_top_inst/pxna_di_re_1_s0 .INIT=16'hF088;
LUT4 \myfft/fft_top_inst/pxna_di_re_0_s0  (
	.I0(out_d_12[30]),
	.I1(\myfft/fft_top_inst/btfy_xkare [0]),
	.I2(sXnRe[0]),
	.I3(\myfft/fft_top_inst/isLdxn ),
	.F(\myfft/fft_top_inst/pxna_di_re [0])
);
defparam \myfft/fft_top_inst/pxna_di_re_0_s0 .INIT=16'hF088;
LUT4 \myfft/fft_top_inst/pxna_di_im_15_s0  (
	.I0(out_d_12[30]),
	.I1(\myfft/fft_top_inst/btfy_xkaim [15]),
	.I2(sXnRe[15]),
	.I3(\myfft/fft_top_inst/isLdxn ),
	.F(\myfft/fft_top_inst/pxna_di_im [15])
);
defparam \myfft/fft_top_inst/pxna_di_im_15_s0 .INIT=16'hF088;
LUT4 \myfft/fft_top_inst/pxna_di_im_14_s0  (
	.I0(out_d_12[30]),
	.I1(\myfft/fft_top_inst/btfy_xkaim [14]),
	.I2(sXnRe[14]),
	.I3(\myfft/fft_top_inst/isLdxn ),
	.F(\myfft/fft_top_inst/pxna_di_im [14])
);
defparam \myfft/fft_top_inst/pxna_di_im_14_s0 .INIT=16'hF088;
LUT4 \myfft/fft_top_inst/pxna_di_im_13_s0  (
	.I0(out_d_12[30]),
	.I1(\myfft/fft_top_inst/btfy_xkaim [13]),
	.I2(sXnRe[13]),
	.I3(\myfft/fft_top_inst/isLdxn ),
	.F(\myfft/fft_top_inst/pxna_di_im [13])
);
defparam \myfft/fft_top_inst/pxna_di_im_13_s0 .INIT=16'hF088;
LUT4 \myfft/fft_top_inst/pxna_di_im_12_s0  (
	.I0(out_d_12[30]),
	.I1(\myfft/fft_top_inst/btfy_xkaim [12]),
	.I2(sXnRe[12]),
	.I3(\myfft/fft_top_inst/isLdxn ),
	.F(\myfft/fft_top_inst/pxna_di_im [12])
);
defparam \myfft/fft_top_inst/pxna_di_im_12_s0 .INIT=16'hF088;
LUT4 \myfft/fft_top_inst/pxna_di_im_11_s0  (
	.I0(out_d_12[30]),
	.I1(\myfft/fft_top_inst/btfy_xkaim [11]),
	.I2(sXnRe[11]),
	.I3(\myfft/fft_top_inst/isLdxn ),
	.F(\myfft/fft_top_inst/pxna_di_im [11])
);
defparam \myfft/fft_top_inst/pxna_di_im_11_s0 .INIT=16'hF088;
LUT4 \myfft/fft_top_inst/pxna_di_im_10_s0  (
	.I0(out_d_12[30]),
	.I1(\myfft/fft_top_inst/btfy_xkaim [10]),
	.I2(sXnRe[10]),
	.I3(\myfft/fft_top_inst/isLdxn ),
	.F(\myfft/fft_top_inst/pxna_di_im [10])
);
defparam \myfft/fft_top_inst/pxna_di_im_10_s0 .INIT=16'hF088;
LUT4 \myfft/fft_top_inst/pxna_di_im_9_s0  (
	.I0(out_d_12[30]),
	.I1(\myfft/fft_top_inst/btfy_xkaim [9]),
	.I2(sXnRe[9]),
	.I3(\myfft/fft_top_inst/isLdxn ),
	.F(\myfft/fft_top_inst/pxna_di_im [9])
);
defparam \myfft/fft_top_inst/pxna_di_im_9_s0 .INIT=16'hF088;
LUT4 \myfft/fft_top_inst/pxna_di_im_8_s0  (
	.I0(out_d_12[30]),
	.I1(\myfft/fft_top_inst/btfy_xkaim [8]),
	.I2(sXnRe[8]),
	.I3(\myfft/fft_top_inst/isLdxn ),
	.F(\myfft/fft_top_inst/pxna_di_im [8])
);
defparam \myfft/fft_top_inst/pxna_di_im_8_s0 .INIT=16'hF088;
LUT4 \myfft/fft_top_inst/pxna_di_im_7_s0  (
	.I0(out_d_12[30]),
	.I1(\myfft/fft_top_inst/btfy_xkaim [7]),
	.I2(sXnRe[7]),
	.I3(\myfft/fft_top_inst/isLdxn ),
	.F(\myfft/fft_top_inst/pxna_di_im [7])
);
defparam \myfft/fft_top_inst/pxna_di_im_7_s0 .INIT=16'hF088;
LUT4 \myfft/fft_top_inst/pxna_di_im_6_s0  (
	.I0(out_d_12[30]),
	.I1(\myfft/fft_top_inst/btfy_xkaim [6]),
	.I2(sXnRe[6]),
	.I3(\myfft/fft_top_inst/isLdxn ),
	.F(\myfft/fft_top_inst/pxna_di_im [6])
);
defparam \myfft/fft_top_inst/pxna_di_im_6_s0 .INIT=16'hF088;
LUT4 \myfft/fft_top_inst/pxna_di_im_5_s0  (
	.I0(out_d_12[30]),
	.I1(\myfft/fft_top_inst/btfy_xkaim [5]),
	.I2(sXnRe[5]),
	.I3(\myfft/fft_top_inst/isLdxn ),
	.F(\myfft/fft_top_inst/pxna_di_im [5])
);
defparam \myfft/fft_top_inst/pxna_di_im_5_s0 .INIT=16'hF088;
LUT4 \myfft/fft_top_inst/pxna_di_im_4_s0  (
	.I0(out_d_12[30]),
	.I1(\myfft/fft_top_inst/btfy_xkaim [4]),
	.I2(sXnRe[4]),
	.I3(\myfft/fft_top_inst/isLdxn ),
	.F(\myfft/fft_top_inst/pxna_di_im [4])
);
defparam \myfft/fft_top_inst/pxna_di_im_4_s0 .INIT=16'hF088;
LUT4 \myfft/fft_top_inst/pxna_di_im_3_s0  (
	.I0(out_d_12[30]),
	.I1(\myfft/fft_top_inst/btfy_xkaim [3]),
	.I2(sXnRe[3]),
	.I3(\myfft/fft_top_inst/isLdxn ),
	.F(\myfft/fft_top_inst/pxna_di_im [3])
);
defparam \myfft/fft_top_inst/pxna_di_im_3_s0 .INIT=16'hF088;
LUT4 \myfft/fft_top_inst/pxna_di_im_2_s0  (
	.I0(out_d_12[30]),
	.I1(\myfft/fft_top_inst/btfy_xkaim [2]),
	.I2(sXnRe[2]),
	.I3(\myfft/fft_top_inst/isLdxn ),
	.F(\myfft/fft_top_inst/pxna_di_im [2])
);
defparam \myfft/fft_top_inst/pxna_di_im_2_s0 .INIT=16'hF088;
LUT4 \myfft/fft_top_inst/pxna_di_im_1_s0  (
	.I0(out_d_12[30]),
	.I1(\myfft/fft_top_inst/btfy_xkaim [1]),
	.I2(sXnRe[1]),
	.I3(\myfft/fft_top_inst/isLdxn ),
	.F(\myfft/fft_top_inst/pxna_di_im [1])
);
defparam \myfft/fft_top_inst/pxna_di_im_1_s0 .INIT=16'hF088;
LUT4 \myfft/fft_top_inst/pxna_di_im_0_s0  (
	.I0(out_d_12[30]),
	.I1(\myfft/fft_top_inst/btfy_xkaim [0]),
	.I2(sXnRe[0]),
	.I3(\myfft/fft_top_inst/isLdxn ),
	.F(\myfft/fft_top_inst/pxna_di_im [0])
);
defparam \myfft/fft_top_inst/pxna_di_im_0_s0 .INIT=16'hF088;
LUT3 \myfft/fft_top_inst/qxna_ad_9_s0  (
	.I0(\myfft/fft_top_inst/qxna_ad_9_4 ),
	.I1(\myfft/fft_top_inst/ud_xkm_ad [9]),
	.I2(out_d_12[30]),
	.F(\myfft/fft_top_inst/qxna_ad [9])
);
defparam \myfft/fft_top_inst/qxna_ad_9_s0 .INIT=8'hAC;
LUT3 \myfft/fft_top_inst/qxna_ad_8_s0  (
	.I0(\myfft/fft_top_inst/ud_xkm_ad [8]),
	.I1(\myfft/fft_top_inst/qxna_ad_8_4 ),
	.I2(out_d_12[30]),
	.F(\myfft/fft_top_inst/qxna_ad [8])
);
defparam \myfft/fft_top_inst/qxna_ad_8_s0 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/qxna_ad_7_s0  (
	.I0(\myfft/fft_top_inst/ud_xkm_ad [7]),
	.I1(\myfft/fft_top_inst/qxna_ad_7_4 ),
	.I2(out_d_12[30]),
	.F(\myfft/fft_top_inst/qxna_ad [7])
);
defparam \myfft/fft_top_inst/qxna_ad_7_s0 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/qxna_ad_6_s0  (
	.I0(\myfft/fft_top_inst/ud_xkm_ad [6]),
	.I1(\myfft/fft_top_inst/qxna_ad_6_4 ),
	.I2(out_d_12[30]),
	.F(\myfft/fft_top_inst/qxna_ad [6])
);
defparam \myfft/fft_top_inst/qxna_ad_6_s0 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/qxna_ad_5_s0  (
	.I0(\myfft/fft_top_inst/ud_xkm_ad [5]),
	.I1(\myfft/fft_top_inst/qxna_ad_5_4 ),
	.I2(out_d_12[30]),
	.F(\myfft/fft_top_inst/qxna_ad [5])
);
defparam \myfft/fft_top_inst/qxna_ad_5_s0 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/qxna_ad_4_s0  (
	.I0(\myfft/fft_top_inst/ud_xkm_ad [4]),
	.I1(\myfft/fft_top_inst/qxna_ad_4_4 ),
	.I2(out_d_12[30]),
	.F(\myfft/fft_top_inst/qxna_ad [4])
);
defparam \myfft/fft_top_inst/qxna_ad_4_s0 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/qxna_ad_3_s0  (
	.I0(\myfft/fft_top_inst/ud_xkm_ad [3]),
	.I1(\myfft/fft_top_inst/qxna_ad_3_4 ),
	.I2(out_d_12[30]),
	.F(\myfft/fft_top_inst/qxna_ad [3])
);
defparam \myfft/fft_top_inst/qxna_ad_3_s0 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/qxna_ad_2_s0  (
	.I0(\myfft/fft_top_inst/ud_xkm_ad [2]),
	.I1(\myfft/fft_top_inst/qxna_ad_2_4 ),
	.I2(out_d_12[30]),
	.F(\myfft/fft_top_inst/qxna_ad [2])
);
defparam \myfft/fft_top_inst/qxna_ad_2_s0 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/qxna_ad_1_s0  (
	.I0(\myfft/fft_top_inst/ud_xkm_ad [1]),
	.I1(\myfft/fft_top_inst/qxna_ad_1_4 ),
	.I2(out_d_12[30]),
	.F(\myfft/fft_top_inst/qxna_ad [1])
);
defparam \myfft/fft_top_inst/qxna_ad_1_s0 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/qxna_ad_0_s0  (
	.I0(\myfft/fft_top_inst/ud_xkm_ad [0]),
	.I1(\myfft/fft_top_inst/qxna_ad_0_4 ),
	.I2(out_d_12[30]),
	.F(\myfft/fft_top_inst/qxna_ad [0])
);
defparam \myfft/fft_top_inst/qxna_ad_0_s0 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/n939_s0  (
	.I0(\myfft/fft_top_inst/n944_3 ),
	.I1(\myfft/fft_top_inst/stage [0]),
	.I2(\myfft/fft_top_inst/stage [1]),
	.F(\myfft/fft_top_inst/n939_3 )
);
defparam \myfft/fft_top_inst/n939_s0 .INIT=8'h78;
LUT2 \myfft/fft_top_inst/n940_s0  (
	.I0(\myfft/fft_top_inst/n944_3 ),
	.I1(\myfft/fft_top_inst/stage [0]),
	.F(\myfft/fft_top_inst/n940_3 )
);
defparam \myfft/fft_top_inst/n940_s0 .INIT=4'h6;
LUT3 \myfft/fft_top_inst/pxna_ad_9_s7  (
	.I0(\myfft/fft_top_inst/twBtfy_upwad [9]),
	.I1(\myfft/fft_top_inst/cclt_upwad_2 [9]),
	.I2(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/corePa_ad [9])
);
defparam \myfft/fft_top_inst/pxna_ad_9_s7 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/pxna_ad_8_s6  (
	.I0(\myfft/fft_top_inst/twBtfy_upwad [8]),
	.I1(\myfft/fft_top_inst/cclt_upwad_2 [8]),
	.I2(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/corePa_ad [8])
);
defparam \myfft/fft_top_inst/pxna_ad_8_s6 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/pxna_ad_7_s6  (
	.I0(\myfft/fft_top_inst/twBtfy_upwad [7]),
	.I1(\myfft/fft_top_inst/cclt_upwad_2 [7]),
	.I2(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/corePa_ad [7])
);
defparam \myfft/fft_top_inst/pxna_ad_7_s6 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/pxna_ad_6_s6  (
	.I0(\myfft/fft_top_inst/twBtfy_upwad [6]),
	.I1(\myfft/fft_top_inst/cclt_upwad_2 [6]),
	.I2(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/corePa_ad [6])
);
defparam \myfft/fft_top_inst/pxna_ad_6_s6 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/pxna_ad_5_s6  (
	.I0(\myfft/fft_top_inst/twBtfy_upwad [5]),
	.I1(\myfft/fft_top_inst/cclt_upwad_2 [5]),
	.I2(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/corePa_ad [5])
);
defparam \myfft/fft_top_inst/pxna_ad_5_s6 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/pxna_ad_4_s6  (
	.I0(\myfft/fft_top_inst/twBtfy_upwad [4]),
	.I1(\myfft/fft_top_inst/cclt_upwad_2 [4]),
	.I2(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/corePa_ad [4])
);
defparam \myfft/fft_top_inst/pxna_ad_4_s6 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/pxna_ad_3_s6  (
	.I0(\myfft/fft_top_inst/twBtfy_upwad [3]),
	.I1(\myfft/fft_top_inst/cclt_upwad_2 [3]),
	.I2(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/corePa_ad [3])
);
defparam \myfft/fft_top_inst/pxna_ad_3_s6 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/pxna_ad_2_s6  (
	.I0(\myfft/fft_top_inst/twBtfy_upwad [2]),
	.I1(\myfft/fft_top_inst/cclt_upwad_2 [2]),
	.I2(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/corePa_ad [2])
);
defparam \myfft/fft_top_inst/pxna_ad_2_s6 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/pxna_ad_1_s6  (
	.I0(\myfft/fft_top_inst/twBtfy_upwad [1]),
	.I1(\myfft/fft_top_inst/cclt_upwad_2 [1]),
	.I2(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/corePa_ad [1])
);
defparam \myfft/fft_top_inst/pxna_ad_1_s6 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/pxna_ad_0_s6  (
	.I0(\myfft/fft_top_inst/twBtfy_upwad [0]),
	.I1(\myfft/fft_top_inst/cclt_upwad_2 [0]),
	.I2(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/corePa_ad [0])
);
defparam \myfft/fft_top_inst/pxna_ad_0_s6 .INIT=8'hCA;
LUT2 \myfft/fft_top_inst/isLdxn_s3  (
	.I0(\myfft/fft_top_inst/stage [1]),
	.I1(\myfft/fft_top_inst/stage [0]),
	.F(\myfft/fft_top_inst/isLdxn )
);
defparam \myfft/fft_top_inst/isLdxn_s3 .INIT=4'h4;
LUT2 \myfft/fft_top_inst/isFFT_s3  (
	.I0(\myfft/fft_top_inst/stage [0]),
	.I1(\myfft/fft_top_inst/stage [1]),
	.F(out_d_12[30])
);
defparam \myfft/fft_top_inst/isFFT_s3 .INIT=4'h4;
LUT3 \myfft/fft_top_inst/pxna_ad_9_s6  (
	.I0(\myfft/fft_top_inst/ud_xkm_ad [9]),
	.I1(\myfft/fft_top_inst/ld_xnm_ad [9]),
	.I2(\myfft/fft_top_inst/isLdxn ),
	.F(\myfft/fft_top_inst/pxna_ad_9_7 )
);
defparam \myfft/fft_top_inst/pxna_ad_9_s6 .INIT=8'hCA;
LUT2 \myfft/fft_top_inst/pxna_ad_9_s5  (
	.I0(\myfft/fft_top_inst/isLdxn ),
	.I1(out_d_12[30]),
	.F(\myfft/fft_top_inst/pxna_ad_0_8 )
);
defparam \myfft/fft_top_inst/pxna_ad_9_s5 .INIT=4'h4;
LUT3 \myfft/fft_top_inst/pxna_ad_8_s5  (
	.I0(\myfft/fft_top_inst/ud_xkm_ad [8]),
	.I1(\myfft/fft_top_inst/ld_xnm_ad [8]),
	.I2(\myfft/fft_top_inst/isLdxn ),
	.F(\myfft/fft_top_inst/pxna_ad_8_7 )
);
defparam \myfft/fft_top_inst/pxna_ad_8_s5 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/pxna_ad_7_s5  (
	.I0(\myfft/fft_top_inst/ud_xkm_ad [7]),
	.I1(\myfft/fft_top_inst/ld_xnm_ad [7]),
	.I2(\myfft/fft_top_inst/isLdxn ),
	.F(\myfft/fft_top_inst/pxna_ad_7_7 )
);
defparam \myfft/fft_top_inst/pxna_ad_7_s5 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/pxna_ad_6_s5  (
	.I0(\myfft/fft_top_inst/ud_xkm_ad [6]),
	.I1(\myfft/fft_top_inst/ld_xnm_ad [6]),
	.I2(\myfft/fft_top_inst/isLdxn ),
	.F(\myfft/fft_top_inst/pxna_ad_6_7 )
);
defparam \myfft/fft_top_inst/pxna_ad_6_s5 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/pxna_ad_5_s5  (
	.I0(\myfft/fft_top_inst/ud_xkm_ad [5]),
	.I1(\myfft/fft_top_inst/ld_xnm_ad [5]),
	.I2(\myfft/fft_top_inst/isLdxn ),
	.F(\myfft/fft_top_inst/pxna_ad_5_7 )
);
defparam \myfft/fft_top_inst/pxna_ad_5_s5 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/pxna_ad_4_s5  (
	.I0(\myfft/fft_top_inst/ud_xkm_ad [4]),
	.I1(\myfft/fft_top_inst/ld_xnm_ad [4]),
	.I2(\myfft/fft_top_inst/isLdxn ),
	.F(\myfft/fft_top_inst/pxna_ad_4_7 )
);
defparam \myfft/fft_top_inst/pxna_ad_4_s5 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/pxna_ad_3_s5  (
	.I0(\myfft/fft_top_inst/ud_xkm_ad [3]),
	.I1(\myfft/fft_top_inst/ld_xnm_ad [3]),
	.I2(\myfft/fft_top_inst/isLdxn ),
	.F(\myfft/fft_top_inst/pxna_ad_3_7 )
);
defparam \myfft/fft_top_inst/pxna_ad_3_s5 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/pxna_ad_2_s5  (
	.I0(\myfft/fft_top_inst/ud_xkm_ad [2]),
	.I1(\myfft/fft_top_inst/ld_xnm_ad [2]),
	.I2(\myfft/fft_top_inst/isLdxn ),
	.F(\myfft/fft_top_inst/pxna_ad_2_7 )
);
defparam \myfft/fft_top_inst/pxna_ad_2_s5 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/pxna_ad_1_s5  (
	.I0(\myfft/fft_top_inst/ud_xkm_ad [1]),
	.I1(\myfft/fft_top_inst/ld_xnm_ad [1]),
	.I2(\myfft/fft_top_inst/isLdxn ),
	.F(\myfft/fft_top_inst/pxna_ad_1_7 )
);
defparam \myfft/fft_top_inst/pxna_ad_1_s5 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/pxna_ad_0_s5  (
	.I0(\myfft/fft_top_inst/ud_xkm_ad [0]),
	.I1(\myfft/fft_top_inst/ld_xnm_ad [0]),
	.I2(\myfft/fft_top_inst/isLdxn ),
	.F(\myfft/fft_top_inst/pxna_ad_0_7 )
);
defparam \myfft/fft_top_inst/pxna_ad_0_s5 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/qxna_ad_9_s1  (
	.I0(\myfft/fft_top_inst/cclt_upwad_2 [9]),
	.I1(\myfft/fft_top_inst/twBtfy_upwad [9]),
	.I2(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/qxna_ad_9_4 )
);
defparam \myfft/fft_top_inst/qxna_ad_9_s1 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/qxna_ad_8_s1  (
	.I0(\myfft/fft_top_inst/cclt_upwad_2 [8]),
	.I1(\myfft/fft_top_inst/twBtfy_upwad [8]),
	.I2(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/qxna_ad_8_4 )
);
defparam \myfft/fft_top_inst/qxna_ad_8_s1 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/qxna_ad_7_s1  (
	.I0(\myfft/fft_top_inst/cclt_upwad_2 [7]),
	.I1(\myfft/fft_top_inst/twBtfy_upwad [7]),
	.I2(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/qxna_ad_7_4 )
);
defparam \myfft/fft_top_inst/qxna_ad_7_s1 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/qxna_ad_6_s1  (
	.I0(\myfft/fft_top_inst/cclt_upwad_2 [6]),
	.I1(\myfft/fft_top_inst/twBtfy_upwad [6]),
	.I2(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/qxna_ad_6_4 )
);
defparam \myfft/fft_top_inst/qxna_ad_6_s1 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/qxna_ad_5_s1  (
	.I0(\myfft/fft_top_inst/cclt_upwad_2 [5]),
	.I1(\myfft/fft_top_inst/twBtfy_upwad [5]),
	.I2(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/qxna_ad_5_4 )
);
defparam \myfft/fft_top_inst/qxna_ad_5_s1 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/qxna_ad_4_s1  (
	.I0(\myfft/fft_top_inst/cclt_upwad_2 [4]),
	.I1(\myfft/fft_top_inst/twBtfy_upwad [4]),
	.I2(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/qxna_ad_4_4 )
);
defparam \myfft/fft_top_inst/qxna_ad_4_s1 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/qxna_ad_3_s1  (
	.I0(\myfft/fft_top_inst/cclt_upwad_2 [3]),
	.I1(\myfft/fft_top_inst/twBtfy_upwad [3]),
	.I2(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/qxna_ad_3_4 )
);
defparam \myfft/fft_top_inst/qxna_ad_3_s1 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/qxna_ad_2_s1  (
	.I0(\myfft/fft_top_inst/cclt_upwad_2 [2]),
	.I1(\myfft/fft_top_inst/twBtfy_upwad [2]),
	.I2(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/qxna_ad_2_4 )
);
defparam \myfft/fft_top_inst/qxna_ad_2_s1 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/qxna_ad_1_s1  (
	.I0(\myfft/fft_top_inst/cclt_upwad_2 [1]),
	.I1(\myfft/fft_top_inst/twBtfy_upwad [1]),
	.I2(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/qxna_ad_1_4 )
);
defparam \myfft/fft_top_inst/qxna_ad_1_s1 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/qxna_ad_0_s1  (
	.I0(\myfft/fft_top_inst/cclt_upwad_2 [0]),
	.I1(\myfft/fft_top_inst/twBtfy_upwad [0]),
	.I2(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/qxna_ad_0_4 )
);
defparam \myfft/fft_top_inst/qxna_ad_0_s1 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/qxna_wre_s2  (
	.I0(out_d_12[30]),
	.I1(\myfft/fft_top_inst/dSrc ),
	.I2(\myfft/fft_top_inst/ftCtl_ea ),
	.F(\myfft/fft_top_inst/qxna_wre )
);
defparam \myfft/fft_top_inst/qxna_wre_s2 .INIT=8'h20;
DFFR \myfft/fft_top_inst/sod_reg_s1  (
	.D(fftStart),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/slfRst ),
	.Q(out_d_12[27])
);
defparam \myfft/fft_top_inst/sod_reg_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/soud_reg0_s1  (
	.D(\myfft/fft_top_inst/fft_ok ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/slfRst ),
	.Q(\myfft/fft_top_inst/soud_reg0 )
);
defparam \myfft/fft_top_inst/soud_reg0_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/soud_reg1_s1  (
	.D(\myfft/fft_top_inst/soud_reg0 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/slfRst ),
	.Q(out_d_12[31])
);
defparam \myfft/fft_top_inst/soud_reg1_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/stage_1_s1  (
	.D(\myfft/fft_top_inst/n939_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/slfRst ),
	.Q(\myfft/fft_top_inst/stage [1])
);
defparam \myfft/fft_top_inst/stage_1_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/stage_0_s1  (
	.D(\myfft/fft_top_inst/n940_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/slfRst ),
	.Q(\myfft/fft_top_inst/stage [0])
);
defparam \myfft/fft_top_inst/stage_0_s1 .INIT=1'b0;
MUX2_LUT5 \myfft/fft_top_inst/n944_s0  (
	.I0(\myfft/fft_top_inst/n941_2 ),
	.I1(\myfft/fft_top_inst/n945_2 ),
	.S0(\myfft/fft_top_inst/stage [1]),
	.O(\myfft/fft_top_inst/n944_3 )
);
MUX2_LUT5 \myfft/fft_top_inst/pxna_ad_9_s3  (
	.I0(\myfft/fft_top_inst/pxna_ad_9_7 ),
	.I1(\myfft/fft_top_inst/corePa_ad [9]),
	.S0(\myfft/fft_top_inst/pxna_ad_0_8 ),
	.O(\myfft/fft_top_inst/pxna_ad_9_5 )
);
MUX2_LUT5 \myfft/fft_top_inst/pxna_ad_8_s3  (
	.I0(\myfft/fft_top_inst/pxna_ad_8_7 ),
	.I1(\myfft/fft_top_inst/corePa_ad [8]),
	.S0(\myfft/fft_top_inst/pxna_ad_0_8 ),
	.O(\myfft/fft_top_inst/pxna_ad_8_5 )
);
MUX2_LUT5 \myfft/fft_top_inst/pxna_ad_7_s3  (
	.I0(\myfft/fft_top_inst/pxna_ad_7_7 ),
	.I1(\myfft/fft_top_inst/corePa_ad [7]),
	.S0(\myfft/fft_top_inst/pxna_ad_0_8 ),
	.O(\myfft/fft_top_inst/pxna_ad_7_5 )
);
MUX2_LUT5 \myfft/fft_top_inst/pxna_ad_6_s3  (
	.I0(\myfft/fft_top_inst/pxna_ad_6_7 ),
	.I1(\myfft/fft_top_inst/corePa_ad [6]),
	.S0(\myfft/fft_top_inst/pxna_ad_0_8 ),
	.O(\myfft/fft_top_inst/pxna_ad_6_5 )
);
MUX2_LUT5 \myfft/fft_top_inst/pxna_ad_5_s3  (
	.I0(\myfft/fft_top_inst/pxna_ad_5_7 ),
	.I1(\myfft/fft_top_inst/corePa_ad [5]),
	.S0(\myfft/fft_top_inst/pxna_ad_0_8 ),
	.O(\myfft/fft_top_inst/pxna_ad_5_5 )
);
MUX2_LUT5 \myfft/fft_top_inst/pxna_ad_4_s3  (
	.I0(\myfft/fft_top_inst/pxna_ad_4_7 ),
	.I1(\myfft/fft_top_inst/corePa_ad [4]),
	.S0(\myfft/fft_top_inst/pxna_ad_0_8 ),
	.O(\myfft/fft_top_inst/pxna_ad_4_5 )
);
MUX2_LUT5 \myfft/fft_top_inst/pxna_ad_3_s3  (
	.I0(\myfft/fft_top_inst/pxna_ad_3_7 ),
	.I1(\myfft/fft_top_inst/corePa_ad [3]),
	.S0(\myfft/fft_top_inst/pxna_ad_0_8 ),
	.O(\myfft/fft_top_inst/pxna_ad_3_5 )
);
MUX2_LUT5 \myfft/fft_top_inst/pxna_ad_2_s3  (
	.I0(\myfft/fft_top_inst/pxna_ad_2_7 ),
	.I1(\myfft/fft_top_inst/corePa_ad [2]),
	.S0(\myfft/fft_top_inst/pxna_ad_0_8 ),
	.O(\myfft/fft_top_inst/pxna_ad_2_5 )
);
MUX2_LUT5 \myfft/fft_top_inst/pxna_ad_1_s3  (
	.I0(\myfft/fft_top_inst/pxna_ad_1_7 ),
	.I1(\myfft/fft_top_inst/corePa_ad [1]),
	.S0(\myfft/fft_top_inst/pxna_ad_0_8 ),
	.O(\myfft/fft_top_inst/pxna_ad_1_5 )
);
MUX2_LUT5 \myfft/fft_top_inst/pxna_ad_0_s3  (
	.I0(\myfft/fft_top_inst/pxna_ad_0_7 ),
	.I1(\myfft/fft_top_inst/corePa_ad [0]),
	.S0(\myfft/fft_top_inst/pxna_ad_0_8 ),
	.O(\myfft/fft_top_inst/pxna_ad_0_5 )
);
LUT2 \myfft/fft_top_inst/mem_inst/pmem/reMem/pxna_do_re_15_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/pxna_do_re_b [15]),
	.F(\myfft/fft_top_inst/ud_xkm_re [15])
);
defparam \myfft/fft_top_inst/mem_inst/pmem/reMem/pxna_do_re_15_s .INIT=4'h4;
LUT2 \myfft/fft_top_inst/mem_inst/pmem/reMem/pxna_do_re_14_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/pxna_do_re_b [14]),
	.F(\myfft/fft_top_inst/ud_xkm_re [14])
);
defparam \myfft/fft_top_inst/mem_inst/pmem/reMem/pxna_do_re_14_s .INIT=4'h4;
LUT2 \myfft/fft_top_inst/mem_inst/pmem/reMem/pxna_do_re_13_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/pxna_do_re_b [13]),
	.F(\myfft/fft_top_inst/ud_xkm_re [13])
);
defparam \myfft/fft_top_inst/mem_inst/pmem/reMem/pxna_do_re_13_s .INIT=4'h4;
LUT2 \myfft/fft_top_inst/mem_inst/pmem/reMem/pxna_do_re_12_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/pxna_do_re_b [12]),
	.F(\myfft/fft_top_inst/ud_xkm_re [12])
);
defparam \myfft/fft_top_inst/mem_inst/pmem/reMem/pxna_do_re_12_s .INIT=4'h4;
LUT2 \myfft/fft_top_inst/mem_inst/pmem/reMem/pxna_do_re_11_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/pxna_do_re_b [11]),
	.F(\myfft/fft_top_inst/ud_xkm_re [11])
);
defparam \myfft/fft_top_inst/mem_inst/pmem/reMem/pxna_do_re_11_s .INIT=4'h4;
LUT2 \myfft/fft_top_inst/mem_inst/pmem/reMem/pxna_do_re_10_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/pxna_do_re_b [10]),
	.F(\myfft/fft_top_inst/ud_xkm_re [10])
);
defparam \myfft/fft_top_inst/mem_inst/pmem/reMem/pxna_do_re_10_s .INIT=4'h4;
LUT2 \myfft/fft_top_inst/mem_inst/pmem/reMem/pxna_do_re_9_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/pxna_do_re_b [9]),
	.F(\myfft/fft_top_inst/ud_xkm_re [9])
);
defparam \myfft/fft_top_inst/mem_inst/pmem/reMem/pxna_do_re_9_s .INIT=4'h4;
LUT2 \myfft/fft_top_inst/mem_inst/pmem/reMem/pxna_do_re_8_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/pxna_do_re_b [8]),
	.F(\myfft/fft_top_inst/ud_xkm_re [8])
);
defparam \myfft/fft_top_inst/mem_inst/pmem/reMem/pxna_do_re_8_s .INIT=4'h4;
LUT2 \myfft/fft_top_inst/mem_inst/pmem/reMem/pxna_do_re_7_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/pxna_do_re_b [7]),
	.F(\myfft/fft_top_inst/ud_xkm_re [7])
);
defparam \myfft/fft_top_inst/mem_inst/pmem/reMem/pxna_do_re_7_s .INIT=4'h4;
LUT2 \myfft/fft_top_inst/mem_inst/pmem/reMem/pxna_do_re_6_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/pxna_do_re_b [6]),
	.F(\myfft/fft_top_inst/ud_xkm_re [6])
);
defparam \myfft/fft_top_inst/mem_inst/pmem/reMem/pxna_do_re_6_s .INIT=4'h4;
LUT2 \myfft/fft_top_inst/mem_inst/pmem/reMem/pxna_do_re_5_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/pxna_do_re_b [5]),
	.F(\myfft/fft_top_inst/ud_xkm_re [5])
);
defparam \myfft/fft_top_inst/mem_inst/pmem/reMem/pxna_do_re_5_s .INIT=4'h4;
LUT2 \myfft/fft_top_inst/mem_inst/pmem/reMem/pxna_do_re_4_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/pxna_do_re_b [4]),
	.F(\myfft/fft_top_inst/ud_xkm_re [4])
);
defparam \myfft/fft_top_inst/mem_inst/pmem/reMem/pxna_do_re_4_s .INIT=4'h4;
LUT2 \myfft/fft_top_inst/mem_inst/pmem/reMem/pxna_do_re_3_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/pxna_do_re_b [3]),
	.F(\myfft/fft_top_inst/ud_xkm_re [3])
);
defparam \myfft/fft_top_inst/mem_inst/pmem/reMem/pxna_do_re_3_s .INIT=4'h4;
LUT2 \myfft/fft_top_inst/mem_inst/pmem/reMem/pxna_do_re_2_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/pxna_do_re_b [2]),
	.F(\myfft/fft_top_inst/ud_xkm_re [2])
);
defparam \myfft/fft_top_inst/mem_inst/pmem/reMem/pxna_do_re_2_s .INIT=4'h4;
LUT2 \myfft/fft_top_inst/mem_inst/pmem/reMem/pxna_do_re_1_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/pxna_do_re_b [1]),
	.F(\myfft/fft_top_inst/ud_xkm_re [1])
);
defparam \myfft/fft_top_inst/mem_inst/pmem/reMem/pxna_do_re_1_s .INIT=4'h4;
LUT2 \myfft/fft_top_inst/mem_inst/pmem/reMem/pxna_do_re_0_s34  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/pxna_do_re_b [0]),
	.F(\myfft/fft_top_inst/ud_xkm_re [0])
);
defparam \myfft/fft_top_inst/mem_inst/pmem/reMem/pxna_do_re_0_s34 .INIT=4'h4;
DPB \myfft/fft_top_inst/mem_inst/pmem/reMem/mem_r_mem_r_0_0_s  (
	.CLKA(out_d_11[35]),
	.OCEA(GND),
	.CEA(VCC),
	.RESETA(GND),
	.WREA(\myfft/fft_top_inst/corePa_wre ),
	.CLKB(out_d_11[35]),
	.OCEB(GND),
	.CEB(VCC),
	.RESETB(GND),
	.WREB(\myfft/fft_top_inst/pxna_wre ),
	.BLKSELA({GND, GND, GND}),
	.BLKSELB({GND, GND, GND}),
	.DIA({\myfft/fft_top_inst/qxnb_di_re [15:0]}),
	.DIB({\myfft/fft_top_inst/pxna_di_re [15:0]}),
	.ADA({\myfft/fft_top_inst/corePb_ad [9:0], GND, GND, VCC, VCC}),
	.ADB({\myfft/fft_top_inst/pxna_ad_9_5 , \myfft/fft_top_inst/pxna_ad_8_5 , \myfft/fft_top_inst/pxna_ad_7_5 , \myfft/fft_top_inst/pxna_ad_6_5 , \myfft/fft_top_inst/pxna_ad_5_5 , \myfft/fft_top_inst/pxna_ad_4_5 , \myfft/fft_top_inst/pxna_ad_3_5 , \myfft/fft_top_inst/pxna_ad_2_5 , \myfft/fft_top_inst/pxna_ad_1_5 , \myfft/fft_top_inst/pxna_ad_0_5 , GND, GND, VCC, VCC}),
	.DOA({\myfft/fft_top_inst/pxnb_do_re_b [15:0]}),
	.DOB({\myfft/fft_top_inst/pxna_do_re_b [15:0]})
);
defparam \myfft/fft_top_inst/mem_inst/pmem/reMem/mem_r_mem_r_0_0_s .READ_MODE0=1'b0;
defparam \myfft/fft_top_inst/mem_inst/pmem/reMem/mem_r_mem_r_0_0_s .READ_MODE1=1'b0;
defparam \myfft/fft_top_inst/mem_inst/pmem/reMem/mem_r_mem_r_0_0_s .WRITE_MODE0=2'b01;
defparam \myfft/fft_top_inst/mem_inst/pmem/reMem/mem_r_mem_r_0_0_s .WRITE_MODE1=2'b01;
defparam \myfft/fft_top_inst/mem_inst/pmem/reMem/mem_r_mem_r_0_0_s .BIT_WIDTH_0=16;
defparam \myfft/fft_top_inst/mem_inst/pmem/reMem/mem_r_mem_r_0_0_s .BIT_WIDTH_1=16;
defparam \myfft/fft_top_inst/mem_inst/pmem/reMem/mem_r_mem_r_0_0_s .RESET_MODE="SYNC";
defparam \myfft/fft_top_inst/mem_inst/pmem/reMem/mem_r_mem_r_0_0_s .BLK_SEL_0=3'b000;
defparam \myfft/fft_top_inst/mem_inst/pmem/reMem/mem_r_mem_r_0_0_s .BLK_SEL_1=3'b000;
DPB \myfft/fft_top_inst/mem_inst/pmem/reMem/mem_r_mem_r_0_1_s  (
	.CLKA(out_d_11[35]),
	.OCEA(GND),
	.CEA(VCC),
	.RESETA(GND),
	.WREA(\myfft/fft_top_inst/corePa_wre ),
	.CLKB(out_d_11[35]),
	.OCEB(GND),
	.CEB(VCC),
	.RESETB(GND),
	.WREB(\myfft/fft_top_inst/pxna_wre ),
	.BLKSELA({GND, GND, GND}),
	.BLKSELB({GND, GND, GND}),
	.DIA({\myfft/fft_top_inst/qxnb_di_im [15:0]}),
	.DIB({\myfft/fft_top_inst/pxna_di_im [15:0]}),
	.ADA({\myfft/fft_top_inst/corePb_ad [9:0], GND, GND, VCC, VCC}),
	.ADB({\myfft/fft_top_inst/pxna_ad_9_5 , \myfft/fft_top_inst/pxna_ad_8_5 , \myfft/fft_top_inst/pxna_ad_7_5 , \myfft/fft_top_inst/pxna_ad_6_5 , \myfft/fft_top_inst/pxna_ad_5_5 , \myfft/fft_top_inst/pxna_ad_4_5 , \myfft/fft_top_inst/pxna_ad_3_5 , \myfft/fft_top_inst/pxna_ad_2_5 , \myfft/fft_top_inst/pxna_ad_1_5 , \myfft/fft_top_inst/pxna_ad_0_5 , GND, GND, VCC, VCC}),
	.DOA({\myfft/fft_top_inst/pxnb_do_im_b [15:0]}),
	.DOB({\myfft/fft_top_inst/pxna_do_im_b [15:0]})
);
defparam \myfft/fft_top_inst/mem_inst/pmem/reMem/mem_r_mem_r_0_1_s .READ_MODE0=1'b0;
defparam \myfft/fft_top_inst/mem_inst/pmem/reMem/mem_r_mem_r_0_1_s .READ_MODE1=1'b0;
defparam \myfft/fft_top_inst/mem_inst/pmem/reMem/mem_r_mem_r_0_1_s .WRITE_MODE0=2'b01;
defparam \myfft/fft_top_inst/mem_inst/pmem/reMem/mem_r_mem_r_0_1_s .WRITE_MODE1=2'b01;
defparam \myfft/fft_top_inst/mem_inst/pmem/reMem/mem_r_mem_r_0_1_s .BIT_WIDTH_0=16;
defparam \myfft/fft_top_inst/mem_inst/pmem/reMem/mem_r_mem_r_0_1_s .BIT_WIDTH_1=16;
defparam \myfft/fft_top_inst/mem_inst/pmem/reMem/mem_r_mem_r_0_1_s .RESET_MODE="SYNC";
defparam \myfft/fft_top_inst/mem_inst/pmem/reMem/mem_r_mem_r_0_1_s .BLK_SEL_0=3'b000;
defparam \myfft/fft_top_inst/mem_inst/pmem/reMem/mem_r_mem_r_0_1_s .BLK_SEL_1=3'b000;
LUT2 \myfft/fft_top_inst/mem_inst/pmem/imMem/pxna_do_im_15_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/pxna_do_im_b [15]),
	.F(\myfft/fft_top_inst/ud_xkm_im [15])
);
defparam \myfft/fft_top_inst/mem_inst/pmem/imMem/pxna_do_im_15_s .INIT=4'h4;
LUT2 \myfft/fft_top_inst/mem_inst/pmem/imMem/pxna_do_im_14_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/pxna_do_im_b [14]),
	.F(\myfft/fft_top_inst/ud_xkm_im [14])
);
defparam \myfft/fft_top_inst/mem_inst/pmem/imMem/pxna_do_im_14_s .INIT=4'h4;
LUT2 \myfft/fft_top_inst/mem_inst/pmem/imMem/pxna_do_im_13_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/pxna_do_im_b [13]),
	.F(\myfft/fft_top_inst/ud_xkm_im [13])
);
defparam \myfft/fft_top_inst/mem_inst/pmem/imMem/pxna_do_im_13_s .INIT=4'h4;
LUT2 \myfft/fft_top_inst/mem_inst/pmem/imMem/pxna_do_im_12_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/pxna_do_im_b [12]),
	.F(\myfft/fft_top_inst/ud_xkm_im [12])
);
defparam \myfft/fft_top_inst/mem_inst/pmem/imMem/pxna_do_im_12_s .INIT=4'h4;
LUT2 \myfft/fft_top_inst/mem_inst/pmem/imMem/pxna_do_im_11_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/pxna_do_im_b [11]),
	.F(\myfft/fft_top_inst/ud_xkm_im [11])
);
defparam \myfft/fft_top_inst/mem_inst/pmem/imMem/pxna_do_im_11_s .INIT=4'h4;
LUT2 \myfft/fft_top_inst/mem_inst/pmem/imMem/pxna_do_im_10_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/pxna_do_im_b [10]),
	.F(\myfft/fft_top_inst/ud_xkm_im [10])
);
defparam \myfft/fft_top_inst/mem_inst/pmem/imMem/pxna_do_im_10_s .INIT=4'h4;
LUT2 \myfft/fft_top_inst/mem_inst/pmem/imMem/pxna_do_im_9_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/pxna_do_im_b [9]),
	.F(\myfft/fft_top_inst/ud_xkm_im [9])
);
defparam \myfft/fft_top_inst/mem_inst/pmem/imMem/pxna_do_im_9_s .INIT=4'h4;
LUT2 \myfft/fft_top_inst/mem_inst/pmem/imMem/pxna_do_im_8_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/pxna_do_im_b [8]),
	.F(\myfft/fft_top_inst/ud_xkm_im [8])
);
defparam \myfft/fft_top_inst/mem_inst/pmem/imMem/pxna_do_im_8_s .INIT=4'h4;
LUT2 \myfft/fft_top_inst/mem_inst/pmem/imMem/pxna_do_im_7_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/pxna_do_im_b [7]),
	.F(\myfft/fft_top_inst/ud_xkm_im [7])
);
defparam \myfft/fft_top_inst/mem_inst/pmem/imMem/pxna_do_im_7_s .INIT=4'h4;
LUT2 \myfft/fft_top_inst/mem_inst/pmem/imMem/pxna_do_im_6_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/pxna_do_im_b [6]),
	.F(\myfft/fft_top_inst/ud_xkm_im [6])
);
defparam \myfft/fft_top_inst/mem_inst/pmem/imMem/pxna_do_im_6_s .INIT=4'h4;
LUT2 \myfft/fft_top_inst/mem_inst/pmem/imMem/pxna_do_im_5_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/pxna_do_im_b [5]),
	.F(\myfft/fft_top_inst/ud_xkm_im [5])
);
defparam \myfft/fft_top_inst/mem_inst/pmem/imMem/pxna_do_im_5_s .INIT=4'h4;
LUT2 \myfft/fft_top_inst/mem_inst/pmem/imMem/pxna_do_im_4_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/pxna_do_im_b [4]),
	.F(\myfft/fft_top_inst/ud_xkm_im [4])
);
defparam \myfft/fft_top_inst/mem_inst/pmem/imMem/pxna_do_im_4_s .INIT=4'h4;
LUT2 \myfft/fft_top_inst/mem_inst/pmem/imMem/pxna_do_im_3_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/pxna_do_im_b [3]),
	.F(\myfft/fft_top_inst/ud_xkm_im [3])
);
defparam \myfft/fft_top_inst/mem_inst/pmem/imMem/pxna_do_im_3_s .INIT=4'h4;
LUT2 \myfft/fft_top_inst/mem_inst/pmem/imMem/pxna_do_im_2_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/pxna_do_im_b [2]),
	.F(\myfft/fft_top_inst/ud_xkm_im [2])
);
defparam \myfft/fft_top_inst/mem_inst/pmem/imMem/pxna_do_im_2_s .INIT=4'h4;
LUT2 \myfft/fft_top_inst/mem_inst/pmem/imMem/pxna_do_im_1_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/pxna_do_im_b [1]),
	.F(\myfft/fft_top_inst/ud_xkm_im [1])
);
defparam \myfft/fft_top_inst/mem_inst/pmem/imMem/pxna_do_im_1_s .INIT=4'h4;
LUT2 \myfft/fft_top_inst/mem_inst/pmem/imMem/pxna_do_im_0_s34  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/pxna_do_im_b [0]),
	.F(\myfft/fft_top_inst/ud_xkm_im [0])
);
defparam \myfft/fft_top_inst/mem_inst/pmem/imMem/pxna_do_im_0_s34 .INIT=4'h4;
LUT2 \myfft/fft_top_inst/mem_inst/qmem/reMem/qxnb_do_re_15_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/mem_inst/qmem/reMem/qxnb_do_re_b [15]),
	.F(\myfft/fft_top_inst/coreQb_do_re [15])
);
defparam \myfft/fft_top_inst/mem_inst/qmem/reMem/qxnb_do_re_15_s .INIT=4'h4;
LUT2 \myfft/fft_top_inst/mem_inst/qmem/reMem/qxnb_do_re_14_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/mem_inst/qmem/reMem/qxnb_do_re_b [14]),
	.F(\myfft/fft_top_inst/coreQb_do_re [14])
);
defparam \myfft/fft_top_inst/mem_inst/qmem/reMem/qxnb_do_re_14_s .INIT=4'h4;
LUT2 \myfft/fft_top_inst/mem_inst/qmem/reMem/qxnb_do_re_13_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/mem_inst/qmem/reMem/qxnb_do_re_b [13]),
	.F(\myfft/fft_top_inst/coreQb_do_re [13])
);
defparam \myfft/fft_top_inst/mem_inst/qmem/reMem/qxnb_do_re_13_s .INIT=4'h4;
LUT2 \myfft/fft_top_inst/mem_inst/qmem/reMem/qxnb_do_re_12_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/mem_inst/qmem/reMem/qxnb_do_re_b [12]),
	.F(\myfft/fft_top_inst/coreQb_do_re [12])
);
defparam \myfft/fft_top_inst/mem_inst/qmem/reMem/qxnb_do_re_12_s .INIT=4'h4;
LUT2 \myfft/fft_top_inst/mem_inst/qmem/reMem/qxnb_do_re_11_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/mem_inst/qmem/reMem/qxnb_do_re_b [11]),
	.F(\myfft/fft_top_inst/coreQb_do_re [11])
);
defparam \myfft/fft_top_inst/mem_inst/qmem/reMem/qxnb_do_re_11_s .INIT=4'h4;
LUT2 \myfft/fft_top_inst/mem_inst/qmem/reMem/qxnb_do_re_10_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/mem_inst/qmem/reMem/qxnb_do_re_b [10]),
	.F(\myfft/fft_top_inst/coreQb_do_re [10])
);
defparam \myfft/fft_top_inst/mem_inst/qmem/reMem/qxnb_do_re_10_s .INIT=4'h4;
LUT2 \myfft/fft_top_inst/mem_inst/qmem/reMem/qxnb_do_re_9_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/mem_inst/qmem/reMem/qxnb_do_re_b [9]),
	.F(\myfft/fft_top_inst/coreQb_do_re [9])
);
defparam \myfft/fft_top_inst/mem_inst/qmem/reMem/qxnb_do_re_9_s .INIT=4'h4;
LUT2 \myfft/fft_top_inst/mem_inst/qmem/reMem/qxnb_do_re_8_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/mem_inst/qmem/reMem/qxnb_do_re_b [8]),
	.F(\myfft/fft_top_inst/coreQb_do_re [8])
);
defparam \myfft/fft_top_inst/mem_inst/qmem/reMem/qxnb_do_re_8_s .INIT=4'h4;
LUT2 \myfft/fft_top_inst/mem_inst/qmem/reMem/qxnb_do_re_7_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/mem_inst/qmem/reMem/qxnb_do_re_b [7]),
	.F(\myfft/fft_top_inst/coreQb_do_re [7])
);
defparam \myfft/fft_top_inst/mem_inst/qmem/reMem/qxnb_do_re_7_s .INIT=4'h4;
LUT2 \myfft/fft_top_inst/mem_inst/qmem/reMem/qxnb_do_re_6_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/mem_inst/qmem/reMem/qxnb_do_re_b [6]),
	.F(\myfft/fft_top_inst/coreQb_do_re [6])
);
defparam \myfft/fft_top_inst/mem_inst/qmem/reMem/qxnb_do_re_6_s .INIT=4'h4;
LUT2 \myfft/fft_top_inst/mem_inst/qmem/reMem/qxnb_do_re_5_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/mem_inst/qmem/reMem/qxnb_do_re_b [5]),
	.F(\myfft/fft_top_inst/coreQb_do_re [5])
);
defparam \myfft/fft_top_inst/mem_inst/qmem/reMem/qxnb_do_re_5_s .INIT=4'h4;
LUT2 \myfft/fft_top_inst/mem_inst/qmem/reMem/qxnb_do_re_4_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/mem_inst/qmem/reMem/qxnb_do_re_b [4]),
	.F(\myfft/fft_top_inst/coreQb_do_re [4])
);
defparam \myfft/fft_top_inst/mem_inst/qmem/reMem/qxnb_do_re_4_s .INIT=4'h4;
LUT2 \myfft/fft_top_inst/mem_inst/qmem/reMem/qxnb_do_re_3_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/mem_inst/qmem/reMem/qxnb_do_re_b [3]),
	.F(\myfft/fft_top_inst/coreQb_do_re [3])
);
defparam \myfft/fft_top_inst/mem_inst/qmem/reMem/qxnb_do_re_3_s .INIT=4'h4;
LUT2 \myfft/fft_top_inst/mem_inst/qmem/reMem/qxnb_do_re_2_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/mem_inst/qmem/reMem/qxnb_do_re_b [2]),
	.F(\myfft/fft_top_inst/coreQb_do_re [2])
);
defparam \myfft/fft_top_inst/mem_inst/qmem/reMem/qxnb_do_re_2_s .INIT=4'h4;
LUT2 \myfft/fft_top_inst/mem_inst/qmem/reMem/qxnb_do_re_1_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/mem_inst/qmem/reMem/qxnb_do_re_b [1]),
	.F(\myfft/fft_top_inst/coreQb_do_re [1])
);
defparam \myfft/fft_top_inst/mem_inst/qmem/reMem/qxnb_do_re_1_s .INIT=4'h4;
LUT2 \myfft/fft_top_inst/mem_inst/qmem/reMem/qxnb_do_re_0_s34  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/mem_inst/qmem/reMem/qxnb_do_re_b [0]),
	.F(\myfft/fft_top_inst/coreQb_do_re [0])
);
defparam \myfft/fft_top_inst/mem_inst/qmem/reMem/qxnb_do_re_0_s34 .INIT=4'h4;
DFF \myfft/fft_top_inst/mem_inst/qmem/reMem/qxnb_do_re_0_s33  (
	.D(\myfft/fft_top_inst/slfRst ),
	.CLK(out_d_11[35]),
	.Q(\myfft/fft_top_inst/qxnb_do_re_0_40 )
);
defparam \myfft/fft_top_inst/mem_inst/qmem/reMem/qxnb_do_re_0_s33 .INIT=1'b0;
DPB \myfft/fft_top_inst/mem_inst/qmem/reMem/mem_r_mem_r_0_0_s  (
	.CLKA(out_d_11[35]),
	.OCEA(GND),
	.CEA(VCC),
	.RESETA(GND),
	.WREA(\myfft/fft_top_inst/qxnb_wre ),
	.CLKB(out_d_11[35]),
	.OCEB(GND),
	.CEB(VCC),
	.RESETB(GND),
	.WREB(\myfft/fft_top_inst/qxna_wre ),
	.BLKSELA({GND, GND, GND}),
	.BLKSELB({GND, GND, GND}),
	.DIA({\myfft/fft_top_inst/qxnb_di_re [15:0]}),
	.DIB({\myfft/fft_top_inst/btfy_xkare [15:0]}),
	.ADA({\myfft/fft_top_inst/qxnb_ad [9:0], GND, GND, VCC, VCC}),
	.ADB({\myfft/fft_top_inst/qxna_ad [9:0], GND, GND, VCC, VCC}),
	.DOA({\myfft/fft_top_inst/mem_inst/qmem/reMem/qxnb_do_re_b [15:0]}),
	.DOB({\myfft/fft_top_inst/qxna_do_re_b [15:0]})
);
defparam \myfft/fft_top_inst/mem_inst/qmem/reMem/mem_r_mem_r_0_0_s .READ_MODE0=1'b0;
defparam \myfft/fft_top_inst/mem_inst/qmem/reMem/mem_r_mem_r_0_0_s .READ_MODE1=1'b0;
defparam \myfft/fft_top_inst/mem_inst/qmem/reMem/mem_r_mem_r_0_0_s .WRITE_MODE0=2'b01;
defparam \myfft/fft_top_inst/mem_inst/qmem/reMem/mem_r_mem_r_0_0_s .WRITE_MODE1=2'b01;
defparam \myfft/fft_top_inst/mem_inst/qmem/reMem/mem_r_mem_r_0_0_s .BIT_WIDTH_0=16;
defparam \myfft/fft_top_inst/mem_inst/qmem/reMem/mem_r_mem_r_0_0_s .BIT_WIDTH_1=16;
defparam \myfft/fft_top_inst/mem_inst/qmem/reMem/mem_r_mem_r_0_0_s .RESET_MODE="SYNC";
defparam \myfft/fft_top_inst/mem_inst/qmem/reMem/mem_r_mem_r_0_0_s .BLK_SEL_0=3'b000;
defparam \myfft/fft_top_inst/mem_inst/qmem/reMem/mem_r_mem_r_0_0_s .BLK_SEL_1=3'b000;
DPB \myfft/fft_top_inst/mem_inst/qmem/reMem/mem_r_mem_r_0_1_s  (
	.CLKA(out_d_11[35]),
	.OCEA(GND),
	.CEA(VCC),
	.RESETA(GND),
	.WREA(\myfft/fft_top_inst/qxnb_wre ),
	.CLKB(out_d_11[35]),
	.OCEB(GND),
	.CEB(VCC),
	.RESETB(GND),
	.WREB(\myfft/fft_top_inst/qxna_wre ),
	.BLKSELA({GND, GND, GND}),
	.BLKSELB({GND, GND, GND}),
	.DIA({\myfft/fft_top_inst/qxnb_di_im [15:0]}),
	.DIB({\myfft/fft_top_inst/btfy_xkaim [15:0]}),
	.ADA({\myfft/fft_top_inst/qxnb_ad [9:0], GND, GND, VCC, VCC}),
	.ADB({\myfft/fft_top_inst/qxna_ad [9:0], GND, GND, VCC, VCC}),
	.DOA({\myfft/fft_top_inst/mem_inst/qmem/qxnb_do_im_b [15:0]}),
	.DOB({\myfft/fft_top_inst/qxna_do_im_b [15:0]})
);
defparam \myfft/fft_top_inst/mem_inst/qmem/reMem/mem_r_mem_r_0_1_s .READ_MODE0=1'b0;
defparam \myfft/fft_top_inst/mem_inst/qmem/reMem/mem_r_mem_r_0_1_s .READ_MODE1=1'b0;
defparam \myfft/fft_top_inst/mem_inst/qmem/reMem/mem_r_mem_r_0_1_s .WRITE_MODE0=2'b01;
defparam \myfft/fft_top_inst/mem_inst/qmem/reMem/mem_r_mem_r_0_1_s .WRITE_MODE1=2'b01;
defparam \myfft/fft_top_inst/mem_inst/qmem/reMem/mem_r_mem_r_0_1_s .BIT_WIDTH_0=16;
defparam \myfft/fft_top_inst/mem_inst/qmem/reMem/mem_r_mem_r_0_1_s .BIT_WIDTH_1=16;
defparam \myfft/fft_top_inst/mem_inst/qmem/reMem/mem_r_mem_r_0_1_s .RESET_MODE="SYNC";
defparam \myfft/fft_top_inst/mem_inst/qmem/reMem/mem_r_mem_r_0_1_s .BLK_SEL_0=3'b000;
defparam \myfft/fft_top_inst/mem_inst/qmem/reMem/mem_r_mem_r_0_1_s .BLK_SEL_1=3'b000;
LUT2 \myfft/fft_top_inst/mem_inst/qmem/imMem/qxnb_do_im_15_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/mem_inst/qmem/qxnb_do_im_b [15]),
	.F(\myfft/fft_top_inst/coreQb_do_im [15])
);
defparam \myfft/fft_top_inst/mem_inst/qmem/imMem/qxnb_do_im_15_s .INIT=4'h4;
LUT2 \myfft/fft_top_inst/mem_inst/qmem/imMem/qxnb_do_im_14_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/mem_inst/qmem/qxnb_do_im_b [14]),
	.F(\myfft/fft_top_inst/coreQb_do_im [14])
);
defparam \myfft/fft_top_inst/mem_inst/qmem/imMem/qxnb_do_im_14_s .INIT=4'h4;
LUT2 \myfft/fft_top_inst/mem_inst/qmem/imMem/qxnb_do_im_13_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/mem_inst/qmem/qxnb_do_im_b [13]),
	.F(\myfft/fft_top_inst/coreQb_do_im [13])
);
defparam \myfft/fft_top_inst/mem_inst/qmem/imMem/qxnb_do_im_13_s .INIT=4'h4;
LUT2 \myfft/fft_top_inst/mem_inst/qmem/imMem/qxnb_do_im_12_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/mem_inst/qmem/qxnb_do_im_b [12]),
	.F(\myfft/fft_top_inst/coreQb_do_im [12])
);
defparam \myfft/fft_top_inst/mem_inst/qmem/imMem/qxnb_do_im_12_s .INIT=4'h4;
LUT2 \myfft/fft_top_inst/mem_inst/qmem/imMem/qxnb_do_im_11_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/mem_inst/qmem/qxnb_do_im_b [11]),
	.F(\myfft/fft_top_inst/coreQb_do_im [11])
);
defparam \myfft/fft_top_inst/mem_inst/qmem/imMem/qxnb_do_im_11_s .INIT=4'h4;
LUT2 \myfft/fft_top_inst/mem_inst/qmem/imMem/qxnb_do_im_10_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/mem_inst/qmem/qxnb_do_im_b [10]),
	.F(\myfft/fft_top_inst/coreQb_do_im [10])
);
defparam \myfft/fft_top_inst/mem_inst/qmem/imMem/qxnb_do_im_10_s .INIT=4'h4;
LUT2 \myfft/fft_top_inst/mem_inst/qmem/imMem/qxnb_do_im_9_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/mem_inst/qmem/qxnb_do_im_b [9]),
	.F(\myfft/fft_top_inst/coreQb_do_im [9])
);
defparam \myfft/fft_top_inst/mem_inst/qmem/imMem/qxnb_do_im_9_s .INIT=4'h4;
LUT2 \myfft/fft_top_inst/mem_inst/qmem/imMem/qxnb_do_im_8_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/mem_inst/qmem/qxnb_do_im_b [8]),
	.F(\myfft/fft_top_inst/coreQb_do_im [8])
);
defparam \myfft/fft_top_inst/mem_inst/qmem/imMem/qxnb_do_im_8_s .INIT=4'h4;
LUT2 \myfft/fft_top_inst/mem_inst/qmem/imMem/qxnb_do_im_7_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/mem_inst/qmem/qxnb_do_im_b [7]),
	.F(\myfft/fft_top_inst/coreQb_do_im [7])
);
defparam \myfft/fft_top_inst/mem_inst/qmem/imMem/qxnb_do_im_7_s .INIT=4'h4;
LUT2 \myfft/fft_top_inst/mem_inst/qmem/imMem/qxnb_do_im_6_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/mem_inst/qmem/qxnb_do_im_b [6]),
	.F(\myfft/fft_top_inst/coreQb_do_im [6])
);
defparam \myfft/fft_top_inst/mem_inst/qmem/imMem/qxnb_do_im_6_s .INIT=4'h4;
LUT2 \myfft/fft_top_inst/mem_inst/qmem/imMem/qxnb_do_im_5_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/mem_inst/qmem/qxnb_do_im_b [5]),
	.F(\myfft/fft_top_inst/coreQb_do_im [5])
);
defparam \myfft/fft_top_inst/mem_inst/qmem/imMem/qxnb_do_im_5_s .INIT=4'h4;
LUT2 \myfft/fft_top_inst/mem_inst/qmem/imMem/qxnb_do_im_4_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/mem_inst/qmem/qxnb_do_im_b [4]),
	.F(\myfft/fft_top_inst/coreQb_do_im [4])
);
defparam \myfft/fft_top_inst/mem_inst/qmem/imMem/qxnb_do_im_4_s .INIT=4'h4;
LUT2 \myfft/fft_top_inst/mem_inst/qmem/imMem/qxnb_do_im_3_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/mem_inst/qmem/qxnb_do_im_b [3]),
	.F(\myfft/fft_top_inst/coreQb_do_im [3])
);
defparam \myfft/fft_top_inst/mem_inst/qmem/imMem/qxnb_do_im_3_s .INIT=4'h4;
LUT2 \myfft/fft_top_inst/mem_inst/qmem/imMem/qxnb_do_im_2_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/mem_inst/qmem/qxnb_do_im_b [2]),
	.F(\myfft/fft_top_inst/coreQb_do_im [2])
);
defparam \myfft/fft_top_inst/mem_inst/qmem/imMem/qxnb_do_im_2_s .INIT=4'h4;
LUT2 \myfft/fft_top_inst/mem_inst/qmem/imMem/qxnb_do_im_1_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/mem_inst/qmem/qxnb_do_im_b [1]),
	.F(\myfft/fft_top_inst/coreQb_do_im [1])
);
defparam \myfft/fft_top_inst/mem_inst/qmem/imMem/qxnb_do_im_1_s .INIT=4'h4;
LUT2 \myfft/fft_top_inst/mem_inst/qmem/imMem/qxnb_do_im_0_s34  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/mem_inst/qmem/qxnb_do_im_b [0]),
	.F(\myfft/fft_top_inst/coreQb_do_im [0])
);
defparam \myfft/fft_top_inst/mem_inst/qmem/imMem/qxnb_do_im_0_s34 .INIT=4'h4;
LUT3 \myfft/fft_top_inst/ld_inst/isLstXn_s1  (
	.I0(\myfft/fft_top_inst/ld_xnm_ad [2]),
	.I1(\myfft/fft_top_inst/ld_inst/isLstXn_5 ),
	.I2(\myfft/fft_top_inst/ld_inst/isLstXn_9 ),
	.F(out_d_13[29])
);
defparam \myfft/fft_top_inst/ld_inst/isLstXn_s1 .INIT=8'h80;
LUT2 \myfft/fft_top_inst/ld_inst/slfRst_s1  (
	.I0(out_d_13[29]),
	.I1(\myfft/fft_top_inst/slfRst ),
	.F(\myfft/fft_top_inst/ld_inst/slfRst_0 )
);
defparam \myfft/fft_top_inst/ld_inst/slfRst_s1 .INIT=4'hE;
LUT4 \myfft/fft_top_inst/ld_inst/n35_s0  (
	.I0(\myfft/fft_top_inst/ld_xnm_ad [1]),
	.I1(\myfft/fft_top_inst/ld_inst/n35_4 ),
	.I2(\myfft/fft_top_inst/ld_inst/n35_5 ),
	.I3(\myfft/fft_top_inst/ld_xnm_ad [0]),
	.F(\myfft/fft_top_inst/ld_inst/n35_3 )
);
defparam \myfft/fft_top_inst/ld_inst/n35_s0 .INIT=16'h0708;
LUT4 \myfft/fft_top_inst/ld_inst/n37_s0  (
	.I0(\myfft/fft_top_inst/ld_xnm_ad [3]),
	.I1(\myfft/fft_top_inst/ld_inst/n37_4 ),
	.I2(\myfft/fft_top_inst/ld_inst/n35_5 ),
	.I3(\myfft/fft_top_inst/ld_xnm_ad [2]),
	.F(\myfft/fft_top_inst/ld_inst/n37_3 )
);
defparam \myfft/fft_top_inst/ld_inst/n37_s0 .INIT=16'h0708;
LUT4 \myfft/fft_top_inst/ld_inst/n39_s0  (
	.I0(out_d_13[29]),
	.I1(out_d_13[28]),
	.I2(\myfft/fft_top_inst/ld_inst/isLstXn_5 ),
	.I3(\myfft/fft_top_inst/ld_xnm_ad [4]),
	.F(\myfft/fft_top_inst/ld_inst/n39_3 )
);
defparam \myfft/fft_top_inst/ld_inst/n39_s0 .INIT=16'h3740;
LUT4 \myfft/fft_top_inst/ld_inst/n40_s0  (
	.I0(\myfft/fft_top_inst/ld_xnm_ad [6]),
	.I1(\myfft/fft_top_inst/ld_inst/n40_6 ),
	.I2(\myfft/fft_top_inst/ld_inst/n35_5 ),
	.I3(\myfft/fft_top_inst/ld_xnm_ad [5]),
	.F(\myfft/fft_top_inst/ld_inst/n40_3 )
);
defparam \myfft/fft_top_inst/ld_inst/n40_s0 .INIT=16'h0708;
LUT4 \myfft/fft_top_inst/ld_inst/n42_s0  (
	.I0(out_d_13[29]),
	.I1(out_d_13[28]),
	.I2(\myfft/fft_top_inst/ld_inst/n42_4 ),
	.I3(\myfft/fft_top_inst/ld_xnm_ad [7]),
	.F(\myfft/fft_top_inst/ld_inst/n42_3 )
);
defparam \myfft/fft_top_inst/ld_inst/n42_s0 .INIT=16'h3740;
LUT4 \myfft/fft_top_inst/ld_inst/n43_s0  (
	.I0(out_d_13[29]),
	.I1(out_d_13[28]),
	.I2(\myfft/fft_top_inst/ld_xnm_ad [9]),
	.I3(\myfft/fft_top_inst/ld_xnm_ad [8]),
	.F(\myfft/fft_top_inst/ld_inst/n43_3 )
);
defparam \myfft/fft_top_inst/ld_inst/n43_s0 .INIT=16'h3740;
LUT3 \myfft/fft_top_inst/ld_inst/n44_s0  (
	.I0(out_d_13[29]),
	.I1(\myfft/fft_top_inst/ld_xnm_ad [9]),
	.I2(out_d_13[28]),
	.F(\myfft/fft_top_inst/ld_inst/n44_3 )
);
defparam \myfft/fft_top_inst/ld_inst/n44_s0 .INIT=8'h1C;
LUT3 \myfft/fft_top_inst/ld_inst/n9_s1  (
	.I0(out_d_13[29]),
	.I1(out_d_13[28]),
	.I2(fftStart),
	.F(\myfft/fft_top_inst/ld_inst/n9_5 )
);
defparam \myfft/fft_top_inst/ld_inst/n9_s1 .INIT=8'hF4;
LUT4 \myfft/fft_top_inst/ld_inst/isLstXn_s2  (
	.I0(\myfft/fft_top_inst/ld_xnm_ad [7]),
	.I1(\myfft/fft_top_inst/ld_xnm_ad [6]),
	.I2(\myfft/fft_top_inst/ld_xnm_ad [5]),
	.I3(\myfft/fft_top_inst/ld_inst/n42_4 ),
	.F(\myfft/fft_top_inst/ld_inst/isLstXn_5 )
);
defparam \myfft/fft_top_inst/ld_inst/isLstXn_s2 .INIT=16'h8000;
LUT4 \myfft/fft_top_inst/ld_inst/n35_s1  (
	.I0(\myfft/fft_top_inst/ld_xnm_ad [2]),
	.I1(out_d_13[28]),
	.I2(\myfft/fft_top_inst/ld_inst/isLstXn_5 ),
	.I3(\myfft/fft_top_inst/ld_inst/isLstXn_7 ),
	.F(\myfft/fft_top_inst/ld_inst/n35_4 )
);
defparam \myfft/fft_top_inst/ld_inst/n35_s1 .INIT=16'h8000;
LUT2 \myfft/fft_top_inst/ld_inst/n35_s2  (
	.I0(out_d_13[29]),
	.I1(out_d_13[28]),
	.F(\myfft/fft_top_inst/ld_inst/n35_5 )
);
defparam \myfft/fft_top_inst/ld_inst/n35_s2 .INIT=4'h8;
LUT3 \myfft/fft_top_inst/ld_inst/n37_s1  (
	.I0(\myfft/fft_top_inst/ld_xnm_ad [4]),
	.I1(out_d_13[28]),
	.I2(\myfft/fft_top_inst/ld_inst/isLstXn_5 ),
	.F(\myfft/fft_top_inst/ld_inst/n37_4 )
);
defparam \myfft/fft_top_inst/ld_inst/n37_s1 .INIT=8'h80;
LUT2 \myfft/fft_top_inst/ld_inst/n42_s1  (
	.I0(\myfft/fft_top_inst/ld_xnm_ad [9]),
	.I1(\myfft/fft_top_inst/ld_xnm_ad [8]),
	.F(\myfft/fft_top_inst/ld_inst/n42_4 )
);
defparam \myfft/fft_top_inst/ld_inst/n42_s1 .INIT=4'h8;
LUT2 \myfft/fft_top_inst/ld_inst/isLstXn_s4  (
	.I0(\myfft/fft_top_inst/ld_xnm_ad [4]),
	.I1(\myfft/fft_top_inst/ld_xnm_ad [3]),
	.F(\myfft/fft_top_inst/ld_inst/isLstXn_7 )
);
defparam \myfft/fft_top_inst/ld_inst/isLstXn_s4 .INIT=4'h8;
LUT4 \myfft/fft_top_inst/ld_inst/isLstXn_s5  (
	.I0(\myfft/fft_top_inst/ld_xnm_ad [1]),
	.I1(\myfft/fft_top_inst/ld_xnm_ad [0]),
	.I2(\myfft/fft_top_inst/ld_xnm_ad [4]),
	.I3(\myfft/fft_top_inst/ld_xnm_ad [3]),
	.F(\myfft/fft_top_inst/ld_inst/isLstXn_9 )
);
defparam \myfft/fft_top_inst/ld_inst/isLstXn_s5 .INIT=16'h8000;
LUT4 \myfft/fft_top_inst/ld_inst/n40_s2  (
	.I0(\myfft/fft_top_inst/ld_xnm_ad [7]),
	.I1(out_d_13[28]),
	.I2(\myfft/fft_top_inst/ld_xnm_ad [9]),
	.I3(\myfft/fft_top_inst/ld_xnm_ad [8]),
	.F(\myfft/fft_top_inst/ld_inst/n40_6 )
);
defparam \myfft/fft_top_inst/ld_inst/n40_s2 .INIT=16'h8000;
LUT4 \myfft/fft_top_inst/ld_inst/n41_s1  (
	.I0(out_d_13[29]),
	.I1(out_d_13[28]),
	.I2(\myfft/fft_top_inst/ld_xnm_ad [6]),
	.I3(\myfft/fft_top_inst/ld_inst/n40_6 ),
	.F(\myfft/fft_top_inst/ld_inst/n41_5 )
);
defparam \myfft/fft_top_inst/ld_inst/n41_s1 .INIT=16'h0770;
LUT4 \myfft/fft_top_inst/ld_inst/n38_s1  (
	.I0(out_d_13[29]),
	.I1(out_d_13[28]),
	.I2(\myfft/fft_top_inst/ld_xnm_ad [3]),
	.I3(\myfft/fft_top_inst/ld_inst/n37_4 ),
	.F(\myfft/fft_top_inst/ld_inst/n38_5 )
);
defparam \myfft/fft_top_inst/ld_inst/n38_s1 .INIT=16'h0770;
LUT4 \myfft/fft_top_inst/ld_inst/n36_s1  (
	.I0(out_d_13[29]),
	.I1(out_d_13[28]),
	.I2(\myfft/fft_top_inst/ld_xnm_ad [1]),
	.I3(\myfft/fft_top_inst/ld_inst/n35_4 ),
	.F(\myfft/fft_top_inst/ld_inst/n36_5 )
);
defparam \myfft/fft_top_inst/ld_inst/n36_s1 .INIT=16'h0770;
DFFR \myfft/fft_top_inst/ld_inst/xn_cnt_9_s1  (
	.D(\myfft/fft_top_inst/ld_inst/n35_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/ld_inst/slfRst_0 ),
	.Q(\myfft/fft_top_inst/ld_xnm_ad [0])
);
defparam \myfft/fft_top_inst/ld_inst/xn_cnt_9_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/ld_inst/xn_cnt_8_s1  (
	.D(\myfft/fft_top_inst/ld_inst/n36_5 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/ld_inst/slfRst_0 ),
	.Q(\myfft/fft_top_inst/ld_xnm_ad [1])
);
defparam \myfft/fft_top_inst/ld_inst/xn_cnt_8_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/ld_inst/xn_cnt_7_s1  (
	.D(\myfft/fft_top_inst/ld_inst/n37_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/ld_inst/slfRst_0 ),
	.Q(\myfft/fft_top_inst/ld_xnm_ad [2])
);
defparam \myfft/fft_top_inst/ld_inst/xn_cnt_7_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/ld_inst/xn_cnt_6_s1  (
	.D(\myfft/fft_top_inst/ld_inst/n38_5 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/ld_inst/slfRst_0 ),
	.Q(\myfft/fft_top_inst/ld_xnm_ad [3])
);
defparam \myfft/fft_top_inst/ld_inst/xn_cnt_6_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/ld_inst/xn_cnt_5_s1  (
	.D(\myfft/fft_top_inst/ld_inst/n39_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/ld_inst/slfRst_0 ),
	.Q(\myfft/fft_top_inst/ld_xnm_ad [4])
);
defparam \myfft/fft_top_inst/ld_inst/xn_cnt_5_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/ld_inst/xn_cnt_4_s1  (
	.D(\myfft/fft_top_inst/ld_inst/n40_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/ld_inst/slfRst_0 ),
	.Q(\myfft/fft_top_inst/ld_xnm_ad [5])
);
defparam \myfft/fft_top_inst/ld_inst/xn_cnt_4_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/ld_inst/xn_cnt_3_s1  (
	.D(\myfft/fft_top_inst/ld_inst/n41_5 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/ld_inst/slfRst_0 ),
	.Q(\myfft/fft_top_inst/ld_xnm_ad [6])
);
defparam \myfft/fft_top_inst/ld_inst/xn_cnt_3_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/ld_inst/xn_cnt_2_s1  (
	.D(\myfft/fft_top_inst/ld_inst/n42_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/ld_inst/slfRst_0 ),
	.Q(\myfft/fft_top_inst/ld_xnm_ad [7])
);
defparam \myfft/fft_top_inst/ld_inst/xn_cnt_2_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/ld_inst/xn_cnt_1_s1  (
	.D(\myfft/fft_top_inst/ld_inst/n43_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/ld_inst/slfRst_0 ),
	.Q(\myfft/fft_top_inst/ld_xnm_ad [8])
);
defparam \myfft/fft_top_inst/ld_inst/xn_cnt_1_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/ld_inst/xn_cnt_0_s1  (
	.D(\myfft/fft_top_inst/ld_inst/n44_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/ld_inst/slfRst_0 ),
	.Q(\myfft/fft_top_inst/ld_xnm_ad [9])
);
defparam \myfft/fft_top_inst/ld_inst/xn_cnt_0_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/ld_inst/start_reg_s1  (
	.D(\myfft/fft_top_inst/ld_inst/n9_5 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/ld_inst/slfRst_0 ),
	.Q(out_d_13[28])
);
defparam \myfft/fft_top_inst/ld_inst/start_reg_s1 .INIT=1'b0;
LUT2 \myfft/fft_top_inst/tf_inst/slfRst_s1  (
	.I0(\myfft/fft_top_inst/slfRst ),
	.I1(\myfft/fft_top_inst/fft_ok ),
	.F(\myfft/fft_top_inst/tf_inst/slfRst_3 )
);
defparam \myfft/fft_top_inst/tf_inst/slfRst_s1 .INIT=4'hE;
LUT3 \myfft/fft_top_inst/tf_inst/n417_s1  (
	.I0(\myfft/fft_top_inst/fft_ok ),
	.I1(\myfft/fft_top_inst/ftCtl_ea ),
	.I2(out_d_13[29]),
	.F(\myfft/fft_top_inst/tf_inst/n417_5 )
);
defparam \myfft/fft_top_inst/tf_inst/n417_s1 .INIT=8'hF4;
DFFR \myfft/fft_top_inst/tf_inst/start_reg_s1  (
	.D(\myfft/fft_top_inst/tf_inst/n417_5 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/slfRst ),
	.Q(\myfft/fft_top_inst/ftCtl_ea )
);
defparam \myfft/fft_top_inst/tf_inst/start_reg_s1 .INIT=1'b0;
pROM \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_0_s  (
	.CLK(out_d_11[35]),
	.OCE(GND),
	.CE(VCC),
	.RESET(\myfft/fft_top_inst/tf_inst/slfRst_3 ),
	.AD({\myfft/fft_top_inst/tf_inst/tw_ad [9:0], GND, GND, VCC, VCC}),
	.DO({\myfft/fft_top_inst/tf_inst/tw_inst/DO [31:16], \myfft/fft_top_inst/tf_inst/btfy_twre [15:0]})
);
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_0_s .INIT_RAM_00=256'h7F747F867F977FA67FB47FC17FCD7FD87FE17FE97FF07FF57FF97FFD7FFE7FFF;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_0_s .INIT_RAM_01=256'h7DB07DD57DFA7E1D7E3E7E5F7E7E7E9C7EB97ED57EEF7F097F217F377F4D7F61;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_0_s .INIT_RAM_02=256'h7AB67AEE7B267B5C7B917BC57BF87C297C597C887CB67CE37D0E7D397D627D89;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_0_s .INIT_RAM_03=256'h768D76D87722776B77B377FA783F788478C77909794A798979C87A057A417A7C;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_0_s .INIT_RAM_04=256'h7140719D71F9725472AE7307735E73B5740A745F74B27504755575A575F37641;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_0_s .INIT_RAM_05=256'h6ADC6B4A6BB76C236C8E6CF86D616DC96E306E966EFB6F5E6FC17022708370E2;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_0_s .INIT_RAM_06=256'h637063EE646C64E8656365DD665666CF674667BC683268A66919698B69FD6A6D;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_0_s .INIT_RAM_07=256'h5B0F5B9C5C285CB35D3E5DC75E4F5ED75F5D5FE3606860EB616E61F0627162F1;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_0_s .INIT_RAM_08=256'h51CE52685302539B543254C9556055F5568A571D57B0584258D3596459F35A82;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_0_s .INIT_RAM_09=256'h47C34869490F49B44A584AFB4B9D4C3F4CE04D814E204EBF4F5D4FFB50975133;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_0_s .INIT_RAM_0A=256'h3D073DB83E683F173FC54073412141CE427A432543D0447A452445CD4675471C;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_0_s .INIT_RAM_0B=256'h31B5326E332633DF3496354D360436BA376F382438D9398C3A403AF23BA53C56;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_0_s .INIT_RAM_0C=256'h25E826A82767282628E529A32A612B1F2BDC2C992D552E112ECC2F87304130FB;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_0_s .INIT_RAM_0D=256'h19BE1A821B471C0B1CCF1D931E571F1A1FDD209F2161222322E523A624672528;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_0_s .INIT_RAM_0E=256'h0D540E1C0EE30FAB1072113A120112C8138F1455151C15E216A8176E183318F9;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_0_s .INIT_RAM_0F=256'h00C90192025B032403ED04B6057F0648071107D908A2096A0A330AFB0BC40C8C;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_0_s .INIT_RAM_10=256'hF43CF505F5CDF696F75EF827F8EFF9B8FA81FB4AFC13FCDCFDA5FE6EFF370000;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_0_s .INIT_RAM_11=256'hE7CDE892E958EA1EEAE4EBABEC71ED38EDFFEEC6EF8EF055F11DF1E4F2ACF374;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_0_s .INIT_RAM_12=256'hDB99DC5ADD1BDDDDDE9FDF61E023E0E6E1A9E26DE331E3F5E4B9E57EE642E707;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_0_s .INIT_RAM_13=256'hCFBFD079D134D1EFD2ABD367D424D4E1D59FD65DD71BD7DAD899D958DA18DAD8;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_0_s .INIT_RAM_14=256'hC45BC50EC5C0C674C727C7DCC891C946C9FCCAB3CB6ACC21CCDACD92CE4BCF05;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_0_s .INIT_RAM_15=256'hB98BBA33BADCBB86BC30BCDBBD86BE32BEDFBF8DC03BC0E9C198C248C2F9C3AA;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_0_s .INIT_RAM_16=256'hAF69B005B0A3B141B1E0B27FB320B3C1B463B505B5A8B64CB6F1B797B83DB8E4;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_0_s .INIT_RAM_17=256'hA60DA69CA72DA7BEA850A8E3A976AA0BAAA0AB37ABCEAC65ACFEAD98AE32AECD;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_0_s .INIT_RAM_18=256'h9D8F9E109E929F159F98A01DA0A3A129A1B1A239A2C2A34DA3D8A464A4F1A57E;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_0_s .INIT_RAM_19=256'h9603967596E7975A97CE984498BA993199AA9A239A9D9B189B949C129C909D0F;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_0_s .INIT_RAM_1A=256'h8F7D8FDE903F90A29105916A91D09237929F9308937293DD944994B695249593;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_0_s .INIT_RAM_1B=256'h8A0D8A5B8AAB8AFC8B4E8BA18BF68C4B8CA28CF98D528DAC8E078E638EC08F1E;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_0_s .INIT_RAM_1C=256'h85BF85FB8638867786B686F78739877C87C18806884D889588DE8928897389BF;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_0_s .INIT_RAM_1D=256'h829E82C782F2831D834A837883A783D78408843B846F84A484DA8512854A8584;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_0_s .INIT_RAM_1E=256'h80B380C980DF80F78111812B81478164818281A181C281E38206822B82508277;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_0_s .INIT_RAM_1F=256'h800280038007800B80108017801F80288033803F804C805A8069807A808C809F;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_0_s .INIT_RAM_20=256'h808C807A8069805A804C803F80338028801F80178010800B8007800380028001;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_0_s .INIT_RAM_21=256'h8250822B820681E381C281A1818281648147812B811180F780DF80C980B3809F;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_0_s .INIT_RAM_22=256'h854A851284DA84A4846F843B840883D783A78378834A831D82F282C7829E8277;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_0_s .INIT_RAM_23=256'h8973892888DE8895884D880687C1877C873986F786B68677863885FB85BF8584;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_0_s .INIT_RAM_24=256'h8EC08E638E078DAC8D528CF98CA28C4B8BF68BA18B4E8AFC8AAB8A5B8A0D89BF;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_0_s .INIT_RAM_25=256'h952494B6944993DD93729308929F923791D0916A910590A2903F8FDE8F7D8F1E;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_0_s .INIT_RAM_26=256'h9C909C129B949B189A9D9A2399AA993198BA984497CE975A96E7967596039593;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_0_s .INIT_RAM_27=256'hA4F1A464A3D8A34DA2C2A239A1B1A129A0A3A01D9F989F159E929E109D8F9D0F;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_0_s .INIT_RAM_28=256'hAE32AD98ACFEAC65ABCEAB37AAA0AA0BA976A8E3A850A7BEA72DA69CA60DA57E;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_0_s .INIT_RAM_29=256'hB83DB797B6F1B64CB5A8B505B463B3C1B320B27FB1E0B141B0A3B005AF69AECD;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_0_s .INIT_RAM_2A=256'hC2F9C248C198C0E9C03BBF8DBEDFBE32BD86BCDBBC30BB86BADCBA33B98BB8E4;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_0_s .INIT_RAM_2B=256'hCE4BCD92CCDACC21CB6ACAB3C9FCC946C891C7DCC727C674C5C0C50EC45BC3AA;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_0_s .INIT_RAM_2C=256'hDA18D958D899D7DAD71BD65DD59FD4E1D424D367D2ABD1EFD134D079CFBFCF05;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_0_s .INIT_RAM_2D=256'hE642E57EE4B9E3F5E331E26DE1A9E0E6E023DF61DE9FDDDDDD1BDC5ADB99DAD8;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_0_s .INIT_RAM_2E=256'hF2ACF1E4F11DF055EF8EEEC6EDFFED38EC71EBABEAE4EA1EE958E892E7CDE707;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_0_s .INIT_RAM_2F=256'hFF37FE6EFDA5FCDCFC13FB4AFA81F9B8F8EFF827F75EF696F5CDF505F43CF374;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_0_s .INIT_RAM_30=256'h0BC40AFB0A33096A08A207D907110648057F04B603ED0324025B019200C90000;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_0_s .INIT_RAM_31=256'h1833176E16A815E2151C1455138F12C81201113A10720FAB0EE30E1C0D540C8C;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_0_s .INIT_RAM_32=256'h246723A622E522232161209F1FDD1F1A1E571D931CCF1C0B1B471A8219BE18F9;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_0_s .INIT_RAM_33=256'h30412F872ECC2E112D552C992BDC2B1F2A6129A328E52826276726A825E82528;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_0_s .INIT_RAM_34=256'h3BA53AF23A40398C38D93824376F36BA3604354D349633DF3326326E31B530FB;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_0_s .INIT_RAM_35=256'h467545CD4524447A43D04325427A41CE412140733FC53F173E683DB83D073C56;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_0_s .INIT_RAM_36=256'h50974FFB4F5D4EBF4E204D814CE04C3F4B9D4AFB4A5849B4490F486947C3471C;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_0_s .INIT_RAM_37=256'h59F3596458D3584257B0571D568A55F5556054C95432539B5302526851CE5133;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_0_s .INIT_RAM_38=256'h627161F0616E60EB60685FE35F5D5ED75E4F5DC75D3E5CB35C285B9C5B0F5A82;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_0_s .INIT_RAM_39=256'h69FD698B691968A6683267BC674666CF665665DD656364E8646C63EE637062F1;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_0_s .INIT_RAM_3A=256'h708370226FC16F5E6EFB6E966E306DC96D616CF86C8E6C236BB76B4A6ADC6A6D;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_0_s .INIT_RAM_3B=256'h75F375A57555750474B2745F740A73B5735E730772AE725471F9719D714070E2;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_0_s .INIT_RAM_3C=256'h7A417A0579C87989794A790978C77884783F77FA77B3776B772276D8768D7641;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_0_s .INIT_RAM_3D=256'h7D627D397D0E7CE37CB67C887C597C297BF87BC57B917B5C7B267AEE7AB67A7C;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_0_s .INIT_RAM_3E=256'h7F4D7F377F217F097EEF7ED57EB97E9C7E7E7E5F7E3E7E1D7DFA7DD57DB07D89;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_0_s .INIT_RAM_3F=256'h7FFE7FFD7FF97FF57FF07FE97FE17FD87FCD7FC17FB47FA67F977F867F747F61;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_0_s .READ_MODE=1'b0;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_0_s .BIT_WIDTH=16;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_0_s .RESET_MODE="SYNC";
pROM \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_1_s  (
	.CLK(out_d_11[35]),
	.OCE(GND),
	.CE(VCC),
	.RESET(\myfft/fft_top_inst/tf_inst/slfRst_3 ),
	.AD({\myfft/fft_top_inst/tf_inst/tw_ad [9:0], GND, GND, VCC, VCC}),
	.DO({\myfft/fft_top_inst/tf_inst/tw_inst/DO_0 [31:16], \myfft/fft_top_inst/tf_inst/btfy_twim [15:0]})
);
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_1_s .INIT_RAM_00=256'hF43CF505F5CDF696F75EF827F8EFF9B8FA81FB4AFC13FCDCFDA5FE6EFF370000;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_1_s .INIT_RAM_01=256'hE7CDE892E958EA1EEAE4EBABEC71ED38EDFFEEC6EF8EF055F11DF1E4F2ACF374;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_1_s .INIT_RAM_02=256'hDB99DC5ADD1BDDDDDE9FDF61E023E0E6E1A9E26DE331E3F5E4B9E57EE642E707;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_1_s .INIT_RAM_03=256'hCFBFD079D134D1EFD2ABD367D424D4E1D59FD65DD71BD7DAD899D958DA18DAD8;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_1_s .INIT_RAM_04=256'hC45BC50EC5C0C674C727C7DCC891C946C9FCCAB3CB6ACC21CCDACD92CE4BCF05;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_1_s .INIT_RAM_05=256'hB98BBA33BADCBB86BC30BCDBBD86BE32BEDFBF8DC03BC0E9C198C248C2F9C3AA;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_1_s .INIT_RAM_06=256'hAF69B005B0A3B141B1E0B27FB320B3C1B463B505B5A8B64CB6F1B797B83DB8E4;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_1_s .INIT_RAM_07=256'hA60DA69CA72DA7BEA850A8E3A976AA0BAAA0AB37ABCEAC65ACFEAD98AE32AECD;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_1_s .INIT_RAM_08=256'h9D8F9E109E929F159F98A01DA0A3A129A1B1A239A2C2A34DA3D8A464A4F1A57E;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_1_s .INIT_RAM_09=256'h9603967596E7975A97CE984498BA993199AA9A239A9D9B189B949C129C909D0F;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_1_s .INIT_RAM_0A=256'h8F7D8FDE903F90A29105916A91D09237929F9308937293DD944994B695249593;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_1_s .INIT_RAM_0B=256'h8A0D8A5B8AAB8AFC8B4E8BA18BF68C4B8CA28CF98D528DAC8E078E638EC08F1E;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_1_s .INIT_RAM_0C=256'h85BF85FB8638867786B686F78739877C87C18806884D889588DE8928897389BF;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_1_s .INIT_RAM_0D=256'h829E82C782F2831D834A837883A783D78408843B846F84A484DA8512854A8584;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_1_s .INIT_RAM_0E=256'h80B380C980DF80F78111812B81478164818281A181C281E38206822B82508277;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_1_s .INIT_RAM_0F=256'h800280038007800B80108017801F80288033803F804C805A8069807A808C809F;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_1_s .INIT_RAM_10=256'h808C807A8069805A804C803F80338028801F80178010800B8007800380028001;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_1_s .INIT_RAM_11=256'h8250822B820681E381C281A1818281648147812B811180F780DF80C980B3809F;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_1_s .INIT_RAM_12=256'h854A851284DA84A4846F843B840883D783A78378834A831D82F282C7829E8277;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_1_s .INIT_RAM_13=256'h8973892888DE8895884D880687C1877C873986F786B68677863885FB85BF8584;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_1_s .INIT_RAM_14=256'h8EC08E638E078DAC8D528CF98CA28C4B8BF68BA18B4E8AFC8AAB8A5B8A0D89BF;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_1_s .INIT_RAM_15=256'h952494B6944993DD93729308929F923791D0916A910590A2903F8FDE8F7D8F1E;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_1_s .INIT_RAM_16=256'h9C909C129B949B189A9D9A2399AA993198BA984497CE975A96E7967596039593;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_1_s .INIT_RAM_17=256'hA4F1A464A3D8A34DA2C2A239A1B1A129A0A3A01D9F989F159E929E109D8F9D0F;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_1_s .INIT_RAM_18=256'hAE32AD98ACFEAC65ABCEAB37AAA0AA0BA976A8E3A850A7BEA72DA69CA60DA57E;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_1_s .INIT_RAM_19=256'hB83DB797B6F1B64CB5A8B505B463B3C1B320B27FB1E0B141B0A3B005AF69AECD;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_1_s .INIT_RAM_1A=256'hC2F9C248C198C0E9C03BBF8DBEDFBE32BD86BCDBBC30BB86BADCBA33B98BB8E4;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_1_s .INIT_RAM_1B=256'hCE4BCD92CCDACC21CB6ACAB3C9FCC946C891C7DCC727C674C5C0C50EC45BC3AA;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_1_s .INIT_RAM_1C=256'hDA18D958D899D7DAD71BD65DD59FD4E1D424D367D2ABD1EFD134D079CFBFCF05;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_1_s .INIT_RAM_1D=256'hE642E57EE4B9E3F5E331E26DE1A9E0E6E023DF61DE9FDDDDDD1BDC5ADB99DAD8;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_1_s .INIT_RAM_1E=256'hF2ACF1E4F11DF055EF8EEEC6EDFFED38EC71EBABEAE4EA1EE958E892E7CDE707;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_1_s .INIT_RAM_1F=256'hFF37FE6EFDA5FCDCFC13FB4AFA81F9B8F8EFF827F75EF696F5CDF505F43CF374;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_1_s .INIT_RAM_20=256'h0BC40AFB0A33096A08A207D907110648057F04B603ED0324025B019200C90000;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_1_s .INIT_RAM_21=256'h1833176E16A815E2151C1455138F12C81201113A10720FAB0EE30E1C0D540C8C;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_1_s .INIT_RAM_22=256'h246723A622E522232161209F1FDD1F1A1E571D931CCF1C0B1B471A8219BE18F9;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_1_s .INIT_RAM_23=256'h30412F872ECC2E112D552C992BDC2B1F2A6129A328E52826276726A825E82528;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_1_s .INIT_RAM_24=256'h3BA53AF23A40398C38D93824376F36BA3604354D349633DF3326326E31B530FB;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_1_s .INIT_RAM_25=256'h467545CD4524447A43D04325427A41CE412140733FC53F173E683DB83D073C56;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_1_s .INIT_RAM_26=256'h50974FFB4F5D4EBF4E204D814CE04C3F4B9D4AFB4A5849B4490F486947C3471C;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_1_s .INIT_RAM_27=256'h59F3596458D3584257B0571D568A55F5556054C95432539B5302526851CE5133;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_1_s .INIT_RAM_28=256'h627161F0616E60EB60685FE35F5D5ED75E4F5DC75D3E5CB35C285B9C5B0F5A82;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_1_s .INIT_RAM_29=256'h69FD698B691968A6683267BC674666CF665665DD656364E8646C63EE637062F1;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_1_s .INIT_RAM_2A=256'h708370226FC16F5E6EFB6E966E306DC96D616CF86C8E6C236BB76B4A6ADC6A6D;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_1_s .INIT_RAM_2B=256'h75F375A57555750474B2745F740A73B5735E730772AE725471F9719D714070E2;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_1_s .INIT_RAM_2C=256'h7A417A0579C87989794A790978C77884783F77FA77B3776B772276D8768D7641;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_1_s .INIT_RAM_2D=256'h7D627D397D0E7CE37CB67C887C597C297BF87BC57B917B5C7B267AEE7AB67A7C;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_1_s .INIT_RAM_2E=256'h7F4D7F377F217F097EEF7ED57EB97E9C7E7E7E5F7E3E7E1D7DFA7DD57DB07D89;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_1_s .INIT_RAM_2F=256'h7FFE7FFD7FF97FF57FF07FE97FE17FD87FCD7FC17FB47FA67F977F867F747F61;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_1_s .INIT_RAM_30=256'h7F747F867F977FA67FB47FC17FCD7FD87FE17FE97FF07FF57FF97FFD7FFE7FFF;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_1_s .INIT_RAM_31=256'h7DB07DD57DFA7E1D7E3E7E5F7E7E7E9C7EB97ED57EEF7F097F217F377F4D7F61;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_1_s .INIT_RAM_32=256'h7AB67AEE7B267B5C7B917BC57BF87C297C597C887CB67CE37D0E7D397D627D89;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_1_s .INIT_RAM_33=256'h768D76D87722776B77B377FA783F788478C77909794A798979C87A057A417A7C;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_1_s .INIT_RAM_34=256'h7140719D71F9725472AE7307735E73B5740A745F74B27504755575A575F37641;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_1_s .INIT_RAM_35=256'h6ADC6B4A6BB76C236C8E6CF86D616DC96E306E966EFB6F5E6FC17022708370E2;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_1_s .INIT_RAM_36=256'h637063EE646C64E8656365DD665666CF674667BC683268A66919698B69FD6A6D;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_1_s .INIT_RAM_37=256'h5B0F5B9C5C285CB35D3E5DC75E4F5ED75F5D5FE3606860EB616E61F0627162F1;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_1_s .INIT_RAM_38=256'h51CE52685302539B543254C9556055F5568A571D57B0584258D3596459F35A82;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_1_s .INIT_RAM_39=256'h47C34869490F49B44A584AFB4B9D4C3F4CE04D814E204EBF4F5D4FFB50975133;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_1_s .INIT_RAM_3A=256'h3D073DB83E683F173FC54073412141CE427A432543D0447A452445CD4675471C;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_1_s .INIT_RAM_3B=256'h31B5326E332633DF3496354D360436BA376F382438D9398C3A403AF23BA53C56;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_1_s .INIT_RAM_3C=256'h25E826A82767282628E529A32A612B1F2BDC2C992D552E112ECC2F87304130FB;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_1_s .INIT_RAM_3D=256'h19BE1A821B471C0B1CCF1D931E571F1A1FDD209F2161222322E523A624672528;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_1_s .INIT_RAM_3E=256'h0D540E1C0EE30FAB1072113A120112C8138F1455151C15E216A8176E183318F9;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_1_s .INIT_RAM_3F=256'h00C90192025B032403ED04B6057F0648071107D908A2096A0A330AFB0BC40C8C;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_1_s .READ_MODE=1'b0;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_1_s .BIT_WIDTH=16;
defparam \myfft/fft_top_inst/tf_inst/tw_inst/twRe_r_twRe_r_0_1_s .RESET_MODE="SYNC";
DFFRE \myfft/fft_top_inst/tf_inst/btfy_inst/xa_re_r_14_s0  (
	.D(\myfft/fft_top_inst/tf_inst/btfy_xnare [14]),
	.CLK(out_d_11[35]),
	.CE(\myfft/fft_top_inst/tf_inst/btfy_ea ),
	.RESET(\myfft/fft_top_inst/tf_inst/slfRst_3 ),
	.Q(\myfft/fft_top_inst/tf_inst/btfy_inst/mltA [14])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/xa_re_r_14_s0 .INIT=1'b0;
DFFRE \myfft/fft_top_inst/tf_inst/btfy_inst/xa_re_r_13_s0  (
	.D(\myfft/fft_top_inst/tf_inst/btfy_xnare [13]),
	.CLK(out_d_11[35]),
	.CE(\myfft/fft_top_inst/tf_inst/btfy_ea ),
	.RESET(\myfft/fft_top_inst/tf_inst/slfRst_3 ),
	.Q(\myfft/fft_top_inst/tf_inst/btfy_inst/mltA [13])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/xa_re_r_13_s0 .INIT=1'b0;
DFFRE \myfft/fft_top_inst/tf_inst/btfy_inst/xa_re_r_12_s0  (
	.D(\myfft/fft_top_inst/tf_inst/btfy_xnare [12]),
	.CLK(out_d_11[35]),
	.CE(\myfft/fft_top_inst/tf_inst/btfy_ea ),
	.RESET(\myfft/fft_top_inst/tf_inst/slfRst_3 ),
	.Q(\myfft/fft_top_inst/tf_inst/btfy_inst/mltA [12])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/xa_re_r_12_s0 .INIT=1'b0;
DFFRE \myfft/fft_top_inst/tf_inst/btfy_inst/xa_re_r_11_s0  (
	.D(\myfft/fft_top_inst/tf_inst/btfy_xnare [11]),
	.CLK(out_d_11[35]),
	.CE(\myfft/fft_top_inst/tf_inst/btfy_ea ),
	.RESET(\myfft/fft_top_inst/tf_inst/slfRst_3 ),
	.Q(\myfft/fft_top_inst/tf_inst/btfy_inst/mltA [11])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/xa_re_r_11_s0 .INIT=1'b0;
DFFRE \myfft/fft_top_inst/tf_inst/btfy_inst/xa_re_r_10_s0  (
	.D(\myfft/fft_top_inst/tf_inst/btfy_xnare [10]),
	.CLK(out_d_11[35]),
	.CE(\myfft/fft_top_inst/tf_inst/btfy_ea ),
	.RESET(\myfft/fft_top_inst/tf_inst/slfRst_3 ),
	.Q(\myfft/fft_top_inst/tf_inst/btfy_inst/mltA [10])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/xa_re_r_10_s0 .INIT=1'b0;
DFFRE \myfft/fft_top_inst/tf_inst/btfy_inst/xa_re_r_9_s0  (
	.D(\myfft/fft_top_inst/tf_inst/btfy_xnare [9]),
	.CLK(out_d_11[35]),
	.CE(\myfft/fft_top_inst/tf_inst/btfy_ea ),
	.RESET(\myfft/fft_top_inst/tf_inst/slfRst_3 ),
	.Q(\myfft/fft_top_inst/tf_inst/btfy_inst/mltA [9])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/xa_re_r_9_s0 .INIT=1'b0;
DFFRE \myfft/fft_top_inst/tf_inst/btfy_inst/xa_re_r_8_s0  (
	.D(\myfft/fft_top_inst/tf_inst/btfy_xnare [8]),
	.CLK(out_d_11[35]),
	.CE(\myfft/fft_top_inst/tf_inst/btfy_ea ),
	.RESET(\myfft/fft_top_inst/tf_inst/slfRst_3 ),
	.Q(\myfft/fft_top_inst/tf_inst/btfy_inst/mltA [8])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/xa_re_r_8_s0 .INIT=1'b0;
DFFRE \myfft/fft_top_inst/tf_inst/btfy_inst/xa_re_r_7_s0  (
	.D(\myfft/fft_top_inst/tf_inst/btfy_xnare [7]),
	.CLK(out_d_11[35]),
	.CE(\myfft/fft_top_inst/tf_inst/btfy_ea ),
	.RESET(\myfft/fft_top_inst/tf_inst/slfRst_3 ),
	.Q(\myfft/fft_top_inst/tf_inst/btfy_inst/mltA [7])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/xa_re_r_7_s0 .INIT=1'b0;
DFFRE \myfft/fft_top_inst/tf_inst/btfy_inst/xa_re_r_6_s0  (
	.D(\myfft/fft_top_inst/tf_inst/btfy_xnare [6]),
	.CLK(out_d_11[35]),
	.CE(\myfft/fft_top_inst/tf_inst/btfy_ea ),
	.RESET(\myfft/fft_top_inst/tf_inst/slfRst_3 ),
	.Q(\myfft/fft_top_inst/tf_inst/btfy_inst/mltA [6])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/xa_re_r_6_s0 .INIT=1'b0;
DFFRE \myfft/fft_top_inst/tf_inst/btfy_inst/xa_re_r_5_s0  (
	.D(\myfft/fft_top_inst/tf_inst/btfy_xnare [5]),
	.CLK(out_d_11[35]),
	.CE(\myfft/fft_top_inst/tf_inst/btfy_ea ),
	.RESET(\myfft/fft_top_inst/tf_inst/slfRst_3 ),
	.Q(\myfft/fft_top_inst/tf_inst/btfy_inst/mltA [5])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/xa_re_r_5_s0 .INIT=1'b0;
DFFRE \myfft/fft_top_inst/tf_inst/btfy_inst/xa_re_r_4_s0  (
	.D(\myfft/fft_top_inst/tf_inst/btfy_xnare [4]),
	.CLK(out_d_11[35]),
	.CE(\myfft/fft_top_inst/tf_inst/btfy_ea ),
	.RESET(\myfft/fft_top_inst/tf_inst/slfRst_3 ),
	.Q(\myfft/fft_top_inst/tf_inst/btfy_inst/mltA [4])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/xa_re_r_4_s0 .INIT=1'b0;
DFFRE \myfft/fft_top_inst/tf_inst/btfy_inst/xa_re_r_3_s0  (
	.D(\myfft/fft_top_inst/tf_inst/btfy_xnare [3]),
	.CLK(out_d_11[35]),
	.CE(\myfft/fft_top_inst/tf_inst/btfy_ea ),
	.RESET(\myfft/fft_top_inst/tf_inst/slfRst_3 ),
	.Q(\myfft/fft_top_inst/tf_inst/btfy_inst/mltA [3])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/xa_re_r_3_s0 .INIT=1'b0;
DFFRE \myfft/fft_top_inst/tf_inst/btfy_inst/xa_re_r_2_s0  (
	.D(\myfft/fft_top_inst/tf_inst/btfy_xnare [2]),
	.CLK(out_d_11[35]),
	.CE(\myfft/fft_top_inst/tf_inst/btfy_ea ),
	.RESET(\myfft/fft_top_inst/tf_inst/slfRst_3 ),
	.Q(\myfft/fft_top_inst/tf_inst/btfy_inst/mltA [2])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/xa_re_r_2_s0 .INIT=1'b0;
DFFRE \myfft/fft_top_inst/tf_inst/btfy_inst/xa_re_r_1_s0  (
	.D(\myfft/fft_top_inst/tf_inst/btfy_xnare [1]),
	.CLK(out_d_11[35]),
	.CE(\myfft/fft_top_inst/tf_inst/btfy_ea ),
	.RESET(\myfft/fft_top_inst/tf_inst/slfRst_3 ),
	.Q(\myfft/fft_top_inst/tf_inst/btfy_inst/mltA [1])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/xa_re_r_1_s0 .INIT=1'b0;
DFFRE \myfft/fft_top_inst/tf_inst/btfy_inst/xa_re_r_0_s0  (
	.D(\myfft/fft_top_inst/tf_inst/btfy_xnare [0]),
	.CLK(out_d_11[35]),
	.CE(\myfft/fft_top_inst/tf_inst/btfy_ea ),
	.RESET(\myfft/fft_top_inst/tf_inst/slfRst_3 ),
	.Q(\myfft/fft_top_inst/tf_inst/btfy_inst/mltA [0])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/xa_re_r_0_s0 .INIT=1'b0;
DFFRE \myfft/fft_top_inst/tf_inst/btfy_inst/xa_im_r_15_s0  (
	.D(\myfft/fft_top_inst/tf_inst/btfy_xnaim [15]),
	.CLK(out_d_11[35]),
	.CE(\myfft/fft_top_inst/tf_inst/btfy_ea ),
	.RESET(\myfft/fft_top_inst/tf_inst/slfRst_3 ),
	.Q(\myfft/fft_top_inst/tf_inst/btfy_inst/mltB [15])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/xa_im_r_15_s0 .INIT=1'b0;
DFFRE \myfft/fft_top_inst/tf_inst/btfy_inst/xa_im_r_14_s0  (
	.D(\myfft/fft_top_inst/tf_inst/btfy_xnaim [14]),
	.CLK(out_d_11[35]),
	.CE(\myfft/fft_top_inst/tf_inst/btfy_ea ),
	.RESET(\myfft/fft_top_inst/tf_inst/slfRst_3 ),
	.Q(\myfft/fft_top_inst/tf_inst/btfy_inst/mltB [14])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/xa_im_r_14_s0 .INIT=1'b0;
DFFRE \myfft/fft_top_inst/tf_inst/btfy_inst/xa_im_r_13_s0  (
	.D(\myfft/fft_top_inst/tf_inst/btfy_xnaim [13]),
	.CLK(out_d_11[35]),
	.CE(\myfft/fft_top_inst/tf_inst/btfy_ea ),
	.RESET(\myfft/fft_top_inst/tf_inst/slfRst_3 ),
	.Q(\myfft/fft_top_inst/tf_inst/btfy_inst/mltB [13])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/xa_im_r_13_s0 .INIT=1'b0;
DFFRE \myfft/fft_top_inst/tf_inst/btfy_inst/xa_im_r_12_s0  (
	.D(\myfft/fft_top_inst/tf_inst/btfy_xnaim [12]),
	.CLK(out_d_11[35]),
	.CE(\myfft/fft_top_inst/tf_inst/btfy_ea ),
	.RESET(\myfft/fft_top_inst/tf_inst/slfRst_3 ),
	.Q(\myfft/fft_top_inst/tf_inst/btfy_inst/mltB [12])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/xa_im_r_12_s0 .INIT=1'b0;
DFFRE \myfft/fft_top_inst/tf_inst/btfy_inst/xa_im_r_11_s0  (
	.D(\myfft/fft_top_inst/tf_inst/btfy_xnaim [11]),
	.CLK(out_d_11[35]),
	.CE(\myfft/fft_top_inst/tf_inst/btfy_ea ),
	.RESET(\myfft/fft_top_inst/tf_inst/slfRst_3 ),
	.Q(\myfft/fft_top_inst/tf_inst/btfy_inst/mltB [11])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/xa_im_r_11_s0 .INIT=1'b0;
DFFRE \myfft/fft_top_inst/tf_inst/btfy_inst/xa_im_r_10_s0  (
	.D(\myfft/fft_top_inst/tf_inst/btfy_xnaim [10]),
	.CLK(out_d_11[35]),
	.CE(\myfft/fft_top_inst/tf_inst/btfy_ea ),
	.RESET(\myfft/fft_top_inst/tf_inst/slfRst_3 ),
	.Q(\myfft/fft_top_inst/tf_inst/btfy_inst/mltB [10])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/xa_im_r_10_s0 .INIT=1'b0;
DFFRE \myfft/fft_top_inst/tf_inst/btfy_inst/xa_im_r_9_s0  (
	.D(\myfft/fft_top_inst/tf_inst/btfy_xnaim [9]),
	.CLK(out_d_11[35]),
	.CE(\myfft/fft_top_inst/tf_inst/btfy_ea ),
	.RESET(\myfft/fft_top_inst/tf_inst/slfRst_3 ),
	.Q(\myfft/fft_top_inst/tf_inst/btfy_inst/mltB [9])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/xa_im_r_9_s0 .INIT=1'b0;
DFFRE \myfft/fft_top_inst/tf_inst/btfy_inst/xa_im_r_8_s0  (
	.D(\myfft/fft_top_inst/tf_inst/btfy_xnaim [8]),
	.CLK(out_d_11[35]),
	.CE(\myfft/fft_top_inst/tf_inst/btfy_ea ),
	.RESET(\myfft/fft_top_inst/tf_inst/slfRst_3 ),
	.Q(\myfft/fft_top_inst/tf_inst/btfy_inst/mltB [8])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/xa_im_r_8_s0 .INIT=1'b0;
DFFRE \myfft/fft_top_inst/tf_inst/btfy_inst/xa_im_r_7_s0  (
	.D(\myfft/fft_top_inst/tf_inst/btfy_xnaim [7]),
	.CLK(out_d_11[35]),
	.CE(\myfft/fft_top_inst/tf_inst/btfy_ea ),
	.RESET(\myfft/fft_top_inst/tf_inst/slfRst_3 ),
	.Q(\myfft/fft_top_inst/tf_inst/btfy_inst/mltB [7])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/xa_im_r_7_s0 .INIT=1'b0;
DFFRE \myfft/fft_top_inst/tf_inst/btfy_inst/xa_im_r_6_s0  (
	.D(\myfft/fft_top_inst/tf_inst/btfy_xnaim [6]),
	.CLK(out_d_11[35]),
	.CE(\myfft/fft_top_inst/tf_inst/btfy_ea ),
	.RESET(\myfft/fft_top_inst/tf_inst/slfRst_3 ),
	.Q(\myfft/fft_top_inst/tf_inst/btfy_inst/mltB [6])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/xa_im_r_6_s0 .INIT=1'b0;
DFFRE \myfft/fft_top_inst/tf_inst/btfy_inst/xa_im_r_5_s0  (
	.D(\myfft/fft_top_inst/tf_inst/btfy_xnaim [5]),
	.CLK(out_d_11[35]),
	.CE(\myfft/fft_top_inst/tf_inst/btfy_ea ),
	.RESET(\myfft/fft_top_inst/tf_inst/slfRst_3 ),
	.Q(\myfft/fft_top_inst/tf_inst/btfy_inst/mltB [5])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/xa_im_r_5_s0 .INIT=1'b0;
DFFRE \myfft/fft_top_inst/tf_inst/btfy_inst/xa_im_r_4_s0  (
	.D(\myfft/fft_top_inst/tf_inst/btfy_xnaim [4]),
	.CLK(out_d_11[35]),
	.CE(\myfft/fft_top_inst/tf_inst/btfy_ea ),
	.RESET(\myfft/fft_top_inst/tf_inst/slfRst_3 ),
	.Q(\myfft/fft_top_inst/tf_inst/btfy_inst/mltB [4])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/xa_im_r_4_s0 .INIT=1'b0;
DFFRE \myfft/fft_top_inst/tf_inst/btfy_inst/xa_im_r_3_s0  (
	.D(\myfft/fft_top_inst/tf_inst/btfy_xnaim [3]),
	.CLK(out_d_11[35]),
	.CE(\myfft/fft_top_inst/tf_inst/btfy_ea ),
	.RESET(\myfft/fft_top_inst/tf_inst/slfRst_3 ),
	.Q(\myfft/fft_top_inst/tf_inst/btfy_inst/mltB [3])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/xa_im_r_3_s0 .INIT=1'b0;
DFFRE \myfft/fft_top_inst/tf_inst/btfy_inst/xa_im_r_2_s0  (
	.D(\myfft/fft_top_inst/tf_inst/btfy_xnaim [2]),
	.CLK(out_d_11[35]),
	.CE(\myfft/fft_top_inst/tf_inst/btfy_ea ),
	.RESET(\myfft/fft_top_inst/tf_inst/slfRst_3 ),
	.Q(\myfft/fft_top_inst/tf_inst/btfy_inst/mltB [2])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/xa_im_r_2_s0 .INIT=1'b0;
DFFRE \myfft/fft_top_inst/tf_inst/btfy_inst/xa_im_r_1_s0  (
	.D(\myfft/fft_top_inst/tf_inst/btfy_xnaim [1]),
	.CLK(out_d_11[35]),
	.CE(\myfft/fft_top_inst/tf_inst/btfy_ea ),
	.RESET(\myfft/fft_top_inst/tf_inst/slfRst_3 ),
	.Q(\myfft/fft_top_inst/tf_inst/btfy_inst/mltB [1])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/xa_im_r_1_s0 .INIT=1'b0;
DFFRE \myfft/fft_top_inst/tf_inst/btfy_inst/xa_im_r_0_s0  (
	.D(\myfft/fft_top_inst/tf_inst/btfy_xnaim [0]),
	.CLK(out_d_11[35]),
	.CE(\myfft/fft_top_inst/tf_inst/btfy_ea ),
	.RESET(\myfft/fft_top_inst/tf_inst/slfRst_3 ),
	.Q(\myfft/fft_top_inst/tf_inst/btfy_inst/mltB [0])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/xa_im_r_0_s0 .INIT=1'b0;
DFFRE \myfft/fft_top_inst/tf_inst/btfy_inst/xa_re_r_15_s0  (
	.D(\myfft/fft_top_inst/tf_inst/btfy_xnare [15]),
	.CLK(out_d_11[35]),
	.CE(\myfft/fft_top_inst/tf_inst/btfy_ea ),
	.RESET(\myfft/fft_top_inst/tf_inst/slfRst_3 ),
	.Q(\myfft/fft_top_inst/tf_inst/btfy_inst/mltA [15])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/xa_re_r_15_s0 .INIT=1'b0;
ALU \myfft/fft_top_inst/tf_inst/btfy_inst/constructG_0_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/btfy_inst/mltA [0]),
	.I1(\myfft/fft_top_inst/tf_inst/btfy_inst/reconstructX [0]),
	.I3(GND),
	.CIN(GND),
	.COUT(\myfft/fft_top_inst/tf_inst/btfy_inst/constructG_0_4 ),
	.SUM(\myfft/fft_top_inst/tf_inst/btfy_inst/constructG [0])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/constructG_0_s0 .ALU_MODE=0;
ALU \myfft/fft_top_inst/tf_inst/btfy_inst/constructG_1_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/btfy_inst/mltA [1]),
	.I1(\myfft/fft_top_inst/tf_inst/btfy_inst/reconstructX [1]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/btfy_inst/constructG_0_4 ),
	.COUT(\myfft/fft_top_inst/tf_inst/btfy_inst/constructG_1_4 ),
	.SUM(\myfft/fft_top_inst/btfy_xkare [0])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/constructG_1_s0 .ALU_MODE=0;
ALU \myfft/fft_top_inst/tf_inst/btfy_inst/constructG_2_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/btfy_inst/mltA [2]),
	.I1(\myfft/fft_top_inst/tf_inst/btfy_inst/reconstructX [2]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/btfy_inst/constructG_1_4 ),
	.COUT(\myfft/fft_top_inst/tf_inst/btfy_inst/constructG_2_4 ),
	.SUM(\myfft/fft_top_inst/btfy_xkare [1])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/constructG_2_s0 .ALU_MODE=0;
ALU \myfft/fft_top_inst/tf_inst/btfy_inst/constructG_3_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/btfy_inst/mltA [3]),
	.I1(\myfft/fft_top_inst/tf_inst/btfy_inst/reconstructX [3]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/btfy_inst/constructG_2_4 ),
	.COUT(\myfft/fft_top_inst/tf_inst/btfy_inst/constructG_3_4 ),
	.SUM(\myfft/fft_top_inst/btfy_xkare [2])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/constructG_3_s0 .ALU_MODE=0;
ALU \myfft/fft_top_inst/tf_inst/btfy_inst/constructG_4_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/btfy_inst/mltA [4]),
	.I1(\myfft/fft_top_inst/tf_inst/btfy_inst/reconstructX [4]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/btfy_inst/constructG_3_4 ),
	.COUT(\myfft/fft_top_inst/tf_inst/btfy_inst/constructG_4_4 ),
	.SUM(\myfft/fft_top_inst/btfy_xkare [3])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/constructG_4_s0 .ALU_MODE=0;
ALU \myfft/fft_top_inst/tf_inst/btfy_inst/constructG_5_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/btfy_inst/mltA [5]),
	.I1(\myfft/fft_top_inst/tf_inst/btfy_inst/reconstructX [5]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/btfy_inst/constructG_4_4 ),
	.COUT(\myfft/fft_top_inst/tf_inst/btfy_inst/constructG_5_4 ),
	.SUM(\myfft/fft_top_inst/btfy_xkare [4])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/constructG_5_s0 .ALU_MODE=0;
ALU \myfft/fft_top_inst/tf_inst/btfy_inst/constructG_6_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/btfy_inst/mltA [6]),
	.I1(\myfft/fft_top_inst/tf_inst/btfy_inst/reconstructX [6]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/btfy_inst/constructG_5_4 ),
	.COUT(\myfft/fft_top_inst/tf_inst/btfy_inst/constructG_6_4 ),
	.SUM(\myfft/fft_top_inst/btfy_xkare [5])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/constructG_6_s0 .ALU_MODE=0;
ALU \myfft/fft_top_inst/tf_inst/btfy_inst/constructG_7_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/btfy_inst/mltA [7]),
	.I1(\myfft/fft_top_inst/tf_inst/btfy_inst/reconstructX [7]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/btfy_inst/constructG_6_4 ),
	.COUT(\myfft/fft_top_inst/tf_inst/btfy_inst/constructG_7_4 ),
	.SUM(\myfft/fft_top_inst/btfy_xkare [6])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/constructG_7_s0 .ALU_MODE=0;
ALU \myfft/fft_top_inst/tf_inst/btfy_inst/constructG_8_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/btfy_inst/mltA [8]),
	.I1(\myfft/fft_top_inst/tf_inst/btfy_inst/reconstructX [8]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/btfy_inst/constructG_7_4 ),
	.COUT(\myfft/fft_top_inst/tf_inst/btfy_inst/constructG_8_4 ),
	.SUM(\myfft/fft_top_inst/btfy_xkare [7])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/constructG_8_s0 .ALU_MODE=0;
ALU \myfft/fft_top_inst/tf_inst/btfy_inst/constructG_9_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/btfy_inst/mltA [9]),
	.I1(\myfft/fft_top_inst/tf_inst/btfy_inst/reconstructX [9]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/btfy_inst/constructG_8_4 ),
	.COUT(\myfft/fft_top_inst/tf_inst/btfy_inst/constructG_9_4 ),
	.SUM(\myfft/fft_top_inst/btfy_xkare [8])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/constructG_9_s0 .ALU_MODE=0;
ALU \myfft/fft_top_inst/tf_inst/btfy_inst/constructG_10_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/btfy_inst/mltA [10]),
	.I1(\myfft/fft_top_inst/tf_inst/btfy_inst/reconstructX [10]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/btfy_inst/constructG_9_4 ),
	.COUT(\myfft/fft_top_inst/tf_inst/btfy_inst/constructG_10_4 ),
	.SUM(\myfft/fft_top_inst/btfy_xkare [9])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/constructG_10_s0 .ALU_MODE=0;
ALU \myfft/fft_top_inst/tf_inst/btfy_inst/constructG_11_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/btfy_inst/mltA [11]),
	.I1(\myfft/fft_top_inst/tf_inst/btfy_inst/reconstructX [11]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/btfy_inst/constructG_10_4 ),
	.COUT(\myfft/fft_top_inst/tf_inst/btfy_inst/constructG_11_4 ),
	.SUM(\myfft/fft_top_inst/btfy_xkare [10])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/constructG_11_s0 .ALU_MODE=0;
ALU \myfft/fft_top_inst/tf_inst/btfy_inst/constructG_12_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/btfy_inst/mltA [12]),
	.I1(\myfft/fft_top_inst/tf_inst/btfy_inst/reconstructX [12]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/btfy_inst/constructG_11_4 ),
	.COUT(\myfft/fft_top_inst/tf_inst/btfy_inst/constructG_12_4 ),
	.SUM(\myfft/fft_top_inst/btfy_xkare [11])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/constructG_12_s0 .ALU_MODE=0;
ALU \myfft/fft_top_inst/tf_inst/btfy_inst/constructG_13_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/btfy_inst/mltA [13]),
	.I1(\myfft/fft_top_inst/tf_inst/btfy_inst/reconstructX [13]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/btfy_inst/constructG_12_4 ),
	.COUT(\myfft/fft_top_inst/tf_inst/btfy_inst/constructG_13_4 ),
	.SUM(\myfft/fft_top_inst/btfy_xkare [12])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/constructG_13_s0 .ALU_MODE=0;
ALU \myfft/fft_top_inst/tf_inst/btfy_inst/constructG_14_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/btfy_inst/mltA [14]),
	.I1(\myfft/fft_top_inst/tf_inst/btfy_inst/reconstructX [14]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/btfy_inst/constructG_13_4 ),
	.COUT(\myfft/fft_top_inst/tf_inst/btfy_inst/constructG_14_4 ),
	.SUM(\myfft/fft_top_inst/btfy_xkare [13])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/constructG_14_s0 .ALU_MODE=0;
ALU \myfft/fft_top_inst/tf_inst/btfy_inst/constructG_15_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/btfy_inst/mltA [15]),
	.I1(\myfft/fft_top_inst/tf_inst/btfy_inst/reconstructX [15]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/btfy_inst/constructG_14_4 ),
	.COUT(\myfft/fft_top_inst/tf_inst/btfy_inst/constructG_15_4 ),
	.SUM(\myfft/fft_top_inst/btfy_xkare [14])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/constructG_15_s0 .ALU_MODE=0;
ALU \myfft/fft_top_inst/tf_inst/btfy_inst/constructG_16_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/btfy_inst/mltA [15]),
	.I1(\myfft/fft_top_inst/tf_inst/btfy_inst/reconstructX [15]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/btfy_inst/constructG_15_4 ),
	.COUT(\myfft/fft_top_inst/tf_inst/btfy_inst/constructG_16_1_COUT ),
	.SUM(\myfft/fft_top_inst/btfy_xkare [15])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/constructG_16_s0 .ALU_MODE=0;
ALU \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_0_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/btfy_inst/mltB [0]),
	.I1(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.reconstructY [0]),
	.I3(GND),
	.CIN(GND),
	.COUT(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_0_4 ),
	.SUM(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH [0])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_0_s0 .ALU_MODE=0;
ALU \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_1_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/btfy_inst/mltB [1]),
	.I1(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.reconstructY [1]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_0_4 ),
	.COUT(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_1_4 ),
	.SUM(\myfft/fft_top_inst/btfy_xkaim [0])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_1_s0 .ALU_MODE=0;
ALU \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_2_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/btfy_inst/mltB [2]),
	.I1(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.reconstructY [2]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_1_4 ),
	.COUT(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_2_4 ),
	.SUM(\myfft/fft_top_inst/btfy_xkaim [1])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_2_s0 .ALU_MODE=0;
ALU \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_3_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/btfy_inst/mltB [3]),
	.I1(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.reconstructY [3]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_2_4 ),
	.COUT(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_3_4 ),
	.SUM(\myfft/fft_top_inst/btfy_xkaim [2])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_3_s0 .ALU_MODE=0;
ALU \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_4_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/btfy_inst/mltB [4]),
	.I1(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.reconstructY [4]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_3_4 ),
	.COUT(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_4_4 ),
	.SUM(\myfft/fft_top_inst/btfy_xkaim [3])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_4_s0 .ALU_MODE=0;
ALU \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_5_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/btfy_inst/mltB [5]),
	.I1(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.reconstructY [5]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_4_4 ),
	.COUT(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_5_4 ),
	.SUM(\myfft/fft_top_inst/btfy_xkaim [4])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_5_s0 .ALU_MODE=0;
ALU \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_6_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/btfy_inst/mltB [6]),
	.I1(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.reconstructY [6]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_5_4 ),
	.COUT(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_6_4 ),
	.SUM(\myfft/fft_top_inst/btfy_xkaim [5])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_6_s0 .ALU_MODE=0;
ALU \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_7_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/btfy_inst/mltB [7]),
	.I1(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.reconstructY [7]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_6_4 ),
	.COUT(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_7_4 ),
	.SUM(\myfft/fft_top_inst/btfy_xkaim [6])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_7_s0 .ALU_MODE=0;
ALU \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_8_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/btfy_inst/mltB [8]),
	.I1(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.reconstructY [8]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_7_4 ),
	.COUT(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_8_4 ),
	.SUM(\myfft/fft_top_inst/btfy_xkaim [7])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_8_s0 .ALU_MODE=0;
ALU \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_9_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/btfy_inst/mltB [9]),
	.I1(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.reconstructY [9]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_8_4 ),
	.COUT(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_9_4 ),
	.SUM(\myfft/fft_top_inst/btfy_xkaim [8])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_9_s0 .ALU_MODE=0;
ALU \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_10_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/btfy_inst/mltB [10]),
	.I1(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.reconstructY [10]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_9_4 ),
	.COUT(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_10_4 ),
	.SUM(\myfft/fft_top_inst/btfy_xkaim [9])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_10_s0 .ALU_MODE=0;
ALU \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_11_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/btfy_inst/mltB [11]),
	.I1(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.reconstructY [11]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_10_4 ),
	.COUT(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_11_4 ),
	.SUM(\myfft/fft_top_inst/btfy_xkaim [10])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_11_s0 .ALU_MODE=0;
ALU \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_12_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/btfy_inst/mltB [12]),
	.I1(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.reconstructY [12]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_11_4 ),
	.COUT(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_12_4 ),
	.SUM(\myfft/fft_top_inst/btfy_xkaim [11])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_12_s0 .ALU_MODE=0;
ALU \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_13_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/btfy_inst/mltB [13]),
	.I1(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.reconstructY [13]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_12_4 ),
	.COUT(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_13_4 ),
	.SUM(\myfft/fft_top_inst/btfy_xkaim [12])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_13_s0 .ALU_MODE=0;
ALU \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_14_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/btfy_inst/mltB [14]),
	.I1(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.reconstructY [14]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_13_4 ),
	.COUT(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_14_4 ),
	.SUM(\myfft/fft_top_inst/btfy_xkaim [13])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_14_s0 .ALU_MODE=0;
ALU \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_15_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/btfy_inst/mltB [15]),
	.I1(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.reconstructY [15]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_14_4 ),
	.COUT(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_15_4 ),
	.SUM(\myfft/fft_top_inst/btfy_xkaim [14])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_15_s0 .ALU_MODE=0;
ALU \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_16_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/btfy_inst/mltB [15]),
	.I1(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.reconstructY [15]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_15_4 ),
	.COUT(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_16_1_COUT ),
	.SUM(\myfft/fft_top_inst/btfy_xkaim [15])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructH_16_s0 .ALU_MODE=0;
ALU \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_0_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/btfy_inst/mltA [0]),
	.I1(\myfft/fft_top_inst/tf_inst/btfy_inst/reconstructX [0]),
	.I3(GND),
	.CIN(VCC),
	.COUT(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_0_5 ),
	.SUM(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI [0])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_0_s0 .ALU_MODE=1;
ALU \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_1_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/btfy_inst/mltA [1]),
	.I1(\myfft/fft_top_inst/tf_inst/btfy_inst/reconstructX [1]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_0_5 ),
	.COUT(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_1_5 ),
	.SUM(\myfft/fft_top_inst/qxnb_di_re [0])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_1_s0 .ALU_MODE=1;
ALU \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_2_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/btfy_inst/mltA [2]),
	.I1(\myfft/fft_top_inst/tf_inst/btfy_inst/reconstructX [2]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_1_5 ),
	.COUT(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_2_5 ),
	.SUM(\myfft/fft_top_inst/qxnb_di_re [1])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_2_s0 .ALU_MODE=1;
ALU \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_3_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/btfy_inst/mltA [3]),
	.I1(\myfft/fft_top_inst/tf_inst/btfy_inst/reconstructX [3]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_2_5 ),
	.COUT(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_3_5 ),
	.SUM(\myfft/fft_top_inst/qxnb_di_re [2])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_3_s0 .ALU_MODE=1;
ALU \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_4_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/btfy_inst/mltA [4]),
	.I1(\myfft/fft_top_inst/tf_inst/btfy_inst/reconstructX [4]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_3_5 ),
	.COUT(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_4_5 ),
	.SUM(\myfft/fft_top_inst/qxnb_di_re [3])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_4_s0 .ALU_MODE=1;
ALU \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_5_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/btfy_inst/mltA [5]),
	.I1(\myfft/fft_top_inst/tf_inst/btfy_inst/reconstructX [5]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_4_5 ),
	.COUT(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_5_5 ),
	.SUM(\myfft/fft_top_inst/qxnb_di_re [4])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_5_s0 .ALU_MODE=1;
ALU \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_6_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/btfy_inst/mltA [6]),
	.I1(\myfft/fft_top_inst/tf_inst/btfy_inst/reconstructX [6]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_5_5 ),
	.COUT(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_6_5 ),
	.SUM(\myfft/fft_top_inst/qxnb_di_re [5])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_6_s0 .ALU_MODE=1;
ALU \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_7_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/btfy_inst/mltA [7]),
	.I1(\myfft/fft_top_inst/tf_inst/btfy_inst/reconstructX [7]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_6_5 ),
	.COUT(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_7_5 ),
	.SUM(\myfft/fft_top_inst/qxnb_di_re [6])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_7_s0 .ALU_MODE=1;
ALU \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_8_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/btfy_inst/mltA [8]),
	.I1(\myfft/fft_top_inst/tf_inst/btfy_inst/reconstructX [8]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_7_5 ),
	.COUT(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_8_5 ),
	.SUM(\myfft/fft_top_inst/qxnb_di_re [7])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_8_s0 .ALU_MODE=1;
ALU \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_9_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/btfy_inst/mltA [9]),
	.I1(\myfft/fft_top_inst/tf_inst/btfy_inst/reconstructX [9]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_8_5 ),
	.COUT(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_9_5 ),
	.SUM(\myfft/fft_top_inst/qxnb_di_re [8])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_9_s0 .ALU_MODE=1;
ALU \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_10_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/btfy_inst/mltA [10]),
	.I1(\myfft/fft_top_inst/tf_inst/btfy_inst/reconstructX [10]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_9_5 ),
	.COUT(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_10_5 ),
	.SUM(\myfft/fft_top_inst/qxnb_di_re [9])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_10_s0 .ALU_MODE=1;
ALU \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_11_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/btfy_inst/mltA [11]),
	.I1(\myfft/fft_top_inst/tf_inst/btfy_inst/reconstructX [11]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_10_5 ),
	.COUT(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_11_5 ),
	.SUM(\myfft/fft_top_inst/qxnb_di_re [10])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_11_s0 .ALU_MODE=1;
ALU \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_12_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/btfy_inst/mltA [12]),
	.I1(\myfft/fft_top_inst/tf_inst/btfy_inst/reconstructX [12]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_11_5 ),
	.COUT(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_12_5 ),
	.SUM(\myfft/fft_top_inst/qxnb_di_re [11])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_12_s0 .ALU_MODE=1;
ALU \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_13_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/btfy_inst/mltA [13]),
	.I1(\myfft/fft_top_inst/tf_inst/btfy_inst/reconstructX [13]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_12_5 ),
	.COUT(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_13_5 ),
	.SUM(\myfft/fft_top_inst/qxnb_di_re [12])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_13_s0 .ALU_MODE=1;
ALU \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_14_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/btfy_inst/mltA [14]),
	.I1(\myfft/fft_top_inst/tf_inst/btfy_inst/reconstructX [14]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_13_5 ),
	.COUT(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_14_5 ),
	.SUM(\myfft/fft_top_inst/qxnb_di_re [13])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_14_s0 .ALU_MODE=1;
ALU \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_15_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/btfy_inst/mltA [15]),
	.I1(\myfft/fft_top_inst/tf_inst/btfy_inst/reconstructX [15]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_14_5 ),
	.COUT(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_15_5 ),
	.SUM(\myfft/fft_top_inst/qxnb_di_re [14])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_15_s0 .ALU_MODE=1;
ALU \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_16_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/btfy_inst/mltA [15]),
	.I1(\myfft/fft_top_inst/tf_inst/btfy_inst/reconstructX [15]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_15_5 ),
	.COUT(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_16_1_COUT ),
	.SUM(\myfft/fft_top_inst/qxnb_di_re [15])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructI_16_s0 .ALU_MODE=1;
ALU \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_0_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/btfy_inst/mltB [0]),
	.I1(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.reconstructY [0]),
	.I3(GND),
	.CIN(VCC),
	.COUT(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_0_5 ),
	.SUM(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ [0])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_0_s0 .ALU_MODE=1;
ALU \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_1_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/btfy_inst/mltB [1]),
	.I1(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.reconstructY [1]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_0_5 ),
	.COUT(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_1_5 ),
	.SUM(\myfft/fft_top_inst/qxnb_di_im [0])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_1_s0 .ALU_MODE=1;
ALU \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_2_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/btfy_inst/mltB [2]),
	.I1(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.reconstructY [2]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_1_5 ),
	.COUT(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_2_5 ),
	.SUM(\myfft/fft_top_inst/qxnb_di_im [1])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_2_s0 .ALU_MODE=1;
ALU \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_3_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/btfy_inst/mltB [3]),
	.I1(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.reconstructY [3]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_2_5 ),
	.COUT(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_3_5 ),
	.SUM(\myfft/fft_top_inst/qxnb_di_im [2])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_3_s0 .ALU_MODE=1;
ALU \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_4_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/btfy_inst/mltB [4]),
	.I1(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.reconstructY [4]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_3_5 ),
	.COUT(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_4_5 ),
	.SUM(\myfft/fft_top_inst/qxnb_di_im [3])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_4_s0 .ALU_MODE=1;
ALU \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_5_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/btfy_inst/mltB [5]),
	.I1(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.reconstructY [5]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_4_5 ),
	.COUT(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_5_5 ),
	.SUM(\myfft/fft_top_inst/qxnb_di_im [4])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_5_s0 .ALU_MODE=1;
ALU \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_6_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/btfy_inst/mltB [6]),
	.I1(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.reconstructY [6]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_5_5 ),
	.COUT(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_6_5 ),
	.SUM(\myfft/fft_top_inst/qxnb_di_im [5])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_6_s0 .ALU_MODE=1;
ALU \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_7_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/btfy_inst/mltB [7]),
	.I1(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.reconstructY [7]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_6_5 ),
	.COUT(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_7_5 ),
	.SUM(\myfft/fft_top_inst/qxnb_di_im [6])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_7_s0 .ALU_MODE=1;
ALU \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_8_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/btfy_inst/mltB [8]),
	.I1(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.reconstructY [8]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_7_5 ),
	.COUT(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_8_5 ),
	.SUM(\myfft/fft_top_inst/qxnb_di_im [7])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_8_s0 .ALU_MODE=1;
ALU \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_9_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/btfy_inst/mltB [9]),
	.I1(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.reconstructY [9]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_8_5 ),
	.COUT(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_9_5 ),
	.SUM(\myfft/fft_top_inst/qxnb_di_im [8])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_9_s0 .ALU_MODE=1;
ALU \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_10_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/btfy_inst/mltB [10]),
	.I1(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.reconstructY [10]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_9_5 ),
	.COUT(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_10_5 ),
	.SUM(\myfft/fft_top_inst/qxnb_di_im [9])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_10_s0 .ALU_MODE=1;
ALU \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_11_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/btfy_inst/mltB [11]),
	.I1(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.reconstructY [11]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_10_5 ),
	.COUT(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_11_5 ),
	.SUM(\myfft/fft_top_inst/qxnb_di_im [10])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_11_s0 .ALU_MODE=1;
ALU \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_12_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/btfy_inst/mltB [12]),
	.I1(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.reconstructY [12]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_11_5 ),
	.COUT(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_12_5 ),
	.SUM(\myfft/fft_top_inst/qxnb_di_im [11])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_12_s0 .ALU_MODE=1;
ALU \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_13_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/btfy_inst/mltB [13]),
	.I1(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.reconstructY [13]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_12_5 ),
	.COUT(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_13_5 ),
	.SUM(\myfft/fft_top_inst/qxnb_di_im [12])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_13_s0 .ALU_MODE=1;
ALU \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_14_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/btfy_inst/mltB [14]),
	.I1(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.reconstructY [14]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_13_5 ),
	.COUT(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_14_5 ),
	.SUM(\myfft/fft_top_inst/qxnb_di_im [13])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_14_s0 .ALU_MODE=1;
ALU \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_15_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/btfy_inst/mltB [15]),
	.I1(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.reconstructY [15]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_14_5 ),
	.COUT(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_15_5 ),
	.SUM(\myfft/fft_top_inst/qxnb_di_im [14])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_15_s0 .ALU_MODE=1;
ALU \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_16_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/btfy_inst/mltB [15]),
	.I1(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.reconstructY [15]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_15_5 ),
	.COUT(\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_16_1_COUT ),
	.SUM(\myfft/fft_top_inst/qxnb_di_im [15])
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.constructJ_16_s0 .ALU_MODE=1;
MULTADDALU18X18 \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.multX/iMultaddalu18  (
	.ACCLOAD(GND),
	.CE(\myfft/fft_top_inst/tf_inst/btfy_ea ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/slfRst_3 ),
	.SIA({GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND}),
	.SIB({GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND}),
	.A0({\myfft/fft_top_inst/tf_inst/btfy_xnbre [15], \myfft/fft_top_inst/tf_inst/btfy_xnbre [15], \myfft/fft_top_inst/tf_inst/btfy_xnbre [15:0]}),
	.B0({\myfft/fft_top_inst/tf_inst/btfy_twre [15], \myfft/fft_top_inst/tf_inst/btfy_twre [15], \myfft/fft_top_inst/tf_inst/btfy_twre [15:0]}),
	.A1({\myfft/fft_top_inst/tf_inst/btfy_xnbim [15], \myfft/fft_top_inst/tf_inst/btfy_xnbim [15], \myfft/fft_top_inst/tf_inst/btfy_xnbim [15:0]}),
	.B1({\myfft/fft_top_inst/tf_inst/btfy_twim [15], \myfft/fft_top_inst/tf_inst/btfy_twim [15], \myfft/fft_top_inst/tf_inst/btfy_twim [15:0]}),
	.C({GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND}),
	.CASI({GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND}),
	.ASIGN({VCC, VCC}),
	.BSIGN({VCC, VCC}),
	.ASEL({GND, GND}),
	.BSEL({GND, GND}),
	.SOA({\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.multX/SOA [17:0]}),
	.SOB({\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.multX/SOB [17:0]}),
	.CASO({\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.multX/CASO [54:0]}),
	.DOUT({\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.multX/DOUT [53:31], \myfft/fft_top_inst/tf_inst/btfy_inst/reconstructX [15:0], \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.multX/DOUT [14:0]})
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.multX/iMultaddalu18 .A0REG=1'b0;
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.multX/iMultaddalu18 .A1REG=1'b0;
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.multX/iMultaddalu18 .B0REG=1'b0;
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.multX/iMultaddalu18 .B1REG=1'b0;
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.multX/iMultaddalu18 .CREG=1'b0;
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.multX/iMultaddalu18 .SOA_REG=1'b0;
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.multX/iMultaddalu18 .OUT_REG=1'b1;
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.multX/iMultaddalu18 .PIPE0_REG=1'b0;
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.multX/iMultaddalu18 .PIPE1_REG=1'b0;
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.multX/iMultaddalu18 .ASIGN0_REG=1'b0;
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.multX/iMultaddalu18 .ASIGN1_REG=1'b0;
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.multX/iMultaddalu18 .BSIGN0_REG=1'b0;
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.multX/iMultaddalu18 .BSIGN1_REG=1'b0;
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.multX/iMultaddalu18 .B_ADD_SUB=1'b1;
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.multX/iMultaddalu18 .C_ADD_SUB=1'b0;
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.multX/iMultaddalu18 .MULT_RESET_MODE="SYNC";
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.multX/iMultaddalu18 .ACCLOAD_REG0=1'b0;
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.multX/iMultaddalu18 .ACCLOAD_REG1=1'b0;
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.multX/iMultaddalu18 .MULTADDALU18X18_MODE=0;
MULTADDALU18X18 \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.multY/iMultaddalu18  (
	.ACCLOAD(GND),
	.CE(\myfft/fft_top_inst/tf_inst/btfy_ea ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/slfRst_3 ),
	.SIA({GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND}),
	.SIB({GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND}),
	.A0({\myfft/fft_top_inst/tf_inst/btfy_xnbre [15], \myfft/fft_top_inst/tf_inst/btfy_xnbre [15], \myfft/fft_top_inst/tf_inst/btfy_xnbre [15:0]}),
	.B0({\myfft/fft_top_inst/tf_inst/btfy_twim [15], \myfft/fft_top_inst/tf_inst/btfy_twim [15], \myfft/fft_top_inst/tf_inst/btfy_twim [15:0]}),
	.A1({\myfft/fft_top_inst/tf_inst/btfy_xnbim [15], \myfft/fft_top_inst/tf_inst/btfy_xnbim [15], \myfft/fft_top_inst/tf_inst/btfy_xnbim [15:0]}),
	.B1({\myfft/fft_top_inst/tf_inst/btfy_twre [15], \myfft/fft_top_inst/tf_inst/btfy_twre [15], \myfft/fft_top_inst/tf_inst/btfy_twre [15:0]}),
	.C({GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND}),
	.CASI({GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND}),
	.ASIGN({VCC, VCC}),
	.BSIGN({VCC, VCC}),
	.ASEL({GND, GND}),
	.BSEL({GND, GND}),
	.SOA({\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.multY/SOA [17:0]}),
	.SOB({\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.multY/SOB [17:0]}),
	.CASO({\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.multY/CASO [54:0]}),
	.DOUT({\myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.multY/DOUT [53:31], \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.reconstructY [15:0], \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.multY/DOUT [14:0]})
);
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.multY/iMultaddalu18 .A0REG=1'b0;
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.multY/iMultaddalu18 .A1REG=1'b0;
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.multY/iMultaddalu18 .B0REG=1'b0;
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.multY/iMultaddalu18 .B1REG=1'b0;
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.multY/iMultaddalu18 .CREG=1'b0;
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.multY/iMultaddalu18 .SOA_REG=1'b0;
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.multY/iMultaddalu18 .OUT_REG=1'b1;
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.multY/iMultaddalu18 .PIPE0_REG=1'b0;
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.multY/iMultaddalu18 .PIPE1_REG=1'b0;
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.multY/iMultaddalu18 .ASIGN0_REG=1'b0;
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.multY/iMultaddalu18 .ASIGN1_REG=1'b0;
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.multY/iMultaddalu18 .BSIGN0_REG=1'b0;
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.multY/iMultaddalu18 .BSIGN1_REG=1'b0;
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.multY/iMultaddalu18 .B_ADD_SUB=1'b0;
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.multY/iMultaddalu18 .C_ADD_SUB=1'b0;
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.multY/iMultaddalu18 .MULT_RESET_MODE="SYNC";
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.multY/iMultaddalu18 .ACCLOAD_REG0=1'b0;
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.multY/iMultaddalu18 .ACCLOAD_REG1=1'b0;
defparam \myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.multY/iMultaddalu18 .MULTADDALU18X18_MODE=0;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstLvl_s1  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/l_r [0]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/l_r [2]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/l_r [1]),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/l_r [3]),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstLvl )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstLvl_s1 .INIT=16'h1000;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_s1  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_5 ),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_6 ),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_7 ),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_8 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_s1 .INIT=16'h0100;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_s1  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_5 ),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_6 ),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_7 ),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_8 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_s1 .INIT=16'h8000;
LUT3 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/lcRst_s1  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy ),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp ),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstLvl ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/lcRst )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/lcRst_s1 .INIT=8'h80;
LUT2 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_s1  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/lcRst ),
	.I1(\myfft/fft_top_inst/tf_inst/slfRst_3 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_s1 .INIT=4'hE;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n148_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/l_r [2]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n148_7 ),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n148_5 ),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/l_r [3]),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n148_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n148_s0 .INIT=16'h7F80;
LUT3 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n150_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/l_r [0]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n148_7 ),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/l_r [1]),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n150_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n150_s0 .INIT=8'h78;
LUT3 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n164_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl [9]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl [8]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n148_7 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n164_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n164_s0 .INIT=8'hAC;
LUT3 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n165_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl [7]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl [8]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n148_7 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n165_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n165_s0 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n166_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl [6]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl [7]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n148_7 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n166_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n166_s0 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n167_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl [5]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl [6]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n148_7 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n167_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n167_s0 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n168_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl [4]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl [5]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n148_7 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n168_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n168_s0 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n169_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl [3]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl [4]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n148_7 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n169_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n169_s0 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n170_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl [2]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl [3]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n148_7 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n170_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n170_s0 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n171_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl [1]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl [2]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n148_7 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n171_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n171_s0 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n172_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl [0]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl [1]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n148_7 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n172_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n172_s0 .INIT=8'hCA;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n217_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r [8]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n217_4 ),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n148_7 ),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r [9]),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n217_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n217_s0 .INIT=16'h0708;
LUT3 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n218_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n148_7 ),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r [8]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n217_4 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n218_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n218_s0 .INIT=8'h14;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n219_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r [6]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n219_4 ),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n148_7 ),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r [7]),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n219_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n219_s0 .INIT=16'h0708;
LUT3 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n220_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n148_7 ),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r [6]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n219_4 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n220_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n220_s0 .INIT=8'h14;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n221_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp ),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n221_4 ),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n221_5 ),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r [5]),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n221_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n221_s0 .INIT=16'h3740;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n222_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp ),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n221_4 ),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n222_4 ),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r [4]),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n222_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n222_s0 .INIT=16'h3740;
LUT3 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n223_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n148_7 ),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r [3]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n223_4 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n223_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n223_s0 .INIT=8'h14;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n224_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp ),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n221_4 ),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n224_4 ),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r [2]),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n224_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n224_s0 .INIT=16'h3740;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n225_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp ),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n221_4 ),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r [0]),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r [1]),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n225_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n225_s0 .INIT=16'h3740;
LUT3 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n250_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg [8]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg [9]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n148_7 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n250_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n250_s0 .INIT=8'hAC;
LUT3 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n251_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg [7]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg [8]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n148_7 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n251_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n251_s0 .INIT=8'hAC;
LUT3 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n252_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg [6]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg [7]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n148_7 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n252_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n252_s0 .INIT=8'hAC;
LUT3 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n253_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg [5]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg [6]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n148_7 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n253_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n253_s0 .INIT=8'hAC;
LUT3 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n254_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg [4]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg [5]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n148_7 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n254_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n254_s0 .INIT=8'hAC;
LUT3 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n255_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg [3]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg [4]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n148_7 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n255_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n255_s0 .INIT=8'hAC;
LUT3 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n256_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg [2]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg [3]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n148_7 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n256_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n256_s0 .INIT=8'hAC;
LUT3 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n257_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg [1]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg [2]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n148_7 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n257_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n257_s0 .INIT=8'hAC;
LUT3 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n258_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg [0]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg [1]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n148_7 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n258_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n258_s0 .INIT=8'hAC;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n344_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n344_4 ),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n344_5 ),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n221_4 ),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r [9]),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n344_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n344_s0 .INIT=16'h0708;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n345_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r [7]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n344_4 ),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n221_4 ),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r [8]),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n345_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n345_s0 .INIT=16'h0708;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n347_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r [5]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n347_6 ),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n221_4 ),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r [6]),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n347_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n347_s0 .INIT=16'h0708;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n349_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n349_4 ),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n349_5 ),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n221_4 ),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r [4]),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n349_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n349_s0 .INIT=16'h0708;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n350_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r [2]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n349_4 ),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n221_4 ),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r [3]),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n350_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n350_s0 .INIT=16'h0708;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n352_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy ),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfy_ea ),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r [0]),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r [1]),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n352_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n352_s0 .INIT=16'h3740;
LUT3 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n353_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy ),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r [0]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfy_ea ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n353_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n353_s0 .INIT=8'h1C;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n354_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp ),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n354_4 ),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n354_5 ),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n221_4 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n354_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n354_s0 .INIT=16'h44F0;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n357_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n357_4 ),
	.I1(\myfft/fft_top_inst/twBtfy_upwad [6]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n357_5 ),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n221_4 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n357_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n357_s0 .INIT=16'hAA3C;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n358_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n358_4 ),
	.I1(\myfft/fft_top_inst/twBtfy_upwad [5]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy ),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n358_5 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n358_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n358_s0 .INIT=16'hA3CC;
LUT3 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n359_s0  (
	.I0(\myfft/fft_top_inst/twBtfy_upwad [4]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n359_4 ),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfy_ea ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n359_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n359_s0 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n360_s0  (
	.I0(\myfft/fft_top_inst/twBtfy_upwad [3]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n360_4 ),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfy_ea ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n360_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n360_s0 .INIT=8'h3A;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n361_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n361_4 ),
	.I1(\myfft/fft_top_inst/twBtfy_upwad [2]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy ),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n361_5 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n361_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n361_s0 .INIT=16'hA3CC;
LUT3 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n362_s0  (
	.I0(\myfft/fft_top_inst/twBtfy_upwad [1]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n362_4 ),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfy_ea ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n362_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n362_s0 .INIT=8'hCA;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n363_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy ),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n363_4 ),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfy_ea ),
	.I3(\myfft/fft_top_inst/twBtfy_upwad [0]),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n363_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n363_s0 .INIT=16'h8FD0;
LUT3 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n407_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/wspan [8]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/wspan [9]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n148_7 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n407_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n407_s0 .INIT=8'hAC;
LUT3 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n408_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/wspan [7]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/wspan [8]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n148_7 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n408_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n408_s0 .INIT=8'hAC;
LUT3 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n409_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/wspan [6]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/wspan [7]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n148_7 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n409_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n409_s0 .INIT=8'hAC;
LUT3 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n410_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/wspan [5]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/wspan [6]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n148_7 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n410_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n410_s0 .INIT=8'hAC;
LUT3 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n411_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/wspan [4]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/wspan [5]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n148_7 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n411_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n411_s0 .INIT=8'hAC;
LUT3 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n412_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/wspan [3]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/wspan [4]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n148_7 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n412_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n412_s0 .INIT=8'hAC;
LUT3 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n413_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/wspan [2]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/wspan [3]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n148_7 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n413_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n413_s0 .INIT=8'hAC;
LUT3 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n414_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/wspan [1]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/wspan [2]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n148_7 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n414_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n414_s0 .INIT=8'hAC;
LUT3 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n415_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/wspan [0]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/wspan [1]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n148_7 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n415_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n415_s0 .INIT=8'hAC;
LUT3 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n441_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/tstep [9]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/tstep [8]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n148_7 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n441_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n441_s0 .INIT=8'hAC;
LUT3 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n442_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/tstep [7]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/tstep [8]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n148_7 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n442_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n442_s0 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n443_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/tstep [6]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/tstep [7]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n148_7 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n443_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n443_s0 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n444_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/tstep [5]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/tstep [6]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n148_7 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n444_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n444_s0 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n445_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/tstep [4]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/tstep [5]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n148_7 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n445_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n445_s0 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n446_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/tstep [3]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/tstep [4]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n148_7 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n446_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n446_s0 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n447_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/tstep [2]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/tstep [3]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n148_7 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n447_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n447_s0 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n448_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/tstep [1]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/tstep [2]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n148_7 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n448_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n448_s0 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n449_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/tstep [0]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/tstep [1]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n148_7 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n449_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n449_s0 .INIT=8'hCA;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n492_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy ),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n472_1 ),
	.I2(\myfft/fft_top_inst/tf_inst/tw_ad [9]),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfy_ea ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n492_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n492_s0 .INIT=16'h44F0;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n493_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy ),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n473_1 ),
	.I2(\myfft/fft_top_inst/tf_inst/tw_ad [8]),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfy_ea ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n493_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n493_s0 .INIT=16'h44F0;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n494_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy ),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n474_1 ),
	.I2(\myfft/fft_top_inst/tf_inst/tw_ad [7]),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfy_ea ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n494_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n494_s0 .INIT=16'h44F0;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n495_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy ),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n475_1 ),
	.I2(\myfft/fft_top_inst/tf_inst/tw_ad [6]),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfy_ea ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n495_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n495_s0 .INIT=16'h44F0;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n496_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy ),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n476_1 ),
	.I2(\myfft/fft_top_inst/tf_inst/tw_ad [5]),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfy_ea ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n496_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n496_s0 .INIT=16'h44F0;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n497_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy ),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n477_1 ),
	.I2(\myfft/fft_top_inst/tf_inst/tw_ad [4]),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfy_ea ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n497_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n497_s0 .INIT=16'h44F0;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n498_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy ),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n478_1 ),
	.I2(\myfft/fft_top_inst/tf_inst/tw_ad [3]),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfy_ea ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n498_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n498_s0 .INIT=16'h44F0;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n499_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy ),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n479_1 ),
	.I2(\myfft/fft_top_inst/tf_inst/tw_ad [2]),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfy_ea ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n499_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n499_s0 .INIT=16'h44F0;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n500_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy ),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n480_1 ),
	.I2(\myfft/fft_top_inst/tf_inst/tw_ad [1]),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfy_ea ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n500_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n500_s0 .INIT=16'h44F0;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n501_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy ),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n481_1 ),
	.I2(\myfft/fft_top_inst/tf_inst/tw_ad [0]),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfy_ea ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n501_3 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n501_s0 .INIT=16'h44F0;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_s2  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r [6]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl [6]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_21 ),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_10 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_5 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_s2 .INIT=16'hDFB6;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_s3  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r [4]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl [4]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_11 ),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_12 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_6 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_s3 .INIT=16'hDFB6;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_s4  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_13 ),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r [8]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl [8]),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_14 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_7 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_s4 .INIT=16'hE7BE;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_s5  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r [3]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl [3]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_15 ),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_16 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_8 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_s5 .INIT=16'h6900;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_s2  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_9 ),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r [6]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg [6]),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_10 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_5 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_s2 .INIT=16'hBAC3;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_s3  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_11 ),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_12 ),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_13 ),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_14 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_6 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_s3 .INIT=16'hF100;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_s4  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_15 ),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_16 ),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_17 ),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_26 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_7 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_s4 .INIT=16'h0008;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_s5  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r [9]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_19 ),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg [9]),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_20 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_8 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_s5 .INIT=16'h6100;
LUT2 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n148_s2  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/l_r [0]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/l_r [1]),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n148_5 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n148_s2 .INIT=4'h8;
LUT3 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n217_s1  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r [5]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n223_4 ),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n217_5 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n217_4 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n217_s1 .INIT=8'h80;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n219_s1  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r [3]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r [4]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r [5]),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n223_4 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n219_4 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n219_s1 .INIT=16'h8000;
LUT2 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n221_s1  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy ),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfy_ea ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n221_4 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n221_s1 .INIT=4'h8;
LUT2 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n221_s2  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r [4]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n222_4 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n221_5 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n221_s2 .INIT=4'h8;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n222_s1  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r [1]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r [2]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r [3]),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r [0]),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n222_4 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n222_s1 .INIT=16'h8000;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n223_s1  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r [1]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r [2]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r [0]),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n221_4 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n223_4 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n223_s1 .INIT=16'h8000;
LUT2 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n224_s1  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r [1]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r [0]),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n224_4 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n224_s1 .INIT=4'h8;
LUT3 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n344_s1  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r [5]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r [6]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n347_6 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n344_4 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n344_s1 .INIT=8'h80;
LUT2 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n344_s2  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r [7]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r [8]),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n344_5 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n344_s2 .INIT=4'h8;
LUT3 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n349_s1  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r [1]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfy_ea ),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r [0]),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n349_4 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n349_s1 .INIT=8'h80;
LUT2 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n349_s2  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r [2]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r [3]),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n349_5 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n349_s2 .INIT=4'h8;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n354_s1  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfy_dwnad [7]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfy_dwnad [8]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n354_9 ),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfy_dwnad [9]),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n354_4 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n354_s1 .INIT=16'h7F80;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n354_s2  (
	.I0(\myfft/fft_top_inst/twBtfy_upwad [8]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n357_5 ),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n354_7 ),
	.I3(\myfft/fft_top_inst/twBtfy_upwad [9]),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n354_5 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n354_s2 .INIT=16'h7F80;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n355_s2  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfy_dwnad [7]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n354_9 ),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp ),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfy_dwnad [8]),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n355_5 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n355_s2 .INIT=16'h0708;
LUT3 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n356_s1  (
	.I0(\myfft/fft_top_inst/twBtfy_upwad [6]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n357_5 ),
	.I2(\myfft/fft_top_inst/twBtfy_upwad [7]),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n356_4 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n356_s1 .INIT=8'h78;
LUT3 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n356_s2  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp ),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfy_dwnad [7]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n354_9 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n356_5 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n356_s2 .INIT=8'h14;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n357_s1  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n357_6 ),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n357_7 ),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp ),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfy_dwnad [6]),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n357_4 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n357_s1 .INIT=16'h0708;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n357_s2  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfy_ea ),
	.I1(\myfft/fft_top_inst/twBtfy_upwad [5]),
	.I2(\myfft/fft_top_inst/twBtfy_upwad [4]),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n357_8 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n357_5 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n357_s2 .INIT=16'h8000;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n358_s1  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfy_dwnad [4]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n357_6 ),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp ),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfy_dwnad [5]),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n358_4 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n358_s1 .INIT=16'h0708;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n358_s2  (
	.I0(\myfft/fft_top_inst/twBtfy_upwad [4]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n357_8 ),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy ),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfy_ea ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n358_5 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n358_s2 .INIT=16'hF800;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n359_s1  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n359_5 ),
	.I1(\myfft/fft_top_inst/twBtfy_upwad [4]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n357_8 ),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n359_4 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n359_s1 .INIT=16'hAA3C;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n360_s1  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp ),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n360_5 ),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n360_6 ),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n360_4 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n360_s1 .INIT=16'hBB0F;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n361_s1  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfy_dwnad [1]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfy_dwnad [0]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp ),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfy_dwnad [2]),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n361_4 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n361_s1 .INIT=16'h0708;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n361_s2  (
	.I0(\myfft/fft_top_inst/twBtfy_upwad [0]),
	.I1(\myfft/fft_top_inst/twBtfy_upwad [1]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy ),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfy_ea ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n361_5 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n361_s2 .INIT=16'hF800;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n362_s1  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n362_5 ),
	.I1(\myfft/fft_top_inst/twBtfy_upwad [0]),
	.I2(\myfft/fft_top_inst/twBtfy_upwad [1]),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n362_4 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n362_s1 .INIT=16'hAA3C;
LUT2 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n363_s1  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp ),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfy_dwnad [0]),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n363_4 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n363_s1 .INIT=4'h1;
LUT2 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_s7  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r [7]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl [7]),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_10 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_s7 .INIT=4'h6;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_s8  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl [3]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl [2]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl [1]),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl [0]),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_11 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_s8 .INIT=16'h0001;
LUT2 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_s9  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r [5]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl [5]),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_12 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_s9 .INIT=4'h6;
LUT2 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_s10  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r [9]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl [9]),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_13 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_s10 .INIT=4'h4;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_s11  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl [7]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl [6]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_11 ),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_17 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_14 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_s11 .INIT=16'h1000;
LUT3 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_s12  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl [2]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl [1]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl [0]),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_15 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_s12 .INIT=8'h01;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_s13  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl [9]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r [9]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_18 ),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_19 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_16 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_s13 .INIT=16'h00B0;
LUT2 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_s6  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r [7]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg [7]),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_9 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_s6 .INIT=4'h6;
LUT3 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_s7  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg [5]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg [4]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_13 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_10 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_s7 .INIT=8'h10;
LUT2 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_s8  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r [4]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg [4]),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_11 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_s8 .INIT=4'h4;
LUT2 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_s9  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r [5]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg [5]),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_12 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_s9 .INIT=4'h6;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_s10  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg [3]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg [2]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg [1]),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg [0]),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_13 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_s10 .INIT=16'h0001;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_s11  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r [0]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg [0]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r [1]),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg [1]),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_14 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_s11 .INIT=16'hCAAC;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_s12  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg [2]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_21 ),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r [3]),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg [3]),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_15 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_s12 .INIT=16'hB44B;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_s13  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_13 ),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_22 ),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r [8]),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg [8]),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_16 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_s13 .INIT=16'h7887;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_s14  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r [4]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg [4]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_13 ),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_12 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_17 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_s14 .INIT=16'h00B2;
LUT2 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_s16  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg [8]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r [8]),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_19 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_s16 .INIT=4'h4;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_s17  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r [0]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg [0]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_23 ),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_24 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_20 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_s17 .INIT=16'h0007;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n217_s2  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r [3]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r [4]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r [6]),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r [7]),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n217_5 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n217_s2 .INIT=16'h8000;
LUT2 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n354_s4  (
	.I0(\myfft/fft_top_inst/twBtfy_upwad [7]),
	.I1(\myfft/fft_top_inst/twBtfy_upwad [6]),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n354_7 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n354_s4 .INIT=4'h8;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n357_s3  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfy_dwnad [1]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfy_dwnad [0]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfy_dwnad [2]),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfy_dwnad [3]),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n357_6 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n357_s3 .INIT=16'h8000;
LUT2 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n357_s4  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfy_dwnad [4]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfy_dwnad [5]),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n357_7 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n357_s4 .INIT=4'h8;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n357_s5  (
	.I0(\myfft/fft_top_inst/twBtfy_upwad [0]),
	.I1(\myfft/fft_top_inst/twBtfy_upwad [3]),
	.I2(\myfft/fft_top_inst/twBtfy_upwad [2]),
	.I3(\myfft/fft_top_inst/twBtfy_upwad [1]),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n357_8 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n357_s5 .INIT=16'h8000;
LUT3 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n359_s2  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp ),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfy_dwnad [4]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n357_6 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n359_5 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n359_s2 .INIT=8'h14;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n360_s2  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfy_dwnad [1]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfy_dwnad [0]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfy_dwnad [2]),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfy_dwnad [3]),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n360_5 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n360_s2 .INIT=16'h7F80;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n360_s3  (
	.I0(\myfft/fft_top_inst/twBtfy_upwad [0]),
	.I1(\myfft/fft_top_inst/twBtfy_upwad [2]),
	.I2(\myfft/fft_top_inst/twBtfy_upwad [1]),
	.I3(\myfft/fft_top_inst/twBtfy_upwad [3]),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n360_6 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n360_s3 .INIT=16'h7F80;
LUT3 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n362_s2  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp ),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfy_dwnad [1]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfy_dwnad [0]),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n362_5 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n362_s2 .INIT=8'h14;
LUT2 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_s14  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl [5]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl [4]),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_17 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_s14 .INIT=4'h1;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_s15  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl [1]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl [0]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r [2]),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl [2]),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_18 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_s15 .INIT=16'hE11E;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_s16  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r [1]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl [1]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r [0]),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl [0]),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_19 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_s16 .INIT=16'hF69F;
LUT2 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_s18  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg [1]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg [0]),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_21 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_s18 .INIT=4'h1;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_s19  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg [7]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg [6]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg [5]),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg [4]),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_22 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_s19 .INIT=16'h0001;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_s20  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg [4]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r [4]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg [5]),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r [5]),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_23 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_s20 .INIT=16'h0BB0;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_s21  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg [6]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r [6]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg [7]),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r [7]),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_24 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_s21 .INIT=16'h0BB0;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_s22  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r [2]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg [2]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg [1]),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg [0]),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_26 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy_s22 .INIT=16'h6669;
LUT3 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_s17  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_11 ),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl [5]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl [4]),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_21 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp_s17 .INIT=8'h02;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n149_s1  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n148_7 ),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/l_r [0]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/l_r [1]),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/l_r [2]),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n149_5 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n149_s1 .INIT=16'h7F80;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n347_s2  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r [4]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n349_4 ),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r [2]),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r [3]),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n347_6 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n347_s2 .INIT=16'h8000;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n355_s3  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n357_5 ),
	.I1(\myfft/fft_top_inst/twBtfy_upwad [7]),
	.I2(\myfft/fft_top_inst/twBtfy_upwad [6]),
	.I3(\myfft/fft_top_inst/twBtfy_upwad [8]),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n355_7 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n355_s3 .INIT=16'h7F80;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n354_s5  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfy_dwnad [6]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n357_6 ),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfy_dwnad [4]),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfy_dwnad [5]),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n354_9 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n354_s5 .INIT=16'h8000;
LUT3 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n148_s3  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp ),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy ),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfy_ea ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n148_7 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n148_s3 .INIT=8'h80;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n356_s3  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n356_4 ),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n356_5 ),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy ),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfy_ea ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n356_7 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n356_s3 .INIT=16'hCAAA;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n355_s4  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n355_7 ),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n355_5 ),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy ),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfy_ea ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n355_9 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n355_s4 .INIT=16'hCAAA;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n351_s1  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy ),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfy_ea ),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r [2]),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n349_4 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n351_5 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n351_s1 .INIT=16'h0770;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n348_s1  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy ),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfy_ea ),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r [5]),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n347_6 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n348_5 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n348_s1 .INIT=16'h0770;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n346_s1  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy ),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfy_ea ),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r [7]),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n344_4 ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n346_5 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n346_s1 .INIT=16'h0770;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n226_s1  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp ),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r [0]),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy ),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfy_ea ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n226_5 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n226_s1 .INIT=16'h1CCC;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n151_s1  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/l_r [0]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp ),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy ),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfy_ea ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n151_5 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n151_s1 .INIT=16'h6AAA;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n440_s2  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp ),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy ),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfy_ea ),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/tstep [9]),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n440_7 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n440_s2 .INIT=16'h7F00;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n416_s2  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp ),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy ),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfy_ea ),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/wspan [0]),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n416_7 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n416_s2 .INIT=16'h7F00;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n259_s2  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp ),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy ),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfy_ea ),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg [0]),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n259_7 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n259_s2 .INIT=16'h7F00;
LUT4 \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n163_s2  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstGrp ),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/isLstBtfy ),
	.I2(\myfft/fft_top_inst/tf_inst/twBtfy_ea ),
	.I3(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl [9]),
	.F(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n163_7 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n163_s2 .INIT=16'h7F00;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/l_r_2_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n149_5 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/l_r [2])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/l_r_2_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/l_r_1_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n150_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/l_r [1])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/l_r_1_s1 .INIT=1'b0;
DFFS \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/l_r_0_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n151_5 ),
	.CLK(out_d_11[35]),
	.SET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/l_r [0])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/l_r_0_s1 .INIT=1'b1;
DFFS \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl_9_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n163_7 ),
	.CLK(out_d_11[35]),
	.SET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl [9])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl_9_s1 .INIT=1'b1;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl_8_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n164_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl [8])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl_8_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl_7_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n165_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl [7])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl_7_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl_6_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n166_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl [6])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl_6_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl_5_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n167_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl [5])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl_5_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl_4_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n168_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl [4])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl_4_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl_3_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n169_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl [3])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl_3_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl_2_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n170_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl [2])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl_2_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl_1_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n171_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl [1])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl_1_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl_0_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n172_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl [0])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/gpl_0_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r_9_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n217_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r [9])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r_9_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r_8_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n218_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r [8])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r_8_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r_7_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n219_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r [7])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r_7_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r_6_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n220_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r [6])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r_6_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r_5_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n221_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r [5])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r_5_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r_4_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n222_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r [4])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r_4_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r_3_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n223_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r [3])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r_3_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r_2_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n224_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r [2])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r_2_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r_1_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n225_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r [1])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r_1_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r_0_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n226_5 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r [0])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/g_r_0_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg_9_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n250_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg [9])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg_9_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg_8_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n251_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg [8])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg_8_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg_7_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n252_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg [7])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg_7_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg_6_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n253_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg [6])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg_6_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg_5_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n254_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg [5])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg_5_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg_4_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n255_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg [4])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg_4_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg_3_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n256_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg [3])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg_3_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg_2_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n257_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg [2])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg_2_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg_1_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n258_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg [1])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg_1_s1 .INIT=1'b0;
DFFS \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg_0_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n259_7 ),
	.CLK(out_d_11[35]),
	.SET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg [0])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/bpg_0_s1 .INIT=1'b1;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r_9_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n344_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r [9])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r_9_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r_8_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n345_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r [8])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r_8_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r_7_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n346_5 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r [7])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r_7_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r_6_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n347_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r [6])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r_6_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r_5_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n348_5 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r [5])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r_5_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r_4_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n349_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r [4])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r_4_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r_3_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n350_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r [3])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r_3_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r_2_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n351_5 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r [2])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r_2_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r_1_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n352_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r [1])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r_1_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r_0_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n353_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r [0])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/b_r_0_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/upwad_r_9_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n354_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/twBtfy_upwad [9])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/upwad_r_9_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/upwad_r_8_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n355_9 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/twBtfy_upwad [8])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/upwad_r_8_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/upwad_r_7_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n356_7 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/twBtfy_upwad [7])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/upwad_r_7_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/upwad_r_6_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n357_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/twBtfy_upwad [6])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/upwad_r_6_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/upwad_r_5_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n358_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/twBtfy_upwad [5])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/upwad_r_5_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/upwad_r_4_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n359_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/twBtfy_upwad [4])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/upwad_r_4_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/upwad_r_3_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n360_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/twBtfy_upwad [3])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/upwad_r_3_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/upwad_r_2_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n361_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/twBtfy_upwad [2])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/upwad_r_2_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/upwad_r_1_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n362_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/twBtfy_upwad [1])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/upwad_r_1_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/upwad_r_0_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n363_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/twBtfy_upwad [0])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/upwad_r_0_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/wspan_9_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n407_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/wspan [9])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/wspan_9_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/wspan_8_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n408_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/wspan [8])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/wspan_8_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/wspan_7_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n409_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/wspan [7])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/wspan_7_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/wspan_6_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n410_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/wspan [6])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/wspan_6_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/wspan_5_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n411_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/wspan [5])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/wspan_5_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/wspan_4_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n412_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/wspan [4])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/wspan_4_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/wspan_3_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n413_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/wspan [3])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/wspan_3_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/wspan_2_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n414_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/wspan [2])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/wspan_2_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/wspan_1_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n415_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/wspan [1])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/wspan_1_s1 .INIT=1'b0;
DFFS \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/wspan_0_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n416_7 ),
	.CLK(out_d_11[35]),
	.SET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/wspan [0])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/wspan_0_s1 .INIT=1'b1;
DFFS \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/tstep_9_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n440_7 ),
	.CLK(out_d_11[35]),
	.SET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/tstep [9])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/tstep_9_s1 .INIT=1'b1;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/tstep_8_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n441_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/tstep [8])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/tstep_8_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/tstep_7_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n442_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/tstep [7])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/tstep_7_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/tstep_6_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n443_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/tstep [6])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/tstep_6_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/tstep_5_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n444_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/tstep [5])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/tstep_5_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/tstep_4_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n445_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/tstep [4])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/tstep_4_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/tstep_3_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n446_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/tstep [3])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/tstep_3_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/tstep_2_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n447_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/tstep [2])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/tstep_2_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/tstep_1_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n448_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/tstep [1])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/tstep_1_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/tstep_0_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n449_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/tstep [0])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/tstep_0_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/twfad_r_9_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n492_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/tw_ad [9])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/twfad_r_9_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/twfad_r_8_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n493_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/tw_ad [8])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/twfad_r_8_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/twfad_r_7_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n494_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/tw_ad [7])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/twfad_r_7_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/twfad_r_6_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n495_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/tw_ad [6])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/twfad_r_6_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/twfad_r_5_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n496_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/tw_ad [5])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/twfad_r_5_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/twfad_r_4_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n497_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/tw_ad [4])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/twfad_r_4_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/twfad_r_3_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n498_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/tw_ad [3])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/twfad_r_3_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/twfad_r_2_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n499_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/tw_ad [2])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/twfad_r_2_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/twfad_r_1_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n500_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/tw_ad [1])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/twfad_r_1_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/twfad_r_0_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n501_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/tw_ad [0])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/twfad_r_0_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/l_r_3_s1  (
	.D(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n148_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/slfRst_1 ),
	.Q(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/l_r [3])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/l_r_3_s1 .INIT=1'b0;
ALU \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/dwnad_w_0_s0  (
	.I0(\myfft/fft_top_inst/twBtfy_upwad [0]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/wspan [0]),
	.I3(GND),
	.CIN(GND),
	.COUT(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/dwnad_w_0_3 ),
	.SUM(\myfft/fft_top_inst/tf_inst/twBtfy_dwnad [0])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/dwnad_w_0_s0 .ALU_MODE=0;
ALU \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/dwnad_w_1_s0  (
	.I0(\myfft/fft_top_inst/twBtfy_upwad [1]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/wspan [1]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/dwnad_w_0_3 ),
	.COUT(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/dwnad_w_1_3 ),
	.SUM(\myfft/fft_top_inst/tf_inst/twBtfy_dwnad [1])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/dwnad_w_1_s0 .ALU_MODE=0;
ALU \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/dwnad_w_2_s0  (
	.I0(\myfft/fft_top_inst/twBtfy_upwad [2]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/wspan [2]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/dwnad_w_1_3 ),
	.COUT(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/dwnad_w_2_3 ),
	.SUM(\myfft/fft_top_inst/tf_inst/twBtfy_dwnad [2])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/dwnad_w_2_s0 .ALU_MODE=0;
ALU \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/dwnad_w_3_s0  (
	.I0(\myfft/fft_top_inst/twBtfy_upwad [3]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/wspan [3]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/dwnad_w_2_3 ),
	.COUT(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/dwnad_w_3_3 ),
	.SUM(\myfft/fft_top_inst/tf_inst/twBtfy_dwnad [3])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/dwnad_w_3_s0 .ALU_MODE=0;
ALU \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/dwnad_w_4_s0  (
	.I0(\myfft/fft_top_inst/twBtfy_upwad [4]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/wspan [4]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/dwnad_w_3_3 ),
	.COUT(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/dwnad_w_4_3 ),
	.SUM(\myfft/fft_top_inst/tf_inst/twBtfy_dwnad [4])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/dwnad_w_4_s0 .ALU_MODE=0;
ALU \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/dwnad_w_5_s0  (
	.I0(\myfft/fft_top_inst/twBtfy_upwad [5]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/wspan [5]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/dwnad_w_4_3 ),
	.COUT(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/dwnad_w_5_3 ),
	.SUM(\myfft/fft_top_inst/tf_inst/twBtfy_dwnad [5])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/dwnad_w_5_s0 .ALU_MODE=0;
ALU \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/dwnad_w_6_s0  (
	.I0(\myfft/fft_top_inst/twBtfy_upwad [6]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/wspan [6]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/dwnad_w_5_3 ),
	.COUT(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/dwnad_w_6_3 ),
	.SUM(\myfft/fft_top_inst/tf_inst/twBtfy_dwnad [6])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/dwnad_w_6_s0 .ALU_MODE=0;
ALU \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/dwnad_w_7_s0  (
	.I0(\myfft/fft_top_inst/twBtfy_upwad [7]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/wspan [7]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/dwnad_w_6_3 ),
	.COUT(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/dwnad_w_7_3 ),
	.SUM(\myfft/fft_top_inst/tf_inst/twBtfy_dwnad [7])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/dwnad_w_7_s0 .ALU_MODE=0;
ALU \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/dwnad_w_8_s0  (
	.I0(\myfft/fft_top_inst/twBtfy_upwad [8]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/wspan [8]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/dwnad_w_7_3 ),
	.COUT(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/dwnad_w_8_3 ),
	.SUM(\myfft/fft_top_inst/tf_inst/twBtfy_dwnad [8])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/dwnad_w_8_s0 .ALU_MODE=0;
ALU \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/dwnad_w_9_s0  (
	.I0(\myfft/fft_top_inst/twBtfy_upwad [9]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/wspan [9]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/dwnad_w_8_3 ),
	.COUT(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/dwnad_w_9_1_COUT ),
	.SUM(\myfft/fft_top_inst/tf_inst/twBtfy_dwnad [9])
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/dwnad_w_9_s0 .ALU_MODE=0;
ALU \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n481_s  (
	.I0(\myfft/fft_top_inst/tf_inst/tw_ad [0]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/tstep [0]),
	.I3(GND),
	.CIN(GND),
	.COUT(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n481_2 ),
	.SUM(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n481_1 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n481_s .ALU_MODE=0;
ALU \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n480_s  (
	.I0(\myfft/fft_top_inst/tf_inst/tw_ad [1]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/tstep [1]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n481_2 ),
	.COUT(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n480_2 ),
	.SUM(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n480_1 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n480_s .ALU_MODE=0;
ALU \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n479_s  (
	.I0(\myfft/fft_top_inst/tf_inst/tw_ad [2]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/tstep [2]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n480_2 ),
	.COUT(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n479_2 ),
	.SUM(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n479_1 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n479_s .ALU_MODE=0;
ALU \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n478_s  (
	.I0(\myfft/fft_top_inst/tf_inst/tw_ad [3]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/tstep [3]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n479_2 ),
	.COUT(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n478_2 ),
	.SUM(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n478_1 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n478_s .ALU_MODE=0;
ALU \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n477_s  (
	.I0(\myfft/fft_top_inst/tf_inst/tw_ad [4]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/tstep [4]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n478_2 ),
	.COUT(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n477_2 ),
	.SUM(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n477_1 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n477_s .ALU_MODE=0;
ALU \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n476_s  (
	.I0(\myfft/fft_top_inst/tf_inst/tw_ad [5]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/tstep [5]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n477_2 ),
	.COUT(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n476_2 ),
	.SUM(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n476_1 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n476_s .ALU_MODE=0;
ALU \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n475_s  (
	.I0(\myfft/fft_top_inst/tf_inst/tw_ad [6]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/tstep [6]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n476_2 ),
	.COUT(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n475_2 ),
	.SUM(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n475_1 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n475_s .ALU_MODE=0;
ALU \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n474_s  (
	.I0(\myfft/fft_top_inst/tf_inst/tw_ad [7]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/tstep [7]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n475_2 ),
	.COUT(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n474_2 ),
	.SUM(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n474_1 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n474_s .ALU_MODE=0;
ALU \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n473_s  (
	.I0(\myfft/fft_top_inst/tf_inst/tw_ad [8]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/tstep [8]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n474_2 ),
	.COUT(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n473_2 ),
	.SUM(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n473_1 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n473_s .ALU_MODE=0;
ALU \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n472_s  (
	.I0(\myfft/fft_top_inst/tf_inst/tw_ad [9]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/tstep [9]),
	.I3(GND),
	.CIN(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n473_2 ),
	.COUT(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n472_0_COUT ),
	.SUM(\myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n472_1 )
);
defparam \myfft/fft_top_inst/tf_inst/twBtfyParm_inst/n472_s .ALU_MODE=0;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/isBS2_s1  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/isBS2_5 ),
	.I1(\myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt [2]),
	.I2(\myfft/fft_top_inst/tf_inst/ctrl_inst/isBS0_5 ),
	.I3(\myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt [9]),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/isBS2 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/isBS2_s1 .INIT=16'h0007;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/isLE_s1  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/lvl [0]),
	.I1(\myfft/fft_top_inst/tf_inst/ctrl_inst/lvl [2]),
	.I2(\myfft/fft_top_inst/tf_inst/ctrl_inst/lvl [1]),
	.I3(\myfft/fft_top_inst/tf_inst/ctrl_inst/lvl [3]),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/isLE )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/isLE_s1 .INIT=16'h1000;
LUT3 \myfft/fft_top_inst/tf_inst/ctrl_inst/slfRst_s1  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/isLE ),
	.I1(\myfft/fft_top_inst/tf_inst/ctrl_inst/lcRst ),
	.I2(\myfft/fft_top_inst/tf_inst/slfRst_3 ),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/slfRst_2 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/slfRst_s1 .INIT=8'hF8;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/n53_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/n53_8 ),
	.I1(\myfft/fft_top_inst/tf_inst/ctrl_inst/n53_5 ),
	.I2(\myfft/fft_top_inst/tf_inst/ctrl_inst/n53_6 ),
	.I3(\myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt [9]),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/n53_3 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/n53_s0 .INIT=16'h0708;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/n54_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/n53_5 ),
	.I1(\myfft/fft_top_inst/tf_inst/ctrl_inst/n54_6 ),
	.I2(\myfft/fft_top_inst/tf_inst/ctrl_inst/n53_6 ),
	.I3(\myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt [8]),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/n54_3 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/n54_s0 .INIT=16'h0708;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/n55_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/n55_4 ),
	.I1(\myfft/fft_top_inst/tf_inst/ctrl_inst/n53_5 ),
	.I2(\myfft/fft_top_inst/tf_inst/ctrl_inst/n53_6 ),
	.I3(\myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt [7]),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/n55_3 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/n55_s0 .INIT=16'h0708;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/n56_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt [5]),
	.I1(\myfft/fft_top_inst/tf_inst/ctrl_inst/n53_5 ),
	.I2(\myfft/fft_top_inst/tf_inst/ctrl_inst/n53_6 ),
	.I3(\myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt [6]),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/n56_3 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/n56_s0 .INIT=16'h0708;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/n58_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt [3]),
	.I1(\myfft/fft_top_inst/tf_inst/ctrl_inst/n58_6 ),
	.I2(\myfft/fft_top_inst/tf_inst/ctrl_inst/n53_6 ),
	.I3(\myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt [4]),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/n58_3 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/n58_s0 .INIT=16'h0708;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/n60_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/lcRst ),
	.I1(\myfft/fft_top_inst/ftCtl_ea ),
	.I2(\myfft/fft_top_inst/tf_inst/ctrl_inst/n60_4 ),
	.I3(\myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt [2]),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/n60_3 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/n60_s0 .INIT=16'h3740;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/n61_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/lcRst ),
	.I1(\myfft/fft_top_inst/ftCtl_ea ),
	.I2(\myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt [0]),
	.I3(\myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt [1]),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/n61_3 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/n61_s0 .INIT=16'h3740;
LUT3 \myfft/fft_top_inst/tf_inst/ctrl_inst/n62_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/lcRst ),
	.I1(\myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt [0]),
	.I2(\myfft/fft_top_inst/ftCtl_ea ),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/n62_3 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/n62_s0 .INIT=8'h1C;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/n95_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/lvl [2]),
	.I1(\myfft/fft_top_inst/tf_inst/ctrl_inst/n53_6 ),
	.I2(\myfft/fft_top_inst/tf_inst/ctrl_inst/n95_4 ),
	.I3(\myfft/fft_top_inst/tf_inst/ctrl_inst/lvl [3]),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/n95_3 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/n95_s0 .INIT=16'h7F80;
LUT3 \myfft/fft_top_inst/tf_inst/ctrl_inst/n111_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_1 [9]),
	.I1(\myfft/fft_top_inst/twBtfy_upwad [9]),
	.I2(\myfft/fft_top_inst/ftCtl_ea ),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/n111_3 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/n111_s0 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/ctrl_inst/n112_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_1 [8]),
	.I1(\myfft/fft_top_inst/twBtfy_upwad [8]),
	.I2(\myfft/fft_top_inst/ftCtl_ea ),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/n112_3 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/n112_s0 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/ctrl_inst/n113_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_1 [7]),
	.I1(\myfft/fft_top_inst/twBtfy_upwad [7]),
	.I2(\myfft/fft_top_inst/ftCtl_ea ),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/n113_3 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/n113_s0 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/ctrl_inst/n114_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_1 [6]),
	.I1(\myfft/fft_top_inst/twBtfy_upwad [6]),
	.I2(\myfft/fft_top_inst/ftCtl_ea ),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/n114_3 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/n114_s0 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/ctrl_inst/n115_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_1 [5]),
	.I1(\myfft/fft_top_inst/twBtfy_upwad [5]),
	.I2(\myfft/fft_top_inst/ftCtl_ea ),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/n115_3 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/n115_s0 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/ctrl_inst/n116_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_1 [4]),
	.I1(\myfft/fft_top_inst/twBtfy_upwad [4]),
	.I2(\myfft/fft_top_inst/ftCtl_ea ),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/n116_3 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/n116_s0 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/ctrl_inst/n117_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_1 [3]),
	.I1(\myfft/fft_top_inst/twBtfy_upwad [3]),
	.I2(\myfft/fft_top_inst/ftCtl_ea ),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/n117_3 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/n117_s0 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/ctrl_inst/n118_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_1 [2]),
	.I1(\myfft/fft_top_inst/twBtfy_upwad [2]),
	.I2(\myfft/fft_top_inst/ftCtl_ea ),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/n118_3 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/n118_s0 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/ctrl_inst/n119_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_1 [1]),
	.I1(\myfft/fft_top_inst/twBtfy_upwad [1]),
	.I2(\myfft/fft_top_inst/ftCtl_ea ),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/n119_3 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/n119_s0 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/ctrl_inst/n120_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_1 [0]),
	.I1(\myfft/fft_top_inst/twBtfy_upwad [0]),
	.I2(\myfft/fft_top_inst/ftCtl_ea ),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/n120_3 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/n120_s0 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/ctrl_inst/n121_s0  (
	.I0(\myfft/fft_top_inst/cclt_upwad_2 [9]),
	.I1(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_1 [9]),
	.I2(\myfft/fft_top_inst/ftCtl_ea ),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/n121_3 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/n121_s0 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/ctrl_inst/n122_s0  (
	.I0(\myfft/fft_top_inst/cclt_upwad_2 [8]),
	.I1(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_1 [8]),
	.I2(\myfft/fft_top_inst/ftCtl_ea ),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/n122_3 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/n122_s0 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/ctrl_inst/n123_s0  (
	.I0(\myfft/fft_top_inst/cclt_upwad_2 [7]),
	.I1(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_1 [7]),
	.I2(\myfft/fft_top_inst/ftCtl_ea ),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/n123_3 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/n123_s0 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/ctrl_inst/n124_s0  (
	.I0(\myfft/fft_top_inst/cclt_upwad_2 [6]),
	.I1(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_1 [6]),
	.I2(\myfft/fft_top_inst/ftCtl_ea ),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/n124_3 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/n124_s0 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/ctrl_inst/n125_s0  (
	.I0(\myfft/fft_top_inst/cclt_upwad_2 [5]),
	.I1(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_1 [5]),
	.I2(\myfft/fft_top_inst/ftCtl_ea ),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/n125_3 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/n125_s0 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/ctrl_inst/n126_s0  (
	.I0(\myfft/fft_top_inst/cclt_upwad_2 [4]),
	.I1(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_1 [4]),
	.I2(\myfft/fft_top_inst/ftCtl_ea ),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/n126_3 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/n126_s0 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/ctrl_inst/n127_s0  (
	.I0(\myfft/fft_top_inst/cclt_upwad_2 [3]),
	.I1(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_1 [3]),
	.I2(\myfft/fft_top_inst/ftCtl_ea ),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/n127_3 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/n127_s0 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/ctrl_inst/n128_s0  (
	.I0(\myfft/fft_top_inst/cclt_upwad_2 [2]),
	.I1(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_1 [2]),
	.I2(\myfft/fft_top_inst/ftCtl_ea ),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/n128_3 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/n128_s0 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/ctrl_inst/n129_s0  (
	.I0(\myfft/fft_top_inst/cclt_upwad_2 [1]),
	.I1(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_1 [1]),
	.I2(\myfft/fft_top_inst/ftCtl_ea ),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/n129_3 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/n129_s0 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/ctrl_inst/n130_s0  (
	.I0(\myfft/fft_top_inst/cclt_upwad_2 [0]),
	.I1(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_1 [0]),
	.I2(\myfft/fft_top_inst/ftCtl_ea ),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/n130_3 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/n130_s0 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/ctrl_inst/n131_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_1 [9]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfy_dwnad [9]),
	.I2(\myfft/fft_top_inst/ftCtl_ea ),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/n131_3 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/n131_s0 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/ctrl_inst/n132_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_1 [8]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfy_dwnad [8]),
	.I2(\myfft/fft_top_inst/ftCtl_ea ),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/n132_3 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/n132_s0 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/ctrl_inst/n133_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_1 [7]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfy_dwnad [7]),
	.I2(\myfft/fft_top_inst/ftCtl_ea ),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/n133_3 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/n133_s0 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/ctrl_inst/n134_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_1 [6]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfy_dwnad [6]),
	.I2(\myfft/fft_top_inst/ftCtl_ea ),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/n134_3 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/n134_s0 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/ctrl_inst/n135_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_1 [5]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfy_dwnad [5]),
	.I2(\myfft/fft_top_inst/ftCtl_ea ),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/n135_3 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/n135_s0 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/ctrl_inst/n136_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_1 [4]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfy_dwnad [4]),
	.I2(\myfft/fft_top_inst/ftCtl_ea ),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/n136_3 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/n136_s0 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/ctrl_inst/n137_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_1 [3]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfy_dwnad [3]),
	.I2(\myfft/fft_top_inst/ftCtl_ea ),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/n137_3 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/n137_s0 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/ctrl_inst/n138_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_1 [2]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfy_dwnad [2]),
	.I2(\myfft/fft_top_inst/ftCtl_ea ),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/n138_3 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/n138_s0 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/ctrl_inst/n139_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_1 [1]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfy_dwnad [1]),
	.I2(\myfft/fft_top_inst/ftCtl_ea ),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/n139_3 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/n139_s0 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/ctrl_inst/n140_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_1 [0]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfy_dwnad [0]),
	.I2(\myfft/fft_top_inst/ftCtl_ea ),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/n140_3 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/n140_s0 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/ctrl_inst/n141_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_2 [9]),
	.I1(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_1 [9]),
	.I2(\myfft/fft_top_inst/ftCtl_ea ),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/n141_3 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/n141_s0 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/ctrl_inst/n142_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_2 [8]),
	.I1(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_1 [8]),
	.I2(\myfft/fft_top_inst/ftCtl_ea ),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/n142_3 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/n142_s0 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/ctrl_inst/n143_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_2 [7]),
	.I1(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_1 [7]),
	.I2(\myfft/fft_top_inst/ftCtl_ea ),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/n143_3 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/n143_s0 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/ctrl_inst/n144_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_2 [6]),
	.I1(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_1 [6]),
	.I2(\myfft/fft_top_inst/ftCtl_ea ),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/n144_3 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/n144_s0 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/ctrl_inst/n145_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_2 [5]),
	.I1(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_1 [5]),
	.I2(\myfft/fft_top_inst/ftCtl_ea ),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/n145_3 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/n145_s0 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/ctrl_inst/n146_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_2 [4]),
	.I1(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_1 [4]),
	.I2(\myfft/fft_top_inst/ftCtl_ea ),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/n146_3 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/n146_s0 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/ctrl_inst/n147_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_2 [3]),
	.I1(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_1 [3]),
	.I2(\myfft/fft_top_inst/ftCtl_ea ),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/n147_3 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/n147_s0 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/ctrl_inst/n148_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_2 [2]),
	.I1(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_1 [2]),
	.I2(\myfft/fft_top_inst/ftCtl_ea ),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/n148_3 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/n148_s0 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/ctrl_inst/n149_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_2 [1]),
	.I1(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_1 [1]),
	.I2(\myfft/fft_top_inst/ftCtl_ea ),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/n149_3 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/n149_s0 .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/ctrl_inst/n150_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_2 [0]),
	.I1(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_1 [0]),
	.I2(\myfft/fft_top_inst/ftCtl_ea ),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/n150_3 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/n150_s0 .INIT=8'hCA;
LUT2 \myfft/fft_top_inst/tf_inst/ctrl_inst/corePa_wre_s  (
	.I0(\myfft/fft_top_inst/ftCtl_ea ),
	.I1(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/corePa_wre )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/corePa_wre_s .INIT=4'h8;
LUT2 \myfft/fft_top_inst/tf_inst/ctrl_inst/coreQa_wre_s  (
	.I0(\myfft/fft_top_inst/dSrc ),
	.I1(\myfft/fft_top_inst/ftCtl_ea ),
	.F(\myfft/fft_top_inst/qxnb_wre )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/coreQa_wre_s .INIT=4'h4;
LUT3 \myfft/fft_top_inst/tf_inst/ctrl_inst/corePb_ad_9_s  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfy_dwnad [9]),
	.I1(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_2 [9]),
	.I2(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/corePb_ad [9])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/corePb_ad_9_s .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/ctrl_inst/corePb_ad_8_s  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfy_dwnad [8]),
	.I1(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_2 [8]),
	.I2(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/corePb_ad [8])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/corePb_ad_8_s .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/ctrl_inst/corePb_ad_7_s  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfy_dwnad [7]),
	.I1(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_2 [7]),
	.I2(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/corePb_ad [7])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/corePb_ad_7_s .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/ctrl_inst/corePb_ad_6_s  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfy_dwnad [6]),
	.I1(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_2 [6]),
	.I2(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/corePb_ad [6])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/corePb_ad_6_s .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/ctrl_inst/corePb_ad_5_s  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfy_dwnad [5]),
	.I1(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_2 [5]),
	.I2(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/corePb_ad [5])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/corePb_ad_5_s .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/ctrl_inst/corePb_ad_4_s  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfy_dwnad [4]),
	.I1(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_2 [4]),
	.I2(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/corePb_ad [4])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/corePb_ad_4_s .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/ctrl_inst/corePb_ad_3_s  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfy_dwnad [3]),
	.I1(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_2 [3]),
	.I2(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/corePb_ad [3])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/corePb_ad_3_s .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/ctrl_inst/corePb_ad_2_s  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfy_dwnad [2]),
	.I1(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_2 [2]),
	.I2(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/corePb_ad [2])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/corePb_ad_2_s .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/ctrl_inst/corePb_ad_1_s  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfy_dwnad [1]),
	.I1(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_2 [1]),
	.I2(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/corePb_ad [1])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/corePb_ad_1_s .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/ctrl_inst/corePb_ad_0_s  (
	.I0(\myfft/fft_top_inst/tf_inst/twBtfy_dwnad [0]),
	.I1(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_2 [0]),
	.I2(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/corePb_ad [0])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/corePb_ad_0_s .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/ctrl_inst/coreQb_ad_9_s  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_2 [9]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfy_dwnad [9]),
	.I2(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/qxnb_ad [9])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/coreQb_ad_9_s .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/ctrl_inst/coreQb_ad_8_s  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_2 [8]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfy_dwnad [8]),
	.I2(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/qxnb_ad [8])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/coreQb_ad_8_s .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/ctrl_inst/coreQb_ad_7_s  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_2 [7]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfy_dwnad [7]),
	.I2(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/qxnb_ad [7])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/coreQb_ad_7_s .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/ctrl_inst/coreQb_ad_6_s  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_2 [6]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfy_dwnad [6]),
	.I2(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/qxnb_ad [6])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/coreQb_ad_6_s .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/ctrl_inst/coreQb_ad_5_s  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_2 [5]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfy_dwnad [5]),
	.I2(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/qxnb_ad [5])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/coreQb_ad_5_s .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/ctrl_inst/coreQb_ad_4_s  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_2 [4]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfy_dwnad [4]),
	.I2(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/qxnb_ad [4])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/coreQb_ad_4_s .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/ctrl_inst/coreQb_ad_3_s  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_2 [3]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfy_dwnad [3]),
	.I2(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/qxnb_ad [3])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/coreQb_ad_3_s .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/ctrl_inst/coreQb_ad_2_s  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_2 [2]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfy_dwnad [2]),
	.I2(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/qxnb_ad [2])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/coreQb_ad_2_s .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/ctrl_inst/coreQb_ad_1_s  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_2 [1]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfy_dwnad [1]),
	.I2(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/qxnb_ad [1])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/coreQb_ad_1_s .INIT=8'hCA;
LUT3 \myfft/fft_top_inst/tf_inst/ctrl_inst/coreQb_ad_0_s  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_2 [0]),
	.I1(\myfft/fft_top_inst/tf_inst/twBtfy_dwnad [0]),
	.I2(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/qxnb_ad [0])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/coreQb_ad_0_s .INIT=8'hCA;
LUT2 \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_ea_Z_s  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/isBS0 ),
	.I1(\myfft/fft_top_inst/ftCtl_ea ),
	.F(\myfft/fft_top_inst/tf_inst/btfy_ea )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_ea_Z_s .INIT=4'h4;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnare_15_s  (
	.I0(\myfft/fft_top_inst/pxna_do_re_b [15]),
	.I1(\myfft/fft_top_inst/qxna_do_re_b [15]),
	.I2(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I3(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/tf_inst/btfy_xnare [15])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnare_15_s .INIT=16'h0C0A;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnare_14_s  (
	.I0(\myfft/fft_top_inst/pxna_do_re_b [14]),
	.I1(\myfft/fft_top_inst/qxna_do_re_b [14]),
	.I2(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I3(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/tf_inst/btfy_xnare [14])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnare_14_s .INIT=16'h0C0A;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnare_13_s  (
	.I0(\myfft/fft_top_inst/pxna_do_re_b [13]),
	.I1(\myfft/fft_top_inst/qxna_do_re_b [13]),
	.I2(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I3(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/tf_inst/btfy_xnare [13])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnare_13_s .INIT=16'h0C0A;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnare_12_s  (
	.I0(\myfft/fft_top_inst/pxna_do_re_b [12]),
	.I1(\myfft/fft_top_inst/qxna_do_re_b [12]),
	.I2(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I3(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/tf_inst/btfy_xnare [12])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnare_12_s .INIT=16'h0C0A;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnare_11_s  (
	.I0(\myfft/fft_top_inst/pxna_do_re_b [11]),
	.I1(\myfft/fft_top_inst/qxna_do_re_b [11]),
	.I2(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I3(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/tf_inst/btfy_xnare [11])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnare_11_s .INIT=16'h0C0A;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnare_10_s  (
	.I0(\myfft/fft_top_inst/pxna_do_re_b [10]),
	.I1(\myfft/fft_top_inst/qxna_do_re_b [10]),
	.I2(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I3(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/tf_inst/btfy_xnare [10])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnare_10_s .INIT=16'h0C0A;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnare_9_s  (
	.I0(\myfft/fft_top_inst/pxna_do_re_b [9]),
	.I1(\myfft/fft_top_inst/qxna_do_re_b [9]),
	.I2(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I3(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/tf_inst/btfy_xnare [9])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnare_9_s .INIT=16'h0C0A;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnare_8_s  (
	.I0(\myfft/fft_top_inst/pxna_do_re_b [8]),
	.I1(\myfft/fft_top_inst/qxna_do_re_b [8]),
	.I2(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I3(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/tf_inst/btfy_xnare [8])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnare_8_s .INIT=16'h0C0A;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnare_7_s  (
	.I0(\myfft/fft_top_inst/pxna_do_re_b [7]),
	.I1(\myfft/fft_top_inst/qxna_do_re_b [7]),
	.I2(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I3(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/tf_inst/btfy_xnare [7])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnare_7_s .INIT=16'h0C0A;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnare_6_s  (
	.I0(\myfft/fft_top_inst/pxna_do_re_b [6]),
	.I1(\myfft/fft_top_inst/qxna_do_re_b [6]),
	.I2(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I3(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/tf_inst/btfy_xnare [6])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnare_6_s .INIT=16'h0C0A;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnare_5_s  (
	.I0(\myfft/fft_top_inst/pxna_do_re_b [5]),
	.I1(\myfft/fft_top_inst/qxna_do_re_b [5]),
	.I2(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I3(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/tf_inst/btfy_xnare [5])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnare_5_s .INIT=16'h0C0A;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnare_4_s  (
	.I0(\myfft/fft_top_inst/pxna_do_re_b [4]),
	.I1(\myfft/fft_top_inst/qxna_do_re_b [4]),
	.I2(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I3(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/tf_inst/btfy_xnare [4])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnare_4_s .INIT=16'h0C0A;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnare_3_s  (
	.I0(\myfft/fft_top_inst/pxna_do_re_b [3]),
	.I1(\myfft/fft_top_inst/qxna_do_re_b [3]),
	.I2(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I3(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/tf_inst/btfy_xnare [3])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnare_3_s .INIT=16'h0C0A;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnare_2_s  (
	.I0(\myfft/fft_top_inst/pxna_do_re_b [2]),
	.I1(\myfft/fft_top_inst/qxna_do_re_b [2]),
	.I2(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I3(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/tf_inst/btfy_xnare [2])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnare_2_s .INIT=16'h0C0A;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnare_1_s  (
	.I0(\myfft/fft_top_inst/pxna_do_re_b [1]),
	.I1(\myfft/fft_top_inst/qxna_do_re_b [1]),
	.I2(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I3(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/tf_inst/btfy_xnare [1])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnare_1_s .INIT=16'h0C0A;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnare_0_s  (
	.I0(\myfft/fft_top_inst/pxna_do_re_b [0]),
	.I1(\myfft/fft_top_inst/qxna_do_re_b [0]),
	.I2(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I3(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/tf_inst/btfy_xnare [0])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnare_0_s .INIT=16'h0C0A;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnaim_15_s  (
	.I0(\myfft/fft_top_inst/pxna_do_im_b [15]),
	.I1(\myfft/fft_top_inst/qxna_do_im_b [15]),
	.I2(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I3(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/tf_inst/btfy_xnaim [15])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnaim_15_s .INIT=16'h0C0A;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnaim_14_s  (
	.I0(\myfft/fft_top_inst/pxna_do_im_b [14]),
	.I1(\myfft/fft_top_inst/qxna_do_im_b [14]),
	.I2(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I3(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/tf_inst/btfy_xnaim [14])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnaim_14_s .INIT=16'h0C0A;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnaim_13_s  (
	.I0(\myfft/fft_top_inst/pxna_do_im_b [13]),
	.I1(\myfft/fft_top_inst/qxna_do_im_b [13]),
	.I2(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I3(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/tf_inst/btfy_xnaim [13])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnaim_13_s .INIT=16'h0C0A;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnaim_12_s  (
	.I0(\myfft/fft_top_inst/pxna_do_im_b [12]),
	.I1(\myfft/fft_top_inst/qxna_do_im_b [12]),
	.I2(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I3(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/tf_inst/btfy_xnaim [12])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnaim_12_s .INIT=16'h0C0A;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnaim_11_s  (
	.I0(\myfft/fft_top_inst/pxna_do_im_b [11]),
	.I1(\myfft/fft_top_inst/qxna_do_im_b [11]),
	.I2(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I3(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/tf_inst/btfy_xnaim [11])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnaim_11_s .INIT=16'h0C0A;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnaim_10_s  (
	.I0(\myfft/fft_top_inst/pxna_do_im_b [10]),
	.I1(\myfft/fft_top_inst/qxna_do_im_b [10]),
	.I2(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I3(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/tf_inst/btfy_xnaim [10])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnaim_10_s .INIT=16'h0C0A;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnaim_9_s  (
	.I0(\myfft/fft_top_inst/pxna_do_im_b [9]),
	.I1(\myfft/fft_top_inst/qxna_do_im_b [9]),
	.I2(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I3(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/tf_inst/btfy_xnaim [9])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnaim_9_s .INIT=16'h0C0A;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnaim_8_s  (
	.I0(\myfft/fft_top_inst/pxna_do_im_b [8]),
	.I1(\myfft/fft_top_inst/qxna_do_im_b [8]),
	.I2(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I3(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/tf_inst/btfy_xnaim [8])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnaim_8_s .INIT=16'h0C0A;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnaim_7_s  (
	.I0(\myfft/fft_top_inst/pxna_do_im_b [7]),
	.I1(\myfft/fft_top_inst/qxna_do_im_b [7]),
	.I2(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I3(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/tf_inst/btfy_xnaim [7])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnaim_7_s .INIT=16'h0C0A;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnaim_6_s  (
	.I0(\myfft/fft_top_inst/pxna_do_im_b [6]),
	.I1(\myfft/fft_top_inst/qxna_do_im_b [6]),
	.I2(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I3(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/tf_inst/btfy_xnaim [6])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnaim_6_s .INIT=16'h0C0A;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnaim_5_s  (
	.I0(\myfft/fft_top_inst/pxna_do_im_b [5]),
	.I1(\myfft/fft_top_inst/qxna_do_im_b [5]),
	.I2(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I3(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/tf_inst/btfy_xnaim [5])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnaim_5_s .INIT=16'h0C0A;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnaim_4_s  (
	.I0(\myfft/fft_top_inst/pxna_do_im_b [4]),
	.I1(\myfft/fft_top_inst/qxna_do_im_b [4]),
	.I2(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I3(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/tf_inst/btfy_xnaim [4])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnaim_4_s .INIT=16'h0C0A;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnaim_3_s  (
	.I0(\myfft/fft_top_inst/pxna_do_im_b [3]),
	.I1(\myfft/fft_top_inst/qxna_do_im_b [3]),
	.I2(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I3(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/tf_inst/btfy_xnaim [3])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnaim_3_s .INIT=16'h0C0A;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnaim_2_s  (
	.I0(\myfft/fft_top_inst/pxna_do_im_b [2]),
	.I1(\myfft/fft_top_inst/qxna_do_im_b [2]),
	.I2(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I3(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/tf_inst/btfy_xnaim [2])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnaim_2_s .INIT=16'h0C0A;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnaim_1_s  (
	.I0(\myfft/fft_top_inst/pxna_do_im_b [1]),
	.I1(\myfft/fft_top_inst/qxna_do_im_b [1]),
	.I2(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I3(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/tf_inst/btfy_xnaim [1])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnaim_1_s .INIT=16'h0C0A;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnaim_0_s  (
	.I0(\myfft/fft_top_inst/pxna_do_im_b [0]),
	.I1(\myfft/fft_top_inst/qxna_do_im_b [0]),
	.I2(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I3(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/tf_inst/btfy_xnaim [0])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnaim_0_s .INIT=16'h0C0A;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnbre_15_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/pxnb_do_re_b [15]),
	.I2(\myfft/fft_top_inst/coreQb_do_re [15]),
	.I3(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/tf_inst/btfy_xnbre [15])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnbre_15_s .INIT=16'hF044;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnbre_14_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/pxnb_do_re_b [14]),
	.I2(\myfft/fft_top_inst/coreQb_do_re [14]),
	.I3(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/tf_inst/btfy_xnbre [14])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnbre_14_s .INIT=16'hF044;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnbre_13_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/pxnb_do_re_b [13]),
	.I2(\myfft/fft_top_inst/coreQb_do_re [13]),
	.I3(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/tf_inst/btfy_xnbre [13])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnbre_13_s .INIT=16'hF044;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnbre_12_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/pxnb_do_re_b [12]),
	.I2(\myfft/fft_top_inst/coreQb_do_re [12]),
	.I3(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/tf_inst/btfy_xnbre [12])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnbre_12_s .INIT=16'hF044;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnbre_11_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/pxnb_do_re_b [11]),
	.I2(\myfft/fft_top_inst/coreQb_do_re [11]),
	.I3(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/tf_inst/btfy_xnbre [11])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnbre_11_s .INIT=16'hF044;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnbre_10_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/pxnb_do_re_b [10]),
	.I2(\myfft/fft_top_inst/coreQb_do_re [10]),
	.I3(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/tf_inst/btfy_xnbre [10])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnbre_10_s .INIT=16'hF044;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnbre_9_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/pxnb_do_re_b [9]),
	.I2(\myfft/fft_top_inst/coreQb_do_re [9]),
	.I3(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/tf_inst/btfy_xnbre [9])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnbre_9_s .INIT=16'hF044;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnbre_8_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/pxnb_do_re_b [8]),
	.I2(\myfft/fft_top_inst/coreQb_do_re [8]),
	.I3(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/tf_inst/btfy_xnbre [8])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnbre_8_s .INIT=16'hF044;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnbre_7_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/pxnb_do_re_b [7]),
	.I2(\myfft/fft_top_inst/coreQb_do_re [7]),
	.I3(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/tf_inst/btfy_xnbre [7])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnbre_7_s .INIT=16'hF044;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnbre_6_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/pxnb_do_re_b [6]),
	.I2(\myfft/fft_top_inst/coreQb_do_re [6]),
	.I3(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/tf_inst/btfy_xnbre [6])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnbre_6_s .INIT=16'hF044;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnbre_5_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/pxnb_do_re_b [5]),
	.I2(\myfft/fft_top_inst/coreQb_do_re [5]),
	.I3(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/tf_inst/btfy_xnbre [5])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnbre_5_s .INIT=16'hF044;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnbre_4_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/pxnb_do_re_b [4]),
	.I2(\myfft/fft_top_inst/coreQb_do_re [4]),
	.I3(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/tf_inst/btfy_xnbre [4])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnbre_4_s .INIT=16'hF044;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnbre_3_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/pxnb_do_re_b [3]),
	.I2(\myfft/fft_top_inst/coreQb_do_re [3]),
	.I3(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/tf_inst/btfy_xnbre [3])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnbre_3_s .INIT=16'hF044;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnbre_2_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/pxnb_do_re_b [2]),
	.I2(\myfft/fft_top_inst/coreQb_do_re [2]),
	.I3(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/tf_inst/btfy_xnbre [2])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnbre_2_s .INIT=16'hF044;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnbre_1_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/pxnb_do_re_b [1]),
	.I2(\myfft/fft_top_inst/coreQb_do_re [1]),
	.I3(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/tf_inst/btfy_xnbre [1])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnbre_1_s .INIT=16'hF044;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnbre_0_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/pxnb_do_re_b [0]),
	.I2(\myfft/fft_top_inst/coreQb_do_re [0]),
	.I3(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/tf_inst/btfy_xnbre [0])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnbre_0_s .INIT=16'hF044;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnbim_15_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/pxnb_do_im_b [15]),
	.I2(\myfft/fft_top_inst/coreQb_do_im [15]),
	.I3(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/tf_inst/btfy_xnbim [15])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnbim_15_s .INIT=16'hF044;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnbim_14_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/pxnb_do_im_b [14]),
	.I2(\myfft/fft_top_inst/coreQb_do_im [14]),
	.I3(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/tf_inst/btfy_xnbim [14])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnbim_14_s .INIT=16'hF044;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnbim_13_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/pxnb_do_im_b [13]),
	.I2(\myfft/fft_top_inst/coreQb_do_im [13]),
	.I3(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/tf_inst/btfy_xnbim [13])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnbim_13_s .INIT=16'hF044;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnbim_12_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/pxnb_do_im_b [12]),
	.I2(\myfft/fft_top_inst/coreQb_do_im [12]),
	.I3(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/tf_inst/btfy_xnbim [12])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnbim_12_s .INIT=16'hF044;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnbim_11_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/pxnb_do_im_b [11]),
	.I2(\myfft/fft_top_inst/coreQb_do_im [11]),
	.I3(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/tf_inst/btfy_xnbim [11])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnbim_11_s .INIT=16'hF044;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnbim_10_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/pxnb_do_im_b [10]),
	.I2(\myfft/fft_top_inst/coreQb_do_im [10]),
	.I3(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/tf_inst/btfy_xnbim [10])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnbim_10_s .INIT=16'hF044;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnbim_9_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/pxnb_do_im_b [9]),
	.I2(\myfft/fft_top_inst/coreQb_do_im [9]),
	.I3(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/tf_inst/btfy_xnbim [9])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnbim_9_s .INIT=16'hF044;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnbim_8_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/pxnb_do_im_b [8]),
	.I2(\myfft/fft_top_inst/coreQb_do_im [8]),
	.I3(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/tf_inst/btfy_xnbim [8])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnbim_8_s .INIT=16'hF044;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnbim_7_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/pxnb_do_im_b [7]),
	.I2(\myfft/fft_top_inst/coreQb_do_im [7]),
	.I3(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/tf_inst/btfy_xnbim [7])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnbim_7_s .INIT=16'hF044;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnbim_6_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/pxnb_do_im_b [6]),
	.I2(\myfft/fft_top_inst/coreQb_do_im [6]),
	.I3(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/tf_inst/btfy_xnbim [6])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnbim_6_s .INIT=16'hF044;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnbim_5_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/pxnb_do_im_b [5]),
	.I2(\myfft/fft_top_inst/coreQb_do_im [5]),
	.I3(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/tf_inst/btfy_xnbim [5])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnbim_5_s .INIT=16'hF044;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnbim_4_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/pxnb_do_im_b [4]),
	.I2(\myfft/fft_top_inst/coreQb_do_im [4]),
	.I3(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/tf_inst/btfy_xnbim [4])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnbim_4_s .INIT=16'hF044;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnbim_3_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/pxnb_do_im_b [3]),
	.I2(\myfft/fft_top_inst/coreQb_do_im [3]),
	.I3(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/tf_inst/btfy_xnbim [3])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnbim_3_s .INIT=16'hF044;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnbim_2_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/pxnb_do_im_b [2]),
	.I2(\myfft/fft_top_inst/coreQb_do_im [2]),
	.I3(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/tf_inst/btfy_xnbim [2])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnbim_2_s .INIT=16'hF044;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnbim_1_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/pxnb_do_im_b [1]),
	.I2(\myfft/fft_top_inst/coreQb_do_im [1]),
	.I3(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/tf_inst/btfy_xnbim [1])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnbim_1_s .INIT=16'hF044;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnbim_0_s  (
	.I0(\myfft/fft_top_inst/qxnb_do_re_0_40 ),
	.I1(\myfft/fft_top_inst/pxnb_do_im_b [0]),
	.I2(\myfft/fft_top_inst/coreQb_do_im [0]),
	.I3(\myfft/fft_top_inst/dSrc ),
	.F(\myfft/fft_top_inst/tf_inst/btfy_xnbim [0])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_xnbim_0_s .INIT=16'hF044;
LUT2 \myfft/fft_top_inst/tf_inst/ctrl_inst/twBtfy_ea_s  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/twBtfy_ea_3 ),
	.I1(\myfft/fft_top_inst/ftCtl_ea ),
	.F(\myfft/fft_top_inst/tf_inst/twBtfy_ea )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/twBtfy_ea_s .INIT=4'h4;
LUT2 \myfft/fft_top_inst/tf_inst/ctrl_inst/fft_ok_s1  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/isLE ),
	.I1(\myfft/fft_top_inst/tf_inst/ctrl_inst/lcRst ),
	.F(\myfft/fft_top_inst/fft_ok )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/fft_ok_s1 .INIT=4'h8;
LUT2 \myfft/fft_top_inst/tf_inst/ctrl_inst/isBS0_s2  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/isBS0_6 ),
	.I1(\myfft/fft_top_inst/tf_inst/ctrl_inst/isBS0_7 ),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/isBS0_5 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/isBS0_s2 .INIT=4'h8;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/isBS2_s2  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt [3]),
	.I1(\myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt [4]),
	.I2(\myfft/fft_top_inst/tf_inst/ctrl_inst/n60_4 ),
	.I3(\myfft/fft_top_inst/tf_inst/ctrl_inst/n53_8 ),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/isBS2_5 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/isBS2_s2 .INIT=16'h8000;
LUT3 \myfft/fft_top_inst/tf_inst/ctrl_inst/n53_s2  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt [3]),
	.I1(\myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt [4]),
	.I2(\myfft/fft_top_inst/tf_inst/ctrl_inst/n58_6 ),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/n53_5 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/n53_s2 .INIT=8'h80;
LUT2 \myfft/fft_top_inst/tf_inst/ctrl_inst/n53_s3  (
	.I0(\myfft/fft_top_inst/ftCtl_ea ),
	.I1(\myfft/fft_top_inst/tf_inst/ctrl_inst/lcRst ),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/n53_6 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/n53_s3 .INIT=4'h8;
LUT2 \myfft/fft_top_inst/tf_inst/ctrl_inst/n55_s1  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt [5]),
	.I1(\myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt [6]),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/n55_4 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/n55_s1 .INIT=4'h8;
LUT2 \myfft/fft_top_inst/tf_inst/ctrl_inst/n60_s1  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt [0]),
	.I1(\myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt [1]),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/n60_4 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/n60_s1 .INIT=4'h8;
LUT2 \myfft/fft_top_inst/tf_inst/ctrl_inst/n95_s1  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/lvl [0]),
	.I1(\myfft/fft_top_inst/tf_inst/ctrl_inst/lvl [1]),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/n95_4 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/n95_s1 .INIT=4'h8;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/twBtfy_ea_s0  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/lcRst ),
	.I1(\myfft/fft_top_inst/tf_inst/ctrl_inst/isBS0 ),
	.I2(\myfft/fft_top_inst/tf_inst/ctrl_inst/isBS1 ),
	.I3(\myfft/fft_top_inst/tf_inst/ctrl_inst/isBS2 ),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/twBtfy_ea_3 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/twBtfy_ea_s0 .INIT=16'h0001;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/isBS0_s3  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt [5]),
	.I1(\myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt [6]),
	.I2(\myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt [7]),
	.I3(\myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt [8]),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/isBS0_6 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/isBS0_s3 .INIT=16'h0001;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/isBS0_s4  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt [1]),
	.I1(\myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt [2]),
	.I2(\myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt [3]),
	.I3(\myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt [4]),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/isBS0_7 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/isBS0_s4 .INIT=16'h0001;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/n96_s1  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/n53_6 ),
	.I1(\myfft/fft_top_inst/tf_inst/ctrl_inst/lvl [0]),
	.I2(\myfft/fft_top_inst/tf_inst/ctrl_inst/lvl [1]),
	.I3(\myfft/fft_top_inst/tf_inst/ctrl_inst/lvl [2]),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/n96_5 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/n96_s1 .INIT=16'h7F80;
LUT3 \myfft/fft_top_inst/tf_inst/ctrl_inst/n54_s2  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt [7]),
	.I1(\myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt [5]),
	.I2(\myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt [6]),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/n54_6 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/n54_s2 .INIT=8'h80;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/n53_s4  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt [7]),
	.I1(\myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt [8]),
	.I2(\myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt [5]),
	.I3(\myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt [6]),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/n53_8 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/n53_s4 .INIT=16'h8000;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/n58_s2  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt [2]),
	.I1(\myfft/fft_top_inst/ftCtl_ea ),
	.I2(\myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt [0]),
	.I3(\myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt [1]),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/n58_6 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/n58_s2 .INIT=16'h8000;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/isBE2_s2  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt [0]),
	.I1(\myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt [9]),
	.I2(\myfft/fft_top_inst/tf_inst/ctrl_inst/isBS0_6 ),
	.I3(\myfft/fft_top_inst/tf_inst/ctrl_inst/isBS0_7 ),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/lcRst )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/isBE2_s2 .INIT=16'h8000;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/isBS1_s2  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt [9]),
	.I1(\myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt [0]),
	.I2(\myfft/fft_top_inst/tf_inst/ctrl_inst/isBS0_6 ),
	.I3(\myfft/fft_top_inst/tf_inst/ctrl_inst/isBS0_7 ),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/isBS1 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/isBS1_s2 .INIT=16'h4000;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/isBS0_s5  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt [0]),
	.I1(\myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt [9]),
	.I2(\myfft/fft_top_inst/tf_inst/ctrl_inst/isBS0_6 ),
	.I3(\myfft/fft_top_inst/tf_inst/ctrl_inst/isBS0_7 ),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/isBS0 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/isBS0_s5 .INIT=16'h1000;
LUT3 \myfft/fft_top_inst/tf_inst/ctrl_inst/n99_s1  (
	.I0(\myfft/fft_top_inst/dSrc ),
	.I1(\myfft/fft_top_inst/ftCtl_ea ),
	.I2(\myfft/fft_top_inst/tf_inst/ctrl_inst/lcRst ),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/n99_5 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/n99_s1 .INIT=8'h6A;
LUT3 \myfft/fft_top_inst/tf_inst/ctrl_inst/n98_s1  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/lvl [0]),
	.I1(\myfft/fft_top_inst/ftCtl_ea ),
	.I2(\myfft/fft_top_inst/tf_inst/ctrl_inst/lcRst ),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/n98_5 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/n98_s1 .INIT=8'h6A;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/n97_s1  (
	.I0(\myfft/fft_top_inst/tf_inst/ctrl_inst/lvl [0]),
	.I1(\myfft/fft_top_inst/ftCtl_ea ),
	.I2(\myfft/fft_top_inst/tf_inst/ctrl_inst/lcRst ),
	.I3(\myfft/fft_top_inst/tf_inst/ctrl_inst/lvl [1]),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/n97_5 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/n97_s1 .INIT=16'h7F80;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/n59_s1  (
	.I0(\myfft/fft_top_inst/ftCtl_ea ),
	.I1(\myfft/fft_top_inst/tf_inst/ctrl_inst/lcRst ),
	.I2(\myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt [3]),
	.I3(\myfft/fft_top_inst/tf_inst/ctrl_inst/n58_6 ),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/n59_5 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/n59_s1 .INIT=16'h0770;
LUT4 \myfft/fft_top_inst/tf_inst/ctrl_inst/n57_s1  (
	.I0(\myfft/fft_top_inst/ftCtl_ea ),
	.I1(\myfft/fft_top_inst/tf_inst/ctrl_inst/lcRst ),
	.I2(\myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt [5]),
	.I3(\myfft/fft_top_inst/tf_inst/ctrl_inst/n53_5 ),
	.F(\myfft/fft_top_inst/tf_inst/ctrl_inst/n57_5 )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/n57_s1 .INIT=16'h0770;
DFFR \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt_8_s1  (
	.D(\myfft/fft_top_inst/tf_inst/ctrl_inst/n54_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/ctrl_inst/slfRst_2 ),
	.Q(\myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt [8])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt_8_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt_7_s1  (
	.D(\myfft/fft_top_inst/tf_inst/ctrl_inst/n55_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/ctrl_inst/slfRst_2 ),
	.Q(\myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt [7])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt_7_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt_6_s1  (
	.D(\myfft/fft_top_inst/tf_inst/ctrl_inst/n56_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/ctrl_inst/slfRst_2 ),
	.Q(\myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt [6])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt_6_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt_5_s1  (
	.D(\myfft/fft_top_inst/tf_inst/ctrl_inst/n57_5 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/ctrl_inst/slfRst_2 ),
	.Q(\myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt [5])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt_5_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt_4_s1  (
	.D(\myfft/fft_top_inst/tf_inst/ctrl_inst/n58_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/ctrl_inst/slfRst_2 ),
	.Q(\myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt [4])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt_4_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt_3_s1  (
	.D(\myfft/fft_top_inst/tf_inst/ctrl_inst/n59_5 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/ctrl_inst/slfRst_2 ),
	.Q(\myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt [3])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt_3_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt_2_s1  (
	.D(\myfft/fft_top_inst/tf_inst/ctrl_inst/n60_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/ctrl_inst/slfRst_2 ),
	.Q(\myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt [2])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt_2_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt_1_s1  (
	.D(\myfft/fft_top_inst/tf_inst/ctrl_inst/n61_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/ctrl_inst/slfRst_2 ),
	.Q(\myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt [1])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt_1_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt_0_s1  (
	.D(\myfft/fft_top_inst/tf_inst/ctrl_inst/n62_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/ctrl_inst/slfRst_2 ),
	.Q(\myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt [0])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt_0_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/ctrl_inst/lvl_3_s1  (
	.D(\myfft/fft_top_inst/tf_inst/ctrl_inst/n95_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/ctrl_inst/slfRst_2 ),
	.Q(\myfft/fft_top_inst/tf_inst/ctrl_inst/lvl [3])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/lvl_3_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/ctrl_inst/lvl_2_s1  (
	.D(\myfft/fft_top_inst/tf_inst/ctrl_inst/n96_5 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/ctrl_inst/slfRst_2 ),
	.Q(\myfft/fft_top_inst/tf_inst/ctrl_inst/lvl [2])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/lvl_2_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/ctrl_inst/lvl_1_s1  (
	.D(\myfft/fft_top_inst/tf_inst/ctrl_inst/n97_5 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/ctrl_inst/slfRst_2 ),
	.Q(\myfft/fft_top_inst/tf_inst/ctrl_inst/lvl [1])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/lvl_1_s1 .INIT=1'b0;
DFFS \myfft/fft_top_inst/tf_inst/ctrl_inst/lvl_0_s1  (
	.D(\myfft/fft_top_inst/tf_inst/ctrl_inst/n98_5 ),
	.CLK(out_d_11[35]),
	.SET(\myfft/fft_top_inst/tf_inst/ctrl_inst/slfRst_2 ),
	.Q(\myfft/fft_top_inst/tf_inst/ctrl_inst/lvl [0])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/lvl_0_s1 .INIT=1'b1;
DFFR \myfft/fft_top_inst/tf_inst/ctrl_inst/dSrc_s1  (
	.D(\myfft/fft_top_inst/tf_inst/ctrl_inst/n99_5 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/ctrl_inst/slfRst_2 ),
	.Q(\myfft/fft_top_inst/dSrc )
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/dSrc_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_1_9_s1  (
	.D(\myfft/fft_top_inst/tf_inst/ctrl_inst/n111_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/ctrl_inst/slfRst_2 ),
	.Q(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_1 [9])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_1_9_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_1_8_s1  (
	.D(\myfft/fft_top_inst/tf_inst/ctrl_inst/n112_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/ctrl_inst/slfRst_2 ),
	.Q(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_1 [8])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_1_8_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_1_7_s1  (
	.D(\myfft/fft_top_inst/tf_inst/ctrl_inst/n113_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/ctrl_inst/slfRst_2 ),
	.Q(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_1 [7])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_1_7_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_1_6_s1  (
	.D(\myfft/fft_top_inst/tf_inst/ctrl_inst/n114_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/ctrl_inst/slfRst_2 ),
	.Q(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_1 [6])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_1_6_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_1_5_s1  (
	.D(\myfft/fft_top_inst/tf_inst/ctrl_inst/n115_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/ctrl_inst/slfRst_2 ),
	.Q(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_1 [5])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_1_5_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_1_4_s1  (
	.D(\myfft/fft_top_inst/tf_inst/ctrl_inst/n116_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/ctrl_inst/slfRst_2 ),
	.Q(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_1 [4])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_1_4_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_1_3_s1  (
	.D(\myfft/fft_top_inst/tf_inst/ctrl_inst/n117_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/ctrl_inst/slfRst_2 ),
	.Q(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_1 [3])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_1_3_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_1_2_s1  (
	.D(\myfft/fft_top_inst/tf_inst/ctrl_inst/n118_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/ctrl_inst/slfRst_2 ),
	.Q(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_1 [2])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_1_2_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_1_1_s1  (
	.D(\myfft/fft_top_inst/tf_inst/ctrl_inst/n119_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/ctrl_inst/slfRst_2 ),
	.Q(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_1 [1])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_1_1_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_1_0_s1  (
	.D(\myfft/fft_top_inst/tf_inst/ctrl_inst/n120_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/ctrl_inst/slfRst_2 ),
	.Q(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_1 [0])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_1_0_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_2_9_s1  (
	.D(\myfft/fft_top_inst/tf_inst/ctrl_inst/n121_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/ctrl_inst/slfRst_2 ),
	.Q(\myfft/fft_top_inst/cclt_upwad_2 [9])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_2_9_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_2_8_s1  (
	.D(\myfft/fft_top_inst/tf_inst/ctrl_inst/n122_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/ctrl_inst/slfRst_2 ),
	.Q(\myfft/fft_top_inst/cclt_upwad_2 [8])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_2_8_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_2_7_s1  (
	.D(\myfft/fft_top_inst/tf_inst/ctrl_inst/n123_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/ctrl_inst/slfRst_2 ),
	.Q(\myfft/fft_top_inst/cclt_upwad_2 [7])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_2_7_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_2_6_s1  (
	.D(\myfft/fft_top_inst/tf_inst/ctrl_inst/n124_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/ctrl_inst/slfRst_2 ),
	.Q(\myfft/fft_top_inst/cclt_upwad_2 [6])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_2_6_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_2_5_s1  (
	.D(\myfft/fft_top_inst/tf_inst/ctrl_inst/n125_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/ctrl_inst/slfRst_2 ),
	.Q(\myfft/fft_top_inst/cclt_upwad_2 [5])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_2_5_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_2_4_s1  (
	.D(\myfft/fft_top_inst/tf_inst/ctrl_inst/n126_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/ctrl_inst/slfRst_2 ),
	.Q(\myfft/fft_top_inst/cclt_upwad_2 [4])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_2_4_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_2_3_s1  (
	.D(\myfft/fft_top_inst/tf_inst/ctrl_inst/n127_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/ctrl_inst/slfRst_2 ),
	.Q(\myfft/fft_top_inst/cclt_upwad_2 [3])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_2_3_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_2_2_s1  (
	.D(\myfft/fft_top_inst/tf_inst/ctrl_inst/n128_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/ctrl_inst/slfRst_2 ),
	.Q(\myfft/fft_top_inst/cclt_upwad_2 [2])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_2_2_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_2_1_s1  (
	.D(\myfft/fft_top_inst/tf_inst/ctrl_inst/n129_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/ctrl_inst/slfRst_2 ),
	.Q(\myfft/fft_top_inst/cclt_upwad_2 [1])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_2_1_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_2_0_s1  (
	.D(\myfft/fft_top_inst/tf_inst/ctrl_inst/n130_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/ctrl_inst/slfRst_2 ),
	.Q(\myfft/fft_top_inst/cclt_upwad_2 [0])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_upwad_2_0_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_1_9_s1  (
	.D(\myfft/fft_top_inst/tf_inst/ctrl_inst/n131_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/ctrl_inst/slfRst_2 ),
	.Q(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_1 [9])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_1_9_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_1_8_s1  (
	.D(\myfft/fft_top_inst/tf_inst/ctrl_inst/n132_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/ctrl_inst/slfRst_2 ),
	.Q(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_1 [8])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_1_8_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_1_7_s1  (
	.D(\myfft/fft_top_inst/tf_inst/ctrl_inst/n133_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/ctrl_inst/slfRst_2 ),
	.Q(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_1 [7])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_1_7_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_1_6_s1  (
	.D(\myfft/fft_top_inst/tf_inst/ctrl_inst/n134_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/ctrl_inst/slfRst_2 ),
	.Q(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_1 [6])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_1_6_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_1_5_s1  (
	.D(\myfft/fft_top_inst/tf_inst/ctrl_inst/n135_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/ctrl_inst/slfRst_2 ),
	.Q(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_1 [5])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_1_5_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_1_4_s1  (
	.D(\myfft/fft_top_inst/tf_inst/ctrl_inst/n136_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/ctrl_inst/slfRst_2 ),
	.Q(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_1 [4])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_1_4_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_1_3_s1  (
	.D(\myfft/fft_top_inst/tf_inst/ctrl_inst/n137_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/ctrl_inst/slfRst_2 ),
	.Q(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_1 [3])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_1_3_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_1_2_s1  (
	.D(\myfft/fft_top_inst/tf_inst/ctrl_inst/n138_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/ctrl_inst/slfRst_2 ),
	.Q(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_1 [2])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_1_2_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_1_1_s1  (
	.D(\myfft/fft_top_inst/tf_inst/ctrl_inst/n139_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/ctrl_inst/slfRst_2 ),
	.Q(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_1 [1])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_1_1_s1 .INIT=1'b0;
DFFS \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_1_0_s1  (
	.D(\myfft/fft_top_inst/tf_inst/ctrl_inst/n140_3 ),
	.CLK(out_d_11[35]),
	.SET(\myfft/fft_top_inst/tf_inst/ctrl_inst/slfRst_2 ),
	.Q(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_1 [0])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_1_0_s1 .INIT=1'b1;
DFFR \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_2_9_s1  (
	.D(\myfft/fft_top_inst/tf_inst/ctrl_inst/n141_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/ctrl_inst/slfRst_2 ),
	.Q(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_2 [9])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_2_9_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_2_8_s1  (
	.D(\myfft/fft_top_inst/tf_inst/ctrl_inst/n142_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/ctrl_inst/slfRst_2 ),
	.Q(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_2 [8])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_2_8_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_2_7_s1  (
	.D(\myfft/fft_top_inst/tf_inst/ctrl_inst/n143_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/ctrl_inst/slfRst_2 ),
	.Q(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_2 [7])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_2_7_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_2_6_s1  (
	.D(\myfft/fft_top_inst/tf_inst/ctrl_inst/n144_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/ctrl_inst/slfRst_2 ),
	.Q(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_2 [6])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_2_6_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_2_5_s1  (
	.D(\myfft/fft_top_inst/tf_inst/ctrl_inst/n145_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/ctrl_inst/slfRst_2 ),
	.Q(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_2 [5])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_2_5_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_2_4_s1  (
	.D(\myfft/fft_top_inst/tf_inst/ctrl_inst/n146_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/ctrl_inst/slfRst_2 ),
	.Q(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_2 [4])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_2_4_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_2_3_s1  (
	.D(\myfft/fft_top_inst/tf_inst/ctrl_inst/n147_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/ctrl_inst/slfRst_2 ),
	.Q(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_2 [3])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_2_3_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_2_2_s1  (
	.D(\myfft/fft_top_inst/tf_inst/ctrl_inst/n148_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/ctrl_inst/slfRst_2 ),
	.Q(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_2 [2])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_2_2_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_2_1_s1  (
	.D(\myfft/fft_top_inst/tf_inst/ctrl_inst/n149_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/ctrl_inst/slfRst_2 ),
	.Q(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_2 [1])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_2_1_s1 .INIT=1'b0;
DFFS \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_2_0_s1  (
	.D(\myfft/fft_top_inst/tf_inst/ctrl_inst/n150_3 ),
	.CLK(out_d_11[35]),
	.SET(\myfft/fft_top_inst/tf_inst/ctrl_inst/slfRst_2 ),
	.Q(\myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_2 [0])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/cclt_dwnad_2_0_s1 .INIT=1'b1;
DFFR \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt_9_s1  (
	.D(\myfft/fft_top_inst/tf_inst/ctrl_inst/n53_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/tf_inst/ctrl_inst/slfRst_2 ),
	.Q(\myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt [9])
);
defparam \myfft/fft_top_inst/tf_inst/ctrl_inst/btfy_cnt_9_s1 .INIT=1'b0;
LUT4 \myfft/fft_top_inst/udxk_inst/isLstXk_s1  (
	.I0(\myfft/fft_top_inst/udxk_inst/ud_xk_id [0]),
	.I1(\myfft/fft_top_inst/udxk_inst/ud_xk_id [1]),
	.I2(\myfft/fft_top_inst/udxk_inst/isLstXk_5 ),
	.I3(\myfft/fft_top_inst/udxk_inst/isLstXk_6 ),
	.F(\myfft/fft_top_inst/ud_ok )
);
defparam \myfft/fft_top_inst/udxk_inst/isLstXk_s1 .INIT=16'h8000;
LUT2 \myfft/fft_top_inst/udxk_inst/slfRst_s1  (
	.I0(\myfft/fft_top_inst/slfRst ),
	.I1(\myfft/fft_top_inst/ud_ok ),
	.F(\myfft/fft_top_inst/udxk_inst/slfRst_4 )
);
defparam \myfft/fft_top_inst/udxk_inst/slfRst_s1 .INIT=4'hE;
LUT4 \myfft/fft_top_inst/udxk_inst/n45_s0  (
	.I0(\myfft/fft_top_inst/ud_xkm_ad [8]),
	.I1(\myfft/fft_top_inst/udxk_inst/n45_6 ),
	.I2(\myfft/fft_top_inst/udxk_inst/n202_4 ),
	.I3(\myfft/fft_top_inst/ud_xkm_ad [9]),
	.F(\myfft/fft_top_inst/udxk_inst/n45_3 )
);
defparam \myfft/fft_top_inst/udxk_inst/n45_s0 .INIT=16'h0708;
LUT4 \myfft/fft_top_inst/udxk_inst/n47_s0  (
	.I0(\myfft/fft_top_inst/udxk_inst/n47_4 ),
	.I1(\myfft/fft_top_inst/udxk_inst/n47_5 ),
	.I2(\myfft/fft_top_inst/udxk_inst/n202_4 ),
	.I3(\myfft/fft_top_inst/ud_xkm_ad [7]),
	.F(\myfft/fft_top_inst/udxk_inst/n47_3 )
);
defparam \myfft/fft_top_inst/udxk_inst/n47_s0 .INIT=16'h0708;
LUT4 \myfft/fft_top_inst/udxk_inst/n48_s0  (
	.I0(\myfft/fft_top_inst/ud_xkm_ad [5]),
	.I1(\myfft/fft_top_inst/udxk_inst/n47_4 ),
	.I2(\myfft/fft_top_inst/udxk_inst/n202_4 ),
	.I3(\myfft/fft_top_inst/ud_xkm_ad [6]),
	.F(\myfft/fft_top_inst/udxk_inst/n48_3 )
);
defparam \myfft/fft_top_inst/udxk_inst/n48_s0 .INIT=16'h0708;
LUT4 \myfft/fft_top_inst/udxk_inst/n50_s0  (
	.I0(\myfft/fft_top_inst/ud_xkm_ad [3]),
	.I1(\myfft/fft_top_inst/udxk_inst/n50_4 ),
	.I2(\myfft/fft_top_inst/udxk_inst/n202_4 ),
	.I3(\myfft/fft_top_inst/ud_xkm_ad [4]),
	.F(\myfft/fft_top_inst/udxk_inst/n50_3 )
);
defparam \myfft/fft_top_inst/udxk_inst/n50_s0 .INIT=16'h0708;
LUT4 \myfft/fft_top_inst/udxk_inst/n52_s0  (
	.I0(\myfft/fft_top_inst/ud_ok ),
	.I1(\myfft/fft_top_inst/udxk_inst/start_reg ),
	.I2(\myfft/fft_top_inst/udxk_inst/n52_4 ),
	.I3(\myfft/fft_top_inst/ud_xkm_ad [2]),
	.F(\myfft/fft_top_inst/udxk_inst/n52_3 )
);
defparam \myfft/fft_top_inst/udxk_inst/n52_s0 .INIT=16'h3740;
LUT4 \myfft/fft_top_inst/udxk_inst/n53_s0  (
	.I0(\myfft/fft_top_inst/ud_ok ),
	.I1(\myfft/fft_top_inst/udxk_inst/start_reg ),
	.I2(\myfft/fft_top_inst/ud_xkm_ad [0]),
	.I3(\myfft/fft_top_inst/ud_xkm_ad [1]),
	.F(\myfft/fft_top_inst/udxk_inst/n53_3 )
);
defparam \myfft/fft_top_inst/udxk_inst/n53_s0 .INIT=16'h3740;
LUT3 \myfft/fft_top_inst/udxk_inst/n54_s0  (
	.I0(\myfft/fft_top_inst/ud_ok ),
	.I1(\myfft/fft_top_inst/ud_xkm_ad [0]),
	.I2(\myfft/fft_top_inst/udxk_inst/start_reg ),
	.F(\myfft/fft_top_inst/udxk_inst/n54_3 )
);
defparam \myfft/fft_top_inst/udxk_inst/n54_s0 .INIT=8'h1C;
LUT2 \myfft/fft_top_inst/udxk_inst/ud_xk_im_0_s  (
	.I0(out_d_14[32]),
	.I1(\myfft/fft_top_inst/ud_xkm_im [0]),
	.F(out_d_14[37])
);
defparam \myfft/fft_top_inst/udxk_inst/ud_xk_im_0_s .INIT=4'h8;
LUT2 \myfft/fft_top_inst/udxk_inst/ud_xk_im_1_s  (
	.I0(out_d_14[32]),
	.I1(\myfft/fft_top_inst/ud_xkm_im [1]),
	.F(out_d_14[38])
);
defparam \myfft/fft_top_inst/udxk_inst/ud_xk_im_1_s .INIT=4'h8;
LUT2 \myfft/fft_top_inst/udxk_inst/ud_xk_im_2_s  (
	.I0(out_d_14[32]),
	.I1(\myfft/fft_top_inst/ud_xkm_im [2]),
	.F(out_d_14[39])
);
defparam \myfft/fft_top_inst/udxk_inst/ud_xk_im_2_s .INIT=4'h8;
LUT2 \myfft/fft_top_inst/udxk_inst/ud_xk_im_3_s  (
	.I0(out_d_14[32]),
	.I1(\myfft/fft_top_inst/ud_xkm_im [3]),
	.F(out_d_14[40])
);
defparam \myfft/fft_top_inst/udxk_inst/ud_xk_im_3_s .INIT=4'h8;
LUT2 \myfft/fft_top_inst/udxk_inst/ud_xk_im_4_s  (
	.I0(out_d_14[32]),
	.I1(\myfft/fft_top_inst/ud_xkm_im [4]),
	.F(out_d_14[41])
);
defparam \myfft/fft_top_inst/udxk_inst/ud_xk_im_4_s .INIT=4'h8;
LUT2 \myfft/fft_top_inst/udxk_inst/ud_xk_im_5_s  (
	.I0(out_d_14[32]),
	.I1(\myfft/fft_top_inst/ud_xkm_im [5]),
	.F(out_d_14[42])
);
defparam \myfft/fft_top_inst/udxk_inst/ud_xk_im_5_s .INIT=4'h8;
LUT2 \myfft/fft_top_inst/udxk_inst/ud_xk_im_6_s  (
	.I0(out_d_14[32]),
	.I1(\myfft/fft_top_inst/ud_xkm_im [6]),
	.F(out_d_14[43])
);
defparam \myfft/fft_top_inst/udxk_inst/ud_xk_im_6_s .INIT=4'h8;
LUT2 \myfft/fft_top_inst/udxk_inst/ud_xk_im_7_s  (
	.I0(out_d_14[32]),
	.I1(\myfft/fft_top_inst/ud_xkm_im [7]),
	.F(out_d_14[44])
);
defparam \myfft/fft_top_inst/udxk_inst/ud_xk_im_7_s .INIT=4'h8;
LUT2 \myfft/fft_top_inst/udxk_inst/ud_xk_im_8_s  (
	.I0(out_d_14[32]),
	.I1(\myfft/fft_top_inst/ud_xkm_im [8]),
	.F(out_d_14[45])
);
defparam \myfft/fft_top_inst/udxk_inst/ud_xk_im_8_s .INIT=4'h8;
LUT2 \myfft/fft_top_inst/udxk_inst/ud_xk_im_9_s  (
	.I0(out_d_14[32]),
	.I1(\myfft/fft_top_inst/ud_xkm_im [9]),
	.F(out_d_14[46])
);
defparam \myfft/fft_top_inst/udxk_inst/ud_xk_im_9_s .INIT=4'h8;
LUT2 \myfft/fft_top_inst/udxk_inst/ud_xk_im_10_s  (
	.I0(out_d_14[32]),
	.I1(\myfft/fft_top_inst/ud_xkm_im [10]),
	.F(out_d_14[47])
);
defparam \myfft/fft_top_inst/udxk_inst/ud_xk_im_10_s .INIT=4'h8;
LUT2 \myfft/fft_top_inst/udxk_inst/ud_xk_im_11_s  (
	.I0(out_d_14[32]),
	.I1(\myfft/fft_top_inst/ud_xkm_im [11]),
	.F(out_d_14[48])
);
defparam \myfft/fft_top_inst/udxk_inst/ud_xk_im_11_s .INIT=4'h8;
LUT2 \myfft/fft_top_inst/udxk_inst/ud_xk_im_12_s  (
	.I0(out_d_14[32]),
	.I1(\myfft/fft_top_inst/ud_xkm_im [12]),
	.F(out_d_14[49])
);
defparam \myfft/fft_top_inst/udxk_inst/ud_xk_im_12_s .INIT=4'h8;
LUT2 \myfft/fft_top_inst/udxk_inst/ud_xk_im_13_s  (
	.I0(out_d_14[32]),
	.I1(\myfft/fft_top_inst/ud_xkm_im [13]),
	.F(out_d_14[50])
);
defparam \myfft/fft_top_inst/udxk_inst/ud_xk_im_13_s .INIT=4'h8;
LUT2 \myfft/fft_top_inst/udxk_inst/ud_xk_im_14_s  (
	.I0(out_d_14[32]),
	.I1(\myfft/fft_top_inst/ud_xkm_im [14]),
	.F(out_d_14[51])
);
defparam \myfft/fft_top_inst/udxk_inst/ud_xk_im_14_s .INIT=4'h8;
LUT2 \myfft/fft_top_inst/udxk_inst/ud_xk_im_15_s  (
	.I0(out_d_14[32]),
	.I1(\myfft/fft_top_inst/ud_xkm_im [15]),
	.F(out_d_14[52])
);
defparam \myfft/fft_top_inst/udxk_inst/ud_xk_im_15_s .INIT=4'h8;
LUT2 \myfft/fft_top_inst/udxk_inst/ud_xk_re_0_s  (
	.I0(out_d_14[32]),
	.I1(\myfft/fft_top_inst/ud_xkm_re [0]),
	.F(out_d_14[11])
);
defparam \myfft/fft_top_inst/udxk_inst/ud_xk_re_0_s .INIT=4'h8;
LUT2 \myfft/fft_top_inst/udxk_inst/ud_xk_re_1_s  (
	.I0(out_d_14[32]),
	.I1(\myfft/fft_top_inst/ud_xkm_re [1]),
	.F(out_d_14[12])
);
defparam \myfft/fft_top_inst/udxk_inst/ud_xk_re_1_s .INIT=4'h8;
LUT2 \myfft/fft_top_inst/udxk_inst/ud_xk_re_2_s  (
	.I0(out_d_14[32]),
	.I1(\myfft/fft_top_inst/ud_xkm_re [2]),
	.F(out_d_14[13])
);
defparam \myfft/fft_top_inst/udxk_inst/ud_xk_re_2_s .INIT=4'h8;
LUT2 \myfft/fft_top_inst/udxk_inst/ud_xk_re_3_s  (
	.I0(out_d_14[32]),
	.I1(\myfft/fft_top_inst/ud_xkm_re [3]),
	.F(out_d_14[14])
);
defparam \myfft/fft_top_inst/udxk_inst/ud_xk_re_3_s .INIT=4'h8;
LUT2 \myfft/fft_top_inst/udxk_inst/ud_xk_re_4_s  (
	.I0(out_d_14[32]),
	.I1(\myfft/fft_top_inst/ud_xkm_re [4]),
	.F(out_d_14[15])
);
defparam \myfft/fft_top_inst/udxk_inst/ud_xk_re_4_s .INIT=4'h8;
LUT2 \myfft/fft_top_inst/udxk_inst/ud_xk_re_5_s  (
	.I0(out_d_14[32]),
	.I1(\myfft/fft_top_inst/ud_xkm_re [5]),
	.F(out_d_14[16])
);
defparam \myfft/fft_top_inst/udxk_inst/ud_xk_re_5_s .INIT=4'h8;
LUT2 \myfft/fft_top_inst/udxk_inst/ud_xk_re_6_s  (
	.I0(out_d_14[32]),
	.I1(\myfft/fft_top_inst/ud_xkm_re [6]),
	.F(out_d_14[17])
);
defparam \myfft/fft_top_inst/udxk_inst/ud_xk_re_6_s .INIT=4'h8;
LUT2 \myfft/fft_top_inst/udxk_inst/ud_xk_re_7_s  (
	.I0(out_d_14[32]),
	.I1(\myfft/fft_top_inst/ud_xkm_re [7]),
	.F(out_d_14[18])
);
defparam \myfft/fft_top_inst/udxk_inst/ud_xk_re_7_s .INIT=4'h8;
LUT2 \myfft/fft_top_inst/udxk_inst/ud_xk_re_8_s  (
	.I0(out_d_14[32]),
	.I1(\myfft/fft_top_inst/ud_xkm_re [8]),
	.F(out_d_14[19])
);
defparam \myfft/fft_top_inst/udxk_inst/ud_xk_re_8_s .INIT=4'h8;
LUT2 \myfft/fft_top_inst/udxk_inst/ud_xk_re_9_s  (
	.I0(out_d_14[32]),
	.I1(\myfft/fft_top_inst/ud_xkm_re [9]),
	.F(out_d_14[20])
);
defparam \myfft/fft_top_inst/udxk_inst/ud_xk_re_9_s .INIT=4'h8;
LUT2 \myfft/fft_top_inst/udxk_inst/ud_xk_re_10_s  (
	.I0(out_d_14[32]),
	.I1(\myfft/fft_top_inst/ud_xkm_re [10]),
	.F(out_d_14[21])
);
defparam \myfft/fft_top_inst/udxk_inst/ud_xk_re_10_s .INIT=4'h8;
LUT2 \myfft/fft_top_inst/udxk_inst/ud_xk_re_11_s  (
	.I0(out_d_14[32]),
	.I1(\myfft/fft_top_inst/ud_xkm_re [11]),
	.F(out_d_14[22])
);
defparam \myfft/fft_top_inst/udxk_inst/ud_xk_re_11_s .INIT=4'h8;
LUT2 \myfft/fft_top_inst/udxk_inst/ud_xk_re_12_s  (
	.I0(out_d_14[32]),
	.I1(\myfft/fft_top_inst/ud_xkm_re [12]),
	.F(out_d_14[23])
);
defparam \myfft/fft_top_inst/udxk_inst/ud_xk_re_12_s .INIT=4'h8;
LUT2 \myfft/fft_top_inst/udxk_inst/ud_xk_re_13_s  (
	.I0(out_d_14[32]),
	.I1(\myfft/fft_top_inst/ud_xkm_re [13]),
	.F(out_d_14[24])
);
defparam \myfft/fft_top_inst/udxk_inst/ud_xk_re_13_s .INIT=4'h8;
LUT2 \myfft/fft_top_inst/udxk_inst/ud_xk_re_14_s  (
	.I0(out_d_14[32]),
	.I1(\myfft/fft_top_inst/ud_xkm_re [14]),
	.F(out_d_14[25])
);
defparam \myfft/fft_top_inst/udxk_inst/ud_xk_re_14_s .INIT=4'h8;
LUT2 \myfft/fft_top_inst/udxk_inst/ud_xk_re_15_s  (
	.I0(out_d_14[32]),
	.I1(\myfft/fft_top_inst/ud_xkm_re [15]),
	.F(out_d_14[26])
);
defparam \myfft/fft_top_inst/udxk_inst/ud_xk_re_15_s .INIT=4'h8;
LUT3 \myfft/fft_top_inst/udxk_inst/n9_s1  (
	.I0(\myfft/fft_top_inst/ud_ok ),
	.I1(\myfft/fft_top_inst/udxk_inst/start_reg ),
	.I2(\myfft/fft_top_inst/fft_ok ),
	.F(\myfft/fft_top_inst/udxk_inst/n9_5 )
);
defparam \myfft/fft_top_inst/udxk_inst/n9_s1 .INIT=8'hF4;
LUT2 \myfft/fft_top_inst/udxk_inst/n202_s1  (
	.I0(\myfft/fft_top_inst/ud_ok ),
	.I1(\myfft/fft_top_inst/udxk_inst/start_reg ),
	.F(\myfft/fft_top_inst/udxk_inst/n202_4 )
);
defparam \myfft/fft_top_inst/udxk_inst/n202_s1 .INIT=4'h8;
LUT4 \myfft/fft_top_inst/udxk_inst/isLstXk_s2  (
	.I0(\myfft/fft_top_inst/udxk_inst/ud_xk_id [2]),
	.I1(\myfft/fft_top_inst/udxk_inst/ud_xk_id [3]),
	.I2(\myfft/fft_top_inst/udxk_inst/ud_xk_id [4]),
	.I3(\myfft/fft_top_inst/udxk_inst/ud_xk_id [5]),
	.F(\myfft/fft_top_inst/udxk_inst/isLstXk_5 )
);
defparam \myfft/fft_top_inst/udxk_inst/isLstXk_s2 .INIT=16'h8000;
LUT4 \myfft/fft_top_inst/udxk_inst/isLstXk_s3  (
	.I0(\myfft/fft_top_inst/udxk_inst/ud_xk_id [6]),
	.I1(\myfft/fft_top_inst/udxk_inst/ud_xk_id [7]),
	.I2(\myfft/fft_top_inst/udxk_inst/ud_xk_id [8]),
	.I3(\myfft/fft_top_inst/udxk_inst/ud_xk_id [9]),
	.F(\myfft/fft_top_inst/udxk_inst/isLstXk_6 )
);
defparam \myfft/fft_top_inst/udxk_inst/isLstXk_s3 .INIT=16'h8000;
LUT3 \myfft/fft_top_inst/udxk_inst/n47_s1  (
	.I0(\myfft/fft_top_inst/ud_xkm_ad [4]),
	.I1(\myfft/fft_top_inst/ud_xkm_ad [3]),
	.I2(\myfft/fft_top_inst/udxk_inst/n50_4 ),
	.F(\myfft/fft_top_inst/udxk_inst/n47_4 )
);
defparam \myfft/fft_top_inst/udxk_inst/n47_s1 .INIT=8'h80;
LUT2 \myfft/fft_top_inst/udxk_inst/n47_s2  (
	.I0(\myfft/fft_top_inst/ud_xkm_ad [6]),
	.I1(\myfft/fft_top_inst/ud_xkm_ad [5]),
	.F(\myfft/fft_top_inst/udxk_inst/n47_5 )
);
defparam \myfft/fft_top_inst/udxk_inst/n47_s2 .INIT=4'h8;
LUT4 \myfft/fft_top_inst/udxk_inst/n50_s1  (
	.I0(\myfft/fft_top_inst/udxk_inst/start_reg ),
	.I1(\myfft/fft_top_inst/ud_xkm_ad [2]),
	.I2(\myfft/fft_top_inst/ud_xkm_ad [1]),
	.I3(\myfft/fft_top_inst/ud_xkm_ad [0]),
	.F(\myfft/fft_top_inst/udxk_inst/n50_4 )
);
defparam \myfft/fft_top_inst/udxk_inst/n50_s1 .INIT=16'h8000;
LUT2 \myfft/fft_top_inst/udxk_inst/n52_s1  (
	.I0(\myfft/fft_top_inst/ud_xkm_ad [1]),
	.I1(\myfft/fft_top_inst/ud_xkm_ad [0]),
	.F(\myfft/fft_top_inst/udxk_inst/n52_4 )
);
defparam \myfft/fft_top_inst/udxk_inst/n52_s1 .INIT=4'h8;
LUT4 \myfft/fft_top_inst/udxk_inst/n45_s2  (
	.I0(\myfft/fft_top_inst/ud_xkm_ad [7]),
	.I1(\myfft/fft_top_inst/udxk_inst/n47_4 ),
	.I2(\myfft/fft_top_inst/ud_xkm_ad [6]),
	.I3(\myfft/fft_top_inst/ud_xkm_ad [5]),
	.F(\myfft/fft_top_inst/udxk_inst/n45_6 )
);
defparam \myfft/fft_top_inst/udxk_inst/n45_s2 .INIT=16'h8000;
LUT4 \myfft/fft_top_inst/udxk_inst/n51_s1  (
	.I0(\myfft/fft_top_inst/ud_ok ),
	.I1(\myfft/fft_top_inst/udxk_inst/start_reg ),
	.I2(\myfft/fft_top_inst/ud_xkm_ad [3]),
	.I3(\myfft/fft_top_inst/udxk_inst/n50_4 ),
	.F(\myfft/fft_top_inst/udxk_inst/n51_5 )
);
defparam \myfft/fft_top_inst/udxk_inst/n51_s1 .INIT=16'h0770;
LUT4 \myfft/fft_top_inst/udxk_inst/n49_s1  (
	.I0(\myfft/fft_top_inst/ud_ok ),
	.I1(\myfft/fft_top_inst/udxk_inst/start_reg ),
	.I2(\myfft/fft_top_inst/ud_xkm_ad [5]),
	.I3(\myfft/fft_top_inst/udxk_inst/n47_4 ),
	.F(\myfft/fft_top_inst/udxk_inst/n49_5 )
);
defparam \myfft/fft_top_inst/udxk_inst/n49_s1 .INIT=16'h0770;
LUT4 \myfft/fft_top_inst/udxk_inst/n46_s1  (
	.I0(\myfft/fft_top_inst/ud_ok ),
	.I1(\myfft/fft_top_inst/udxk_inst/start_reg ),
	.I2(\myfft/fft_top_inst/ud_xkm_ad [8]),
	.I3(\myfft/fft_top_inst/udxk_inst/n45_6 ),
	.F(\myfft/fft_top_inst/udxk_inst/n46_5 )
);
defparam \myfft/fft_top_inst/udxk_inst/n46_s1 .INIT=16'h0770;
LUT3 \myfft/fft_top_inst/udxk_inst/n202_s2  (
	.I0(\myfft/fft_top_inst/udxk_inst/slfRst_4 ),
	.I1(\myfft/fft_top_inst/ud_ok ),
	.I2(\myfft/fft_top_inst/udxk_inst/start_reg ),
	.F(\myfft/fft_top_inst/udxk_inst/n202_6 )
);
defparam \myfft/fft_top_inst/udxk_inst/n202_s2 .INIT=8'h40;
DFFR \myfft/fft_top_inst/udxk_inst/xk_cnt_9_s1  (
	.D(\myfft/fft_top_inst/udxk_inst/n45_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/udxk_inst/slfRst_4 ),
	.Q(\myfft/fft_top_inst/ud_xkm_ad [9])
);
defparam \myfft/fft_top_inst/udxk_inst/xk_cnt_9_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/udxk_inst/xk_cnt_8_s1  (
	.D(\myfft/fft_top_inst/udxk_inst/n46_5 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/udxk_inst/slfRst_4 ),
	.Q(\myfft/fft_top_inst/ud_xkm_ad [8])
);
defparam \myfft/fft_top_inst/udxk_inst/xk_cnt_8_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/udxk_inst/xk_cnt_7_s1  (
	.D(\myfft/fft_top_inst/udxk_inst/n47_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/udxk_inst/slfRst_4 ),
	.Q(\myfft/fft_top_inst/ud_xkm_ad [7])
);
defparam \myfft/fft_top_inst/udxk_inst/xk_cnt_7_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/udxk_inst/xk_cnt_6_s1  (
	.D(\myfft/fft_top_inst/udxk_inst/n48_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/udxk_inst/slfRst_4 ),
	.Q(\myfft/fft_top_inst/ud_xkm_ad [6])
);
defparam \myfft/fft_top_inst/udxk_inst/xk_cnt_6_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/udxk_inst/xk_cnt_5_s1  (
	.D(\myfft/fft_top_inst/udxk_inst/n49_5 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/udxk_inst/slfRst_4 ),
	.Q(\myfft/fft_top_inst/ud_xkm_ad [5])
);
defparam \myfft/fft_top_inst/udxk_inst/xk_cnt_5_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/udxk_inst/xk_cnt_4_s1  (
	.D(\myfft/fft_top_inst/udxk_inst/n50_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/udxk_inst/slfRst_4 ),
	.Q(\myfft/fft_top_inst/ud_xkm_ad [4])
);
defparam \myfft/fft_top_inst/udxk_inst/xk_cnt_4_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/udxk_inst/xk_cnt_3_s1  (
	.D(\myfft/fft_top_inst/udxk_inst/n51_5 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/udxk_inst/slfRst_4 ),
	.Q(\myfft/fft_top_inst/ud_xkm_ad [3])
);
defparam \myfft/fft_top_inst/udxk_inst/xk_cnt_3_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/udxk_inst/xk_cnt_2_s1  (
	.D(\myfft/fft_top_inst/udxk_inst/n52_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/udxk_inst/slfRst_4 ),
	.Q(\myfft/fft_top_inst/ud_xkm_ad [2])
);
defparam \myfft/fft_top_inst/udxk_inst/xk_cnt_2_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/udxk_inst/xk_cnt_1_s1  (
	.D(\myfft/fft_top_inst/udxk_inst/n53_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/udxk_inst/slfRst_4 ),
	.Q(\myfft/fft_top_inst/ud_xkm_ad [1])
);
defparam \myfft/fft_top_inst/udxk_inst/xk_cnt_1_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/udxk_inst/xk_cnt_0_s1  (
	.D(\myfft/fft_top_inst/udxk_inst/n54_3 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/udxk_inst/slfRst_4 ),
	.Q(\myfft/fft_top_inst/ud_xkm_ad [0])
);
defparam \myfft/fft_top_inst/udxk_inst/xk_cnt_0_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/udxk_inst/xk_cnt_1_9_s1  (
	.D(\myfft/fft_top_inst/ud_xkm_ad [9]),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/udxk_inst/n202_6 ),
	.Q(\myfft/fft_top_inst/udxk_inst/ud_xk_id [9])
);
defparam \myfft/fft_top_inst/udxk_inst/xk_cnt_1_9_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/udxk_inst/xk_cnt_1_8_s1  (
	.D(\myfft/fft_top_inst/ud_xkm_ad [8]),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/udxk_inst/n202_6 ),
	.Q(\myfft/fft_top_inst/udxk_inst/ud_xk_id [8])
);
defparam \myfft/fft_top_inst/udxk_inst/xk_cnt_1_8_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/udxk_inst/xk_cnt_1_7_s1  (
	.D(\myfft/fft_top_inst/ud_xkm_ad [7]),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/udxk_inst/n202_6 ),
	.Q(\myfft/fft_top_inst/udxk_inst/ud_xk_id [7])
);
defparam \myfft/fft_top_inst/udxk_inst/xk_cnt_1_7_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/udxk_inst/xk_cnt_1_6_s1  (
	.D(\myfft/fft_top_inst/ud_xkm_ad [6]),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/udxk_inst/n202_6 ),
	.Q(\myfft/fft_top_inst/udxk_inst/ud_xk_id [6])
);
defparam \myfft/fft_top_inst/udxk_inst/xk_cnt_1_6_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/udxk_inst/xk_cnt_1_5_s1  (
	.D(\myfft/fft_top_inst/ud_xkm_ad [5]),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/udxk_inst/n202_6 ),
	.Q(\myfft/fft_top_inst/udxk_inst/ud_xk_id [5])
);
defparam \myfft/fft_top_inst/udxk_inst/xk_cnt_1_5_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/udxk_inst/xk_cnt_1_4_s1  (
	.D(\myfft/fft_top_inst/ud_xkm_ad [4]),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/udxk_inst/n202_6 ),
	.Q(\myfft/fft_top_inst/udxk_inst/ud_xk_id [4])
);
defparam \myfft/fft_top_inst/udxk_inst/xk_cnt_1_4_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/udxk_inst/xk_cnt_1_3_s1  (
	.D(\myfft/fft_top_inst/ud_xkm_ad [3]),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/udxk_inst/n202_6 ),
	.Q(\myfft/fft_top_inst/udxk_inst/ud_xk_id [3])
);
defparam \myfft/fft_top_inst/udxk_inst/xk_cnt_1_3_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/udxk_inst/xk_cnt_1_2_s1  (
	.D(\myfft/fft_top_inst/ud_xkm_ad [2]),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/udxk_inst/n202_6 ),
	.Q(\myfft/fft_top_inst/udxk_inst/ud_xk_id [2])
);
defparam \myfft/fft_top_inst/udxk_inst/xk_cnt_1_2_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/udxk_inst/xk_cnt_1_1_s1  (
	.D(\myfft/fft_top_inst/ud_xkm_ad [1]),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/udxk_inst/n202_6 ),
	.Q(\myfft/fft_top_inst/udxk_inst/ud_xk_id [1])
);
defparam \myfft/fft_top_inst/udxk_inst/xk_cnt_1_1_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/udxk_inst/xk_cnt_1_0_s1  (
	.D(\myfft/fft_top_inst/ud_xkm_ad [0]),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/udxk_inst/n202_6 ),
	.Q(\myfft/fft_top_inst/udxk_inst/ud_xk_id [0])
);
defparam \myfft/fft_top_inst/udxk_inst/xk_cnt_1_0_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/udxk_inst/dv_reg_s1  (
	.D(\myfft/fft_top_inst/udxk_inst/start_reg ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/udxk_inst/slfRst_4 ),
	.Q(out_d_14[32])
);
defparam \myfft/fft_top_inst/udxk_inst/dv_reg_s1 .INIT=1'b0;
DFFR \myfft/fft_top_inst/udxk_inst/start_reg_s1  (
	.D(\myfft/fft_top_inst/udxk_inst/n9_5 ),
	.CLK(out_d_11[35]),
	.RESET(\myfft/fft_top_inst/udxk_inst/slfRst_4 ),
	.Q(\myfft/fft_top_inst/udxk_inst/start_reg )
);
defparam \myfft/fft_top_inst/udxk_inst/start_reg_s1 .INIT=1'b0;
LUT4 \outCrt/n6_s0  (
	.I0(outState[1]),
	.I1(outState[2]),
	.I2(outState[0]),
	.I3(out_d_13[29]),
	.F(\outCrt/n6_3 )
);
defparam \outCrt/n6_s0 .INIT=16'hFF01;
LUT2 \outCrt/n9_s0  (
	.I0(outState[0]),
	.I1(outState[1]),
	.F(\outCrt/n9_5 )
);
defparam \outCrt/n9_s0 .INIT=4'h9;
LUT3 \outCrt/n8_s0  (
	.I0(outState[0]),
	.I1(outState[1]),
	.I2(outState[2]),
	.F(\outCrt/n8_5 )
);
defparam \outCrt/n8_s0 .INIT=8'hE1;
DFFR \outCrt/cnt_r_1_s0  (
	.D(\outCrt/n9_5 ),
	.CLK(sysClk),
	.RESET(\outCrt/n6_3 ),
	.Q(outState[1])
);
defparam \outCrt/cnt_r_1_s0 .INIT=1'b0;
DFFR \outCrt/cnt_r_0_s0  (
	.D(\outCrt/n10_7 ),
	.CLK(sysClk),
	.RESET(\outCrt/n6_3 ),
	.Q(outState[0])
);
defparam \outCrt/cnt_r_0_s0 .INIT=1'b0;
DFFS \outCrt/cnt_r_2_s0  (
	.D(\outCrt/n8_5 ),
	.CLK(sysClk),
	.SET(\outCrt/n6_3 ),
	.Q(outState[2])
);
defparam \outCrt/cnt_r_2_s0 .INIT=1'b1;
LUT1 \outCrt/n10_s2  (
	.I0(outState[0]),
	.F(\outCrt/n10_7 )
);
defparam \outCrt/n10_s2 .INIT=2'h1;
endmodule
