//Copyright (C)2014-2022 Gowin Semiconductor Corporation.
//All rights reserved. 
//File Title: Physical Constraints file
//GOWIN Version: 1.9.8.07
//Part Number: GW2A-LV18PG256C8/I7
//Device: GW2A-18C
//Created Time: Thu 06 01 05:52:39 2023

IO_LOC "led[5]" L16;
IO_PORT "led[5]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led[4]" L14;
IO_PORT "led[4]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led[3]" N14;
IO_PORT "led[3]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led[2]" N16;
IO_PORT "led[2]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led[1]" A13;
IO_PORT "led[1]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "led[0]" C13;
IO_PORT "led[0]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "phyrst" F10;
IO_PORT "phyrst" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=24;
IO_LOC "netrmii_txd[1]" E14;
IO_PORT "netrmii_txd[1]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "netrmii_txd[0]" D16;
IO_PORT "netrmii_txd[0]" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=8;
IO_LOC "netrmii_txen" E16;
IO_PORT "netrmii_txen" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=24;
IO_LOC "netrmii_mdc" F14;
IO_PORT "netrmii_mdc" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=24;
IO_LOC "netrmii_rxd[1]" C9;
IO_PORT "netrmii_rxd[1]" IO_TYPE=LVCMOS33 PULL_MODE=UP;
IO_LOC "netrmii_rxd[0]" F15;
IO_PORT "netrmii_rxd[0]" IO_TYPE=LVCMOS33 PULL_MODE=UP;
IO_LOC "netrmii_rx_crs" M6;
IO_PORT "netrmii_rx_crs" IO_TYPE=LVCMOS33 PULL_MODE=UP;
IO_LOC "netrmii_clk50m" A9;
IO_PORT "netrmii_clk50m" IO_TYPE=LVCMOS33 PULL_MODE=UP;
IO_LOC "rst" T10;
IO_PORT "rst" IO_TYPE=LVCMOS33 PULL_MODE=UP;
IO_LOC "clk" H11;
IO_PORT "clk" IO_TYPE=LVCMOS33 PULL_MODE=UP;
IO_LOC "netrmii_mdio" F16;
IO_PORT "netrmii_mdio" IO_TYPE=LVCMOS33 PULL_MODE=UP DRIVE=24 OPEN_DRAIN=ON;
