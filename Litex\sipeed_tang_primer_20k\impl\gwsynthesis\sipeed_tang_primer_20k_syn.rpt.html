<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>synthesis Report</title>
<style type="text/css">
body { font-family: Verdana, Arial, sans-serif; font-size: 12px; }
div#main_wrapper{ width: 100%; }
div#content { margin-left: 350px; margin-right: 30px; }
div#catalog_wrapper {position: fixed; top: 30px; width: 350px; float: left; }
div#catalog ul { list-style-type: none; }
div#catalog li { text-align: left; list-style-type:circle; color: #0084ff; margin-top: 3px; margin-bottom: 3px; }
div#catalog a { display:inline-block; text-decoration: none; color: #0084ff; font-weight: bold; padding: 3px; }
div#catalog a:visited { color: #0084ff; }
div#catalog a:hover { color: #fff; background: #0084ff; }
hr { margin-top: 30px; margin-bottom: 30px; }
h1, h3 { text-align: center; }
h1 {margin-top: 50px; }
table, th, td { border: 1px solid #aaa; }
table { border-collapse:collapse; margin-top: 10px; margin-bottom: 20px; width: 100%; }
th, td { padding: 5px 5px 5px 5px; }
th { color: #fff; font-weight: bold; background-color: #0084ff; }
table.summary_table td.label { width: 24%; min-width: 200px; background-color: #dee8f4; }
table.detail_table td.label { min-width: 100px; width: 8%;}
</style>
</head>
<body>
<div id="main_wrapper">
<div id="catalog_wrapper">
<div id="catalog">
<ul>
<li><a href="#about" style=" font-size: 16px;">Synthesis Messages</a></li>
<li><a href="#summary" style=" font-size: 16px;">Synthesis Details</a></li>
<li><a href="#resource" style=" font-size: 16px;">Resource</a>
<ul>
<li><a href="#usage" style=" font-size: 14px;">Resource Usage Summary</a></li>
<li><a href="#utilization" style=" font-size: 14px;">Resource Utilization Summary</a></li>
</ul>
</li>
<li><a href="#timing" style=" font-size: 16px;">Timing</a>
<ul>
<li><a href="#clock" style=" font-size: 14px;">Clock Summary</a></li>
<li><a href="#performance" style=" font-size: 14px;">Max Frequency Summary</a></li>
<li><a href="#detail timing" style=" font-size: 14px;">Detail Timing Paths Informations</a></li>
</ul>
</li>
</ul>
</div><!-- catalog -->
</div><!-- catalog_wrapper -->
<div id="content">
<h1><a name="about">Synthesis Messages</a></h1>
<table class="summary_table">
<tr>
<td class="label">Report Title</td>
<td>GowinSynthesis Report</td>
</tr>
<tr>
<td class="label">Design File</td>
<td>G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\VexRiscv.v<br>
G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\src\sipeed_tang_primer_20k.v<br>
</td>
</tr>
<tr>
<td class="label">GowinSynthesis Constraints File</td>
<td>---</td>
</tr>
<tr>
<td class="label">Tool Version</td>
<td>V1.9.11.03 Education</td>
</tr>
<tr>
<td class="label">Part Number</td>
<td>GW2A-LV18PG256C8/I7</td>
</tr>
<tr>
<td class="label">Device</td>
<td>GW2A-18</td>
</tr>
<tr>
<td class="label">Device Version</td>
<td>C</td>
</tr>
<tr>
<td class="label">Created Time</td>
<td>Wed Jul 30 11:36:48 2025
</td>
</tr>
<tr>
<td class="label">Legal Announcement</td>
<td>Copyright (C)2014-2025 Gowin Semiconductor Corporation. ALL rights reserved.</td>
</tr>
</table>
<h1><a name="summary">Synthesis Details</a></h1>
<table class="summary_table">
<tr>
<td class="label">Top Level Module</td>
<td>sipeed_tang_primer_20k</td>
</tr>
<tr>
<td class="label">Synthesis Process</td>
<td>Running parser:<br/>&nbsp;&nbsp;&nbsp;&nbsp;CPU time = 0h 0m 0.937s, Elapsed time = 0h 0m 0.971s, Peak memory usage = 653.871MB<br/>Running netlist conversion:<br/>&nbsp;&nbsp;&nbsp;&nbsp;CPU time = 0h 0m 0s, Elapsed time = 0h 0m 0s, Peak memory usage = 0MB<br/>Running device independent optimization:<br/>&nbsp;&nbsp;&nbsp;&nbsp;Optimizing Phase 0: CPU time = 0h 0m 0.468s, Elapsed time = 0h 0m 0.474s, Peak memory usage = 653.930MB<br/>&nbsp;&nbsp;&nbsp;&nbsp;Optimizing Phase 1: CPU time = 0h 0m 0.14s, Elapsed time = 0h 0m 0.148s, Peak memory usage = 653.934MB<br/>&nbsp;&nbsp;&nbsp;&nbsp;Optimizing Phase 2: CPU time = 0h 0m 0.593s, Elapsed time = 0h 0m 0.606s, Peak memory usage = 653.984MB<br/>Running inference:<br/>&nbsp;&nbsp;&nbsp;&nbsp;Inferring Phase 0: CPU time = 0h 0m 0.75s, Elapsed time = 0h 0m 0.844s, Peak memory usage = 654.242MB<br/>&nbsp;&nbsp;&nbsp;&nbsp;Inferring Phase 1: CPU time = 0h 0m 0.062s, Elapsed time = 0h 0m 0.057s, Peak memory usage = 654.383MB<br/>&nbsp;&nbsp;&nbsp;&nbsp;Inferring Phase 2: CPU time = 0h 0m 0.015s, Elapsed time = 0h 0m 0.02s, Peak memory usage = 654.395MB<br/>&nbsp;&nbsp;&nbsp;&nbsp;Inferring Phase 3: CPU time = 0h 0m 0.015s, Elapsed time = 0h 0m 0.018s, Peak memory usage = 654.395MB<br/>Running technical mapping:<br/>&nbsp;&nbsp;&nbsp;&nbsp;Tech-Mapping Phase 0: CPU time = 0h 0m 0.734s, Elapsed time = 0h 0m 0.748s, Peak memory usage = 654.465MB<br/>&nbsp;&nbsp;&nbsp;&nbsp;Tech-Mapping Phase 1: CPU time = 0h 0m 0.093s, Elapsed time = 0h 0m 0.092s, Peak memory usage = 654.465MB<br/>&nbsp;&nbsp;&nbsp;&nbsp;Tech-Mapping Phase 2: CPU time = 0h 0m 0.109s, Elapsed time = 0h 0m 0.105s, Peak memory usage = 654.465MB<br/>&nbsp;&nbsp;&nbsp;&nbsp;Tech-Mapping Phase 3: CPU time = 0h 0m 16s, Elapsed time = 0h 0m 15s, Peak memory usage = 692.887MB<br/>&nbsp;&nbsp;&nbsp;&nbsp;Tech-Mapping Phase 4: CPU time = 0h 0m 0.484s, Elapsed time = 0h 0m 0.557s, Peak memory usage = 693.008MB<br/>Generate output files:<br/>&nbsp;&nbsp;&nbsp;&nbsp;CPU time = 0h 0m 0.39s, Elapsed time = 0h 0m 0.368s, Peak memory usage = 693.008MB<br/></td>
</tr>
<tr>
<td class="label">Total Time and Memory Usage</td>
<td>CPU time = 0h 0m 20s, Elapsed time = 0h 0m 20s, Peak memory usage = 693.008MB</td>
</tr>
</table>
<h1><a name="resource">Resource</a></h1>
<h2><a name="usage">Resource Usage Summary</a></h2>
<table class="summary_table">
<tr>
<td class="label"><b>Resource</b></td>
<td><b>Usage</b></td>
</tr>
<tr>
<td class="label"><b>I/O Port </b></td>
<td>62</td>
</tr>
<tr>
<td class="label"><b>I/O Buf </b></td>
<td>59</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspIBUF</td>
<td>7</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspOBUF</td>
<td>33</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspIOBUF</td>
<td>16</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspELVDS_OBUF</td>
<td>1</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspELVDS_IOBUF</td>
<td>2</td>
</tr>
<tr>
<td class="label"><b>Register </b></td>
<td>3172</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspDFF</td>
<td>273</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspDFFE</td>
<td>1158</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspDFFS</td>
<td>70</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspDFFSE</td>
<td>50</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspDFFR</td>
<td>737</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspDFFRE</td>
<td>882</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspDFFP</td>
<td>2</td>
</tr>
<tr>
<td class="label"><b>LUT </b></td>
<td>5165</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspLUT2</td>
<td>399</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspLUT3</td>
<td>1519</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspLUT4</td>
<td>3247</td>
</tr>
<tr>
<td class="label"><b>ALU </b></td>
<td>658</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspALU</td>
<td>658</td>
</tr>
<tr>
<td class="label"><b>SSRAM </b></td>
<td>68</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspRAM16SDP4</td>
<td>68</td>
</tr>
<tr>
<td class="label"><b>INV </b></td>
<td>34</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspINV</td>
<td>34</td>
</tr>
<tr>
<td class="label"><b>IOLOGIC </b></td>
<td>86</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspIDES4_MEM</td>
<td>16</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspOSER4</td>
<td>25</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspOSER4_MEM</td>
<td>20</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspIODELAY</td>
<td>25</td>
</tr>
<tr>
<td class="label"><b>DSP </b></td>
<td></td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspMULT18X18</td>
<td>4</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspALU54D</td>
<td>2</td>
</tr>
<tr>
<td class="label"><b>BSRAM </b></td>
<td>46</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspSP</td>
<td>21</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspSDPB</td>
<td>9</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbsppROM</td>
<td>16</td>
</tr>
<tr>
<td class="label"><b>CLOCK </b></td>
<td>5</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspCLKDIV</td>
<td>1</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspDQS</td>
<td>2</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspDHCEN</td>
<td>1</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbsprPLL</td>
<td>1</td>
</tr>
</table>
<h2><a name="utilization">Resource Utilization Summary</a></h2>
<table class="summary_table">
<tr>
<td class="label"><b>Resource</b></td>
<td><b>Usage</b></td>
<td><b>Utilization</b></td>
</tr>
<tr>
<td class="label">Logic</td>
<td>6265(5199 LUT, 658 ALU, 68 RAM16) / 20736</td>
<td>31%</td>
</tr>
<tr>
<td class="label">Register</td>
<td>3172 / 16173</td>
<td>20%</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp--Register as Latch</td>
<td>0 / 16173</td>
<td>0%</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp--Register as FF</td>
<td>3172 / 16173</td>
<td>20%</td>
</tr>
<tr>
<td class="label">BSRAM</td>
<td>46 / 46</td>
<td>100%</td>
</tr>
</table>
<h1><a name="timing">Timing</a></h1>
<h2><a name="clock">Clock Summary:</a></h2>
<table class="summary_table">
<tr>
<th>NO.</th>
<th>Clock Name</th>
<th>Type</th>
<th>Period</th>
<th>Frequency(MHz)</th>
<th>Rise</th>
<th>Fall</th>
<th>Source</th>
<th>Master</th>
<th>Object</th>
</tr>
<tr>
<td>1</td>
<td>clk27</td>
<td>Base</td>
<td>37.037</td>
<td>27.000</td>
<td>0.000</td>
<td>18.519</td>
<td> </td>
<td> </td>
<td>clk27_ibuf/I </td>
</tr>
<tr>
<td>2</td>
<td>rPLL/CLKOUT.default_gen_clk</td>
<td>Generated</td>
<td>10.417</td>
<td>96.000</td>
<td>0.000</td>
<td>5.208</td>
<td>clk27_ibuf/I</td>
<td>clk27</td>
<td>rPLL/CLKOUT </td>
</tr>
<tr>
<td>3</td>
<td>rPLL/CLKOUTP.default_gen_clk</td>
<td>Generated</td>
<td>10.417</td>
<td>96.000</td>
<td>0.000</td>
<td>5.208</td>
<td>clk27_ibuf/I</td>
<td>clk27</td>
<td>rPLL/CLKOUTP </td>
</tr>
<tr>
<td>4</td>
<td>rPLL/CLKOUTD.default_gen_clk</td>
<td>Generated</td>
<td>20.833</td>
<td>48.000</td>
<td>0.000</td>
<td>10.417</td>
<td>clk27_ibuf/I</td>
<td>clk27</td>
<td>rPLL/CLKOUTD </td>
</tr>
<tr>
<td>5</td>
<td>rPLL/CLKOUTD3.default_gen_clk</td>
<td>Generated</td>
<td>31.250</td>
<td>32.000</td>
<td>0.000</td>
<td>15.625</td>
<td>clk27_ibuf/I</td>
<td>clk27</td>
<td>rPLL/CLKOUTD3 </td>
</tr>
<tr>
<td>6</td>
<td>CLKDIV/CLKOUT.default_gen_clk</td>
<td>Generated</td>
<td>20.833</td>
<td>48.000</td>
<td>0.000</td>
<td>10.417</td>
<td>rPLL/CLKOUT</td>
<td>rPLL/CLKOUT.default_gen_clk</td>
<td>CLKDIV/CLKOUT </td>
</tr>
</table>
<h2><a name="performance">Max Frequency Summary:</a></h2>
<table class="summary_table">
<tr>
<th>NO.</th>
<th>Clock Name</th>
<th>Constraint</th>
<th>Actual Fmax</th>
<th>Logic Level</th>
<th>Entity</th>
</tr>
<tr>
<td>1</td>
<td>clk27</td>
<td>27.000(MHz)</td>
<td>268.168(MHz)</td>
<td>4</td>
<td>TOP</td>
</tr>
<tr>
<td>2</td>
<td>rPLL/CLKOUT.default_gen_clk</td>
<td>96.000(MHz)</td>
<td>1030.927(MHz)</td>
<td>1</td>
<td>TOP</td>
</tr>
<tr>
<td>3</td>
<td>CLKDIV/CLKOUT.default_gen_clk</td>
<td>48.000(MHz)</td>
<td>80.932(MHz)</td>
<td>15</td>
<td>TOP</td>
</tr>
</table>
<h2><a name="detail timing">Detail Timing Paths Information</a></h2>
<h3>Path&nbsp1</h3>
<b>Path Summary:</b></br>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>-0.405</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>298.391</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>297.987</td>
</tr>
<tr>
<td class="label">From</td>
<td>gw2ddrphy_pause1_s0</td>
</tr>
<tr>
<td class="label">To</td>
<td>DQS_1</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>clk27[F]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>rPLL/CLKOUT.default_gen_clk[R]</td>
</tr>
</table>
<b>Data Arrival Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>296.296</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>clk27</td>
</tr>
<tr>
<td>296.296</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>clk27_ibuf/I</td>
</tr>
<tr>
<td>296.296</td>
<td>0.000</td>
<td>tINS</td>
<td>RR</td>
<td>32</td>
<td>clk27_ibuf/O</td>
</tr>
<tr>
<td>296.656</td>
<td>0.360</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>gw2ddrphy_pause1_s0/CLK</td>
</tr>
<tr>
<td>296.888</td>
<td>0.232</td>
<td>tC2Q</td>
<td>RF</td>
<td>2</td>
<td>gw2ddrphy_pause1_s0/Q</td>
</tr>
<tr>
<td>297.362</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>n20419_s0/I1</td>
</tr>
<tr>
<td>297.917</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>n20419_s0/F</td>
</tr>
<tr>
<td>298.391</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>DQS_1/HOLD</td>
</tr>
</table>
<b>Data Required Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>296.875</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>rPLL/CLKOUT.default_gen_clk</td>
</tr>
<tr>
<td>297.375</td>
<td>0.500</td>
<td>tCL</td>
<td>FF</td>
<td>1</td>
<td>rPLL/CLKOUT</td>
</tr>
<tr>
<td>297.849</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>3</td>
<td>DHCEN/CLKIN</td>
</tr>
<tr>
<td>298.035</td>
<td>0.186</td>
<td>tINS</td>
<td>FF</td>
<td>65</td>
<td>DHCEN/CLKOUT</td>
</tr>
<tr>
<td>298.509</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>DQS_1/FCLK</td>
</tr>
<tr>
<td>298.474</td>
<td>-0.035</td>
<td>tUnc</td>
<td> </td>
<td> </td>
<td>DQS_1</td>
</tr>
<tr>
<td>297.987</td>
<td>-0.487</td>
<td>tSu</td>
<td> </td>
<td>1</td>
<td>DQS_1</td>
</tr>
</table>
<b>Path Statistics:</b>
<table class="summary_table">
<tr>
<td class="label">Clock Skew:</td>
<td>1.274</td>
</tr>
<tr>
<td class="label">Setup Relationship:</td>
<td>0.579</td>
</tr>
<tr>
<td class="label">Logic Level:</td>
<td>2</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay:</td><td> cell: 0.000, 0.000%; route: 0.360, 100.000%</td></tr>
<tr>
<td class="label">Arrival Data Path Delay:</td><td> cell: 0.555, 31.988%; route: 0.948, 54.640%; tC2Q: 0.232, 13.372%</td></tr>
<tr>
<td class="label">Required Clock Path Delay:</td><td> cell: 0.000, 0.000%; route: 0.360, 100.000%</td></tr>
</table>
<br/>
<h3>Path&nbsp2</h3>
<b>Path Summary:</b></br>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>-0.405</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>298.391</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>297.987</td>
</tr>
<tr>
<td class="label">From</td>
<td>gw2ddrphy_pause1_s0</td>
</tr>
<tr>
<td class="label">To</td>
<td>DQS</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>clk27[F]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>rPLL/CLKOUT.default_gen_clk[R]</td>
</tr>
</table>
<b>Data Arrival Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>296.296</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>clk27</td>
</tr>
<tr>
<td>296.296</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>clk27_ibuf/I</td>
</tr>
<tr>
<td>296.296</td>
<td>0.000</td>
<td>tINS</td>
<td>RR</td>
<td>32</td>
<td>clk27_ibuf/O</td>
</tr>
<tr>
<td>296.656</td>
<td>0.360</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>gw2ddrphy_pause1_s0/CLK</td>
</tr>
<tr>
<td>296.888</td>
<td>0.232</td>
<td>tC2Q</td>
<td>RF</td>
<td>2</td>
<td>gw2ddrphy_pause1_s0/Q</td>
</tr>
<tr>
<td>297.362</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>n20324_s0/I1</td>
</tr>
<tr>
<td>297.917</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>n20324_s0/F</td>
</tr>
<tr>
<td>298.391</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>DQS/HOLD</td>
</tr>
</table>
<b>Data Required Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>296.875</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>rPLL/CLKOUT.default_gen_clk</td>
</tr>
<tr>
<td>297.375</td>
<td>0.500</td>
<td>tCL</td>
<td>FF</td>
<td>1</td>
<td>rPLL/CLKOUT</td>
</tr>
<tr>
<td>297.849</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>3</td>
<td>DHCEN/CLKIN</td>
</tr>
<tr>
<td>298.035</td>
<td>0.186</td>
<td>tINS</td>
<td>FF</td>
<td>65</td>
<td>DHCEN/CLKOUT</td>
</tr>
<tr>
<td>298.509</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>DQS/FCLK</td>
</tr>
<tr>
<td>298.474</td>
<td>-0.035</td>
<td>tUnc</td>
<td> </td>
<td> </td>
<td>DQS</td>
</tr>
<tr>
<td>297.987</td>
<td>-0.487</td>
<td>tSu</td>
<td> </td>
<td>1</td>
<td>DQS</td>
</tr>
</table>
<b>Path Statistics:</b>
<table class="summary_table">
<tr>
<td class="label">Clock Skew:</td>
<td>1.274</td>
</tr>
<tr>
<td class="label">Setup Relationship:</td>
<td>0.579</td>
</tr>
<tr>
<td class="label">Logic Level:</td>
<td>2</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay:</td><td> cell: 0.000, 0.000%; route: 0.360, 100.000%</td></tr>
<tr>
<td class="label">Arrival Data Path Delay:</td><td> cell: 0.555, 31.988%; route: 0.948, 54.640%; tC2Q: 0.232, 13.372%</td></tr>
<tr>
<td class="label">Required Clock Path Delay:</td><td> cell: 0.000, 0.000%; route: 0.360, 100.000%</td></tr>
</table>
<br/>
<h3>Path&nbsp3</h3>
<b>Path Summary:</b></br>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>0.265</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>297.362</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>297.628</td>
</tr>
<tr>
<td class="label">From</td>
<td>gw2ddrphy_stop1_s0</td>
</tr>
<tr>
<td class="label">To</td>
<td>DHCEN</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>clk27[F]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>rPLL/CLKOUT.default_gen_clk[R]</td>
</tr>
</table>
<b>Data Arrival Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>296.296</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>clk27</td>
</tr>
<tr>
<td>296.296</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>clk27_ibuf/I</td>
</tr>
<tr>
<td>296.296</td>
<td>0.000</td>
<td>tINS</td>
<td>RR</td>
<td>32</td>
<td>clk27_ibuf/O</td>
</tr>
<tr>
<td>296.656</td>
<td>0.360</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>gw2ddrphy_stop1_s0/CLK</td>
</tr>
<tr>
<td>296.888</td>
<td>0.232</td>
<td>tC2Q</td>
<td>RF</td>
<td>1</td>
<td>gw2ddrphy_stop1_s0/Q</td>
</tr>
<tr>
<td>297.362</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>DHCEN/CE</td>
</tr>
</table>
<b>Data Required Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>296.875</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>rPLL/CLKOUT.default_gen_clk</td>
</tr>
<tr>
<td>297.375</td>
<td>0.500</td>
<td>tCL</td>
<td>FF</td>
<td>1</td>
<td>rPLL/CLKOUT</td>
</tr>
<tr>
<td>297.849</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>3</td>
<td>DHCEN/CLKIN</td>
</tr>
<tr>
<td>297.814</td>
<td>-0.035</td>
<td>tUnc</td>
<td> </td>
<td> </td>
<td>DHCEN</td>
</tr>
<tr>
<td>297.628</td>
<td>-0.186</td>
<td>tSu</td>
<td> </td>
<td>1</td>
<td>DHCEN</td>
</tr>
</table>
<b>Path Statistics:</b>
<table class="summary_table">
<tr>
<td class="label">Clock Skew:</td>
<td>0.614</td>
</tr>
<tr>
<td class="label">Setup Relationship:</td>
<td>0.579</td>
</tr>
<tr>
<td class="label">Logic Level:</td>
<td>1</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay:</td><td> cell: 0.000, 0.000%; route: 0.360, 100.000%</td></tr>
<tr>
<td class="label">Arrival Data Path Delay:</td><td> cell: 0.000, 0.000%; route: 0.474, 67.139%; tC2Q: 0.232, 32.861%</td></tr>
<tr>
<td class="label">Required Clock Path Delay:</td><td> cell: 0.000, 0.000%; route: 0.360, 100.000%</td></tr>
</table>
<br/>
<h3>Path&nbsp4</h3>
<b>Path Summary:</b></br>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>0.851</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>187.280</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>188.131</td>
</tr>
<tr>
<td class="label">From</td>
<td>gw2ddrphy_pause1_s0</td>
</tr>
<tr>
<td class="label">To</td>
<td>DQS</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>clk27[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>CLKDIV/CLKOUT.default_gen_clk[R]</td>
</tr>
</table>
<b>Data Arrival Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>185.185</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>clk27</td>
</tr>
<tr>
<td>185.185</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>clk27_ibuf/I</td>
</tr>
<tr>
<td>185.185</td>
<td>0.000</td>
<td>tINS</td>
<td>RR</td>
<td>32</td>
<td>clk27_ibuf/O</td>
</tr>
<tr>
<td>185.545</td>
<td>0.360</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>gw2ddrphy_pause1_s0/CLK</td>
</tr>
<tr>
<td>185.777</td>
<td>0.232</td>
<td>tC2Q</td>
<td>RF</td>
<td>2</td>
<td>gw2ddrphy_pause1_s0/Q</td>
</tr>
<tr>
<td>186.251</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>n20324_s0/I1</td>
</tr>
<tr>
<td>186.806</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>n20324_s0/F</td>
</tr>
<tr>
<td>187.280</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>DQS/HOLD</td>
</tr>
</table>
<b>Data Required Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>187.500</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>CLKDIV/CLKOUT.default_gen_clk</td>
</tr>
<tr>
<td>187.841</td>
<td>0.341</td>
<td>tCL</td>
<td>RR</td>
<td>3332</td>
<td>CLKDIV/CLKOUT</td>
</tr>
<tr>
<td>188.201</td>
<td>0.360</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>DQS/PCLK</td>
</tr>
<tr>
<td>188.166</td>
<td>-0.035</td>
<td>tUnc</td>
<td> </td>
<td> </td>
<td>DQS</td>
</tr>
<tr>
<td>188.131</td>
<td>-0.035</td>
<td>tSu</td>
<td> </td>
<td>1</td>
<td>DQS</td>
</tr>
</table>
<b>Path Statistics:</b>
<table class="summary_table">
<tr>
<td class="label">Clock Skew:</td>
<td>0.341</td>
</tr>
<tr>
<td class="label">Setup Relationship:</td>
<td>2.315</td>
</tr>
<tr>
<td class="label">Logic Level:</td>
<td>2</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay:</td><td> cell: 0.000, 0.000%; route: 0.360, 100.000%</td></tr>
<tr>
<td class="label">Arrival Data Path Delay:</td><td> cell: 0.555, 31.988%; route: 0.948, 54.640%; tC2Q: 0.232, 13.372%</td></tr>
<tr>
<td class="label">Required Clock Path Delay:</td><td> cell: 0.000, 0.000%; route: 0.360, 100.000%</td></tr>
</table>
<br/>
<h3>Path&nbsp5</h3>
<b>Path Summary:</b></br>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>0.851</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>187.280</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>188.131</td>
</tr>
<tr>
<td class="label">From</td>
<td>gw2ddrphy_pause1_s0</td>
</tr>
<tr>
<td class="label">To</td>
<td>DQS_1</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>clk27[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>CLKDIV/CLKOUT.default_gen_clk[R]</td>
</tr>
</table>
<b>Data Arrival Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>185.185</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>clk27</td>
</tr>
<tr>
<td>185.185</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>clk27_ibuf/I</td>
</tr>
<tr>
<td>185.185</td>
<td>0.000</td>
<td>tINS</td>
<td>RR</td>
<td>32</td>
<td>clk27_ibuf/O</td>
</tr>
<tr>
<td>185.545</td>
<td>0.360</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>gw2ddrphy_pause1_s0/CLK</td>
</tr>
<tr>
<td>185.777</td>
<td>0.232</td>
<td>tC2Q</td>
<td>RF</td>
<td>2</td>
<td>gw2ddrphy_pause1_s0/Q</td>
</tr>
<tr>
<td>186.251</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>n20419_s0/I1</td>
</tr>
<tr>
<td>186.806</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>n20419_s0/F</td>
</tr>
<tr>
<td>187.280</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>DQS_1/HOLD</td>
</tr>
</table>
<b>Data Required Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>187.500</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>CLKDIV/CLKOUT.default_gen_clk</td>
</tr>
<tr>
<td>187.841</td>
<td>0.341</td>
<td>tCL</td>
<td>RR</td>
<td>3332</td>
<td>CLKDIV/CLKOUT</td>
</tr>
<tr>
<td>188.201</td>
<td>0.360</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>DQS_1/PCLK</td>
</tr>
<tr>
<td>188.166</td>
<td>-0.035</td>
<td>tUnc</td>
<td> </td>
<td> </td>
<td>DQS_1</td>
</tr>
<tr>
<td>188.131</td>
<td>-0.035</td>
<td>tSu</td>
<td> </td>
<td>1</td>
<td>DQS_1</td>
</tr>
</table>
<b>Path Statistics:</b>
<table class="summary_table">
<tr>
<td class="label">Clock Skew:</td>
<td>0.341</td>
</tr>
<tr>
<td class="label">Setup Relationship:</td>
<td>2.315</td>
</tr>
<tr>
<td class="label">Logic Level:</td>
<td>2</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay:</td><td> cell: 0.000, 0.000%; route: 0.360, 100.000%</td></tr>
<tr>
<td class="label">Arrival Data Path Delay:</td><td> cell: 0.555, 31.988%; route: 0.948, 54.640%; tC2Q: 0.232, 13.372%</td></tr>
<tr>
<td class="label">Required Clock Path Delay:</td><td> cell: 0.000, 0.000%; route: 0.360, 100.000%</td></tr>
</table>
<br/>
</div><!-- content -->
</div><!-- main_wrapper -->
</body>
</html>
