<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>synthesis Report</title>
<style type="text/css">
body { font-family: Verdana, Arial, sans-serif; font-size: 12px; }
div#main_wrapper{ width: 100%; }
div#content { margin-left: 350px; margin-right: 30px; }
div#catalog_wrapper {position: fixed; top: 30px; width: 350px; float: left; }
div#catalog ul { list-style-type: none; }
div#catalog li { text-align: left; list-style-type:circle; color: #0084ff; margin-top: 3px; margin-bottom: 3px; }
div#catalog a { display:inline-block; text-decoration: none; color: #0084ff; font-weight: bold; padding: 3px; }
div#catalog a:visited { color: #0084ff; }
div#catalog a:hover { color: #fff; background: #0084ff; }
hr { margin-top: 30px; margin-bottom: 30px; }
h1, h3 { text-align: center; }
h1 {margin-top: 50px; }
table, th, td { border: 1px solid #aaa; }
table { border-collapse:collapse; margin-top: 10px; margin-bottom: 20px; width: 100%; }
th, td { padding: 5px 5px 5px 5px; }
th { color: #fff; font-weight: bold; background-color: #0084ff; }
table.summary_table td.label { width: 24%; min-width: 200px; background-color: #dee8f4; }
table.detail_table td.label { min-width: 100px; width: 8%;}
</style>
</head>
<body>
<div id="main_wrapper">
<div id="catalog_wrapper">
<div id="catalog">
<ul>
<li><a href="#about" style=" font-size: 16px;">Synthesis Messages</a></li>
<li><a href="#summary" style=" font-size: 16px;">Synthesis Details</a></li>
<li><a href="#resource" style=" font-size: 16px;">Resource</a>
<ul>
<li><a href="#usage" style=" font-size: 14px;">Resource Usage Summary</a></li>
<li><a href="#utilization" style=" font-size: 14px;">Resource Utilization Summary</a></li>
</ul>
</li>
<li><a href="#timing" style=" font-size: 16px;">Timing</a>
<ul>
<li><a href="#clock" style=" font-size: 14px;">Clock Summary</a></li>
<li><a href="#performance" style=" font-size: 14px;">Max Frequency Summary</a></li>
<li><a href="#detail timing" style=" font-size: 14px;">Detail Timing Paths Informations</a></li>
</ul>
</li>
</ul>
</div><!-- catalog -->
</div><!-- catalog_wrapper -->
<div id="content">
<h1><a name="about">Synthesis Messages</a></h1>
<table class="summary_table">
<tr>
<td class="label">Report Title</td>
<td>GowinSynthesis Report</td>
</tr>
<tr>
<td class="label">Design File</td>
<td>/netapp/share/gwsw/gowin_new/tags/Gowin/IDE/ipcore/FFT/data/fft.v<br>
/netapp/share/gwsw/gowin_new/tags/Gowin/IDE/ipcore/FFT/data/interface.v<br>
</td>
</tr>
<tr>
<td class="label">GowinSynthesis Constraints File</td>
<td>---</td>
</tr>
<tr>
<td class="label">Version</td>
<td>V1.9.8.11 Education</td>
</tr>
<tr>
<td class="label">Part Number</td>
<td>GW2A-LV18PG256C8/I7</td>
</tr>
<tr>
<td class="label">Device</td>
<td>GW2A-18</td>
</tr>
<tr>
<td class="label">Device Version</td>
<td>C</td>
</tr>
<tr>
<td class="label">Created Time</td>
<td>Sat May  6 10:45:05 2023
</td>
</tr>
<tr>
<td class="label">Legal Announcement</td>
<td>Copyright (C)2014-2023 Gowin Semiconductor Corporation. ALL rights reserved.</td>
</tr>
</table>
<h1><a name="summary">Synthesis Details</a></h1>
<table class="summary_table">
<tr>
<td class="label">Top Level Module</td>
<td>MOD_FFT</td>
</tr>
<tr>
<td class="label">Synthesis Process</td>
<td>Running parser:<br/>&nbsp;&nbsp;&nbsp;&nbsp;CPU time = 0h 0m 0.32s, Elapsed time = 0h 0m 0.34s, Peak memory usage = 72.602MB<br/>Running netlist conversion:<br/>&nbsp;&nbsp;&nbsp;&nbsp;CPU time = 0h 0m 0.02s, Elapsed time = 0h 0m 0.017s, Peak memory usage = 72.688MB<br/>Running device independent optimization:<br/>&nbsp;&nbsp;&nbsp;&nbsp;Optimizing Phase 0: CPU time = 0h 0m 0.029s, Elapsed time = 0h 0m 0.029s, Peak memory usage = 72.918MB<br/>&nbsp;&nbsp;&nbsp;&nbsp;Optimizing Phase 1: CPU time = 0h 0m 0.02s, Elapsed time = 0h 0m 0.015s, Peak memory usage = 72.980MB<br/>&nbsp;&nbsp;&nbsp;&nbsp;Optimizing Phase 2: CPU time = 0h 0m 0.02s, Elapsed time = 0h 0m 0.028s, Peak memory usage = 73.090MB<br/>Running inference:<br/>&nbsp;&nbsp;&nbsp;&nbsp;Inferring Phase 0: CPU time = 0h 0m 0.07s, Elapsed time = 0h 0m 0.069s, Peak memory usage = 73.508MB<br/>&nbsp;&nbsp;&nbsp;&nbsp;Inferring Phase 1: CPU time = 0h 0m 0.01s, Elapsed time = 0h 0m 0.002s, Peak memory usage = 73.598MB<br/>&nbsp;&nbsp;&nbsp;&nbsp;Inferring Phase 2: CPU time = 0h 0m 0s, Elapsed time = 0h 0m 0.007s, Peak memory usage = 73.656MB<br/>&nbsp;&nbsp;&nbsp;&nbsp;Inferring Phase 3: CPU time = 0h 0m 0.01s, Elapsed time = 0h 0m 0.003s, Peak memory usage = 73.742MB<br/>Running technical mapping:<br/>&nbsp;&nbsp;&nbsp;&nbsp;Tech-Mapping Phase 0: CPU time = 0h 0m 0.02s, Elapsed time = 0h 0m 0.027s, Peak memory usage = 73.773MB<br/>&nbsp;&nbsp;&nbsp;&nbsp;Tech-Mapping Phase 1: CPU time = 0h 0m 0.009s, Elapsed time = 0h 0m 0.008s, Peak memory usage = 73.773MB<br/>&nbsp;&nbsp;&nbsp;&nbsp;Tech-Mapping Phase 2: CPU time = 0h 0m 0.01s, Elapsed time = 0h 0m 0.009s, Peak memory usage = 73.773MB<br/>&nbsp;&nbsp;&nbsp;&nbsp;Tech-Mapping Phase 3: CPU time = 0h 0m 1s, Elapsed time = 0h 0m 1s, Peak memory usage = 98.758MB<br/>&nbsp;&nbsp;&nbsp;&nbsp;Tech-Mapping Phase 4: CPU time = 0h 0m 0.04s, Elapsed time = 0h 0m 0.039s, Peak memory usage = 98.758MB<br/>Generate output files:<br/>&nbsp;&nbsp;&nbsp;&nbsp;CPU time = 0h 0m 0.189s, Elapsed time = 0h 0m 0.198s, Peak memory usage = 98.758MB<br/></td>
</tr>
<tr>
<td class="label">Total Time and Memory Usage</td>
<td>CPU time = 0h 0m 1s, Elapsed time = 0h 0m 1s, Peak memory usage = 98.758MB</td>
</tr>
</table>
<h1><a name="resource">Resource</a></h1>
<h2><a name="usage">Resource Usage Summary</a></h2>
<table class="summary_table">
<tr>
<td class="label"><b>Resource</b></td>
<td><b>Usage</b></td>
</tr>
<tr>
<td class="label"><b>I/O Port </b></td>
<td>84</td>
</tr>
<tr>
<td class="label"><b>I/O Buf </b></td>
<td>84</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspIBUF</td>
<td>35</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspOBUF</td>
<td>49</td>
</tr>
<tr>
<td class="label"><b>Register </b></td>
<td>211</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspDFF</td>
<td>1</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspDFFS</td>
<td>8</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspDFFR</td>
<td>170</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspDFFRE</td>
<td>32</td>
</tr>
<tr>
<td class="label"><b>LUT </b></td>
<td>562</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspLUT2</td>
<td>141</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspLUT3</td>
<td>175</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspLUT4</td>
<td>246</td>
</tr>
<tr>
<td class="label"><b>ALU </b></td>
<td>88</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspALU</td>
<td>88</td>
</tr>
<tr>
<td class="label"><b>DSP </b></td>
<td>2</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspMULTADDALU18X18</td>
<td>2</td>
</tr>
<tr>
<td class="label"><b>BSRAM </b></td>
<td>6</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspDPB</td>
<td>4</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbsppROM</td>
<td>2</td>
</tr>
</table>
<h2><a name="utilization">Resource Utilization Summary</a></h2>
<table class="summary_table">
<tr>
<td class="label"><b>Resource</b></td>
<td><b>Usage</b></td>
<td><b>Utilization</b></td>
</tr>
<tr>
<td class="label">Logic</td>
<td>650(562 LUTs, 88 ALUs) / 20736</td>
<td>4%</td>
</tr>
<tr>
<td class="label">Register</td>
<td>211 / 16173</td>
<td>2%</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp--Register as Latch</td>
<td>0 / 16173</td>
<td>0%</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp--Register as FF</td>
<td>211 / 16173</td>
<td>2%</td>
</tr>
<tr>
<td class="label">BSRAM</td>
<td>6 / 46</td>
<td>14%</td>
</tr>
</table>
<h1><a name="timing">Timing</a></h1>
<h2><a name="clock">Clock Summary:</a></h2>
<table class="summary_table">
<tr>
<th>Clock Name</th>
<th>Type</th>
<th>Period</th>
<th>Frequency(MHz)</th>
<th>Rise</th>
<th>Fall</th>
<th>Source</th>
<th>Master</th>
<th>Object</th>
</tr>
<tr>
<td>clk</td>
<td>Base</td>
<td>10.000</td>
<td>100.0</td>
<td>0.000</td>
<td>5.000</td>
<td> </td>
<td> </td>
<td>clk_ibuf/I </td>
</tr>
</table>
<h2><a name="performance">Max Frequency Summary:</a></h2>
<table class="summary_table">
<tr>
<th>No.</th>
<th>Clock Name</th>
<th>Constraint</th>
<th>Actual Fmax</th>
<th>Logic Level</th>
<th>Entity</th>
</tr>
<tr>
<td>1</td>
<td>clk</td>
<td>100.0(MHz)</td>
<td>138.8(MHz)</td>
<td>10</td>
<td>TOP</td>
</tr>
</table>
<h2><a name="detail timing">Detail Timing Paths Information</a></h2>
<h3>Path&nbsp1</h3>
<b>Path Summary:</b></br>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>2.794</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>8.034</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>10.828</td>
</tr>
<tr>
<td class="label">From</td>
<td>fft_top_inst/tf_inst/ctrl_inst/btfy_cnt_2_s1</td>
</tr>
<tr>
<td class="label">To</td>
<td>fft_top_inst/tf_inst/twBtfyParm_inst/b_r_8_s1</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>clk[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>clk[R]</td>
</tr>
</table>
<b>Data Arrival Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>clk</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>0.683</td>
<td>0.683</td>
<td>tINS</td>
<td>RR</td>
<td>223</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>0.863</td>
<td>0.180</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>fft_top_inst/tf_inst/ctrl_inst/btfy_cnt_2_s1/CLK</td>
</tr>
<tr>
<td>1.095</td>
<td>0.232</td>
<td>tC2Q</td>
<td>RF</td>
<td>4</td>
<td>fft_top_inst/tf_inst/ctrl_inst/btfy_cnt_2_s1/Q</td>
</tr>
<tr>
<td>1.332</td>
<td>0.237</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/ctrl_inst/isBS0_s4/I1</td>
</tr>
<tr>
<td>1.887</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>4</td>
<td>fft_top_inst/tf_inst/ctrl_inst/isBS0_s4/F</td>
</tr>
<tr>
<td>2.124</td>
<td>0.237</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/ctrl_inst/isBS0_s2/I1</td>
</tr>
<tr>
<td>2.679</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/ctrl_inst/isBS0_s2/F</td>
</tr>
<tr>
<td>2.916</td>
<td>0.237</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/ctrl_inst/isBS2_s1/I2</td>
</tr>
<tr>
<td>3.369</td>
<td>0.453</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/ctrl_inst/isBS2_s1/F</td>
</tr>
<tr>
<td>3.606</td>
<td>0.237</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/ctrl_inst/twBtfy_ea_s0/I3</td>
</tr>
<tr>
<td>3.977</td>
<td>0.371</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/ctrl_inst/twBtfy_ea_s0/F</td>
</tr>
<tr>
<td>4.214</td>
<td>0.237</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/ctrl_inst/twBtfy_ea_s/I0</td>
</tr>
<tr>
<td>4.731</td>
<td>0.517</td>
<td>tINS</td>
<td>FF</td>
<td>33</td>
<td>fft_top_inst/tf_inst/ctrl_inst/twBtfy_ea_s/F</td>
</tr>
<tr>
<td>4.968</td>
<td>0.237</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/twBtfyParm_inst/n349_s1/I1</td>
</tr>
<tr>
<td>5.523</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>4</td>
<td>fft_top_inst/tf_inst/twBtfyParm_inst/n349_s1/F</td>
</tr>
<tr>
<td>5.760</td>
<td>0.237</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/twBtfyParm_inst/n347_s2/I1</td>
</tr>
<tr>
<td>6.315</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>3</td>
<td>fft_top_inst/tf_inst/twBtfyParm_inst/n347_s2/F</td>
</tr>
<tr>
<td>6.552</td>
<td>0.237</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/twBtfyParm_inst/n344_s1/I2</td>
</tr>
<tr>
<td>7.005</td>
<td>0.453</td>
<td>tINS</td>
<td>FF</td>
<td>3</td>
<td>fft_top_inst/tf_inst/twBtfyParm_inst/n344_s1/F</td>
</tr>
<tr>
<td>7.242</td>
<td>0.237</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/twBtfyParm_inst/n345_s0/I1</td>
</tr>
<tr>
<td>7.797</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/twBtfyParm_inst/n345_s0/F</td>
</tr>
<tr>
<td>8.034</td>
<td>0.237</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/twBtfyParm_inst/b_r_8_s1/D</td>
</tr>
</table>
<b>Data Required Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>clk</td>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>10.682</td>
<td>0.683</td>
<td>tINS</td>
<td>RR</td>
<td>223</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>10.863</td>
<td>0.180</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>fft_top_inst/tf_inst/twBtfyParm_inst/b_r_8_s1/CLK</td>
</tr>
<tr>
<td>10.828</td>
<td>-0.035</td>
<td>tSu</td>
<td> </td>
<td>1</td>
<td>fft_top_inst/tf_inst/twBtfyParm_inst/b_r_8_s1</td>
</tr>
</table>
<b>Path Statistics:</b>
<table class="summary_table">
<tr>
<td class="label">Clock Skew:</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Setup Relationship:</td>
<td>10.000</td>
</tr>
<tr>
<td class="label">Logic Level:</td>
<td>10</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay:</td><td> cell: 0.683, 79.130%; route: 0.180, 20.870%</td></tr>
<tr>
<td class="label">Arrival Data Path Delay:</td><td> cell: 4.569, 63.715%; route: 2.370, 33.050%; tC2Q: 0.232, 3.235%</td></tr>
<tr>
<td class="label">Required Clock Path Delay:</td><td> cell: 0.683, 79.130%; route: 0.180, 20.870%</td></tr>
</table>
<br/>
<h3>Path&nbsp2</h3>
<b>Path Summary:</b></br>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>2.832</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>7.996</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>10.828</td>
</tr>
<tr>
<td class="label">From</td>
<td>fft_top_inst/tf_inst/ctrl_inst/btfy_cnt_2_s1</td>
</tr>
<tr>
<td class="label">To</td>
<td>fft_top_inst/tf_inst/twBtfyParm_inst/b_r_9_s1</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>clk[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>clk[R]</td>
</tr>
</table>
<b>Data Arrival Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>clk</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>0.683</td>
<td>0.683</td>
<td>tINS</td>
<td>RR</td>
<td>223</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>0.863</td>
<td>0.180</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>fft_top_inst/tf_inst/ctrl_inst/btfy_cnt_2_s1/CLK</td>
</tr>
<tr>
<td>1.095</td>
<td>0.232</td>
<td>tC2Q</td>
<td>RF</td>
<td>4</td>
<td>fft_top_inst/tf_inst/ctrl_inst/btfy_cnt_2_s1/Q</td>
</tr>
<tr>
<td>1.332</td>
<td>0.237</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/ctrl_inst/isBS0_s4/I1</td>
</tr>
<tr>
<td>1.887</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>4</td>
<td>fft_top_inst/tf_inst/ctrl_inst/isBS0_s4/F</td>
</tr>
<tr>
<td>2.124</td>
<td>0.237</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/ctrl_inst/isBS0_s2/I1</td>
</tr>
<tr>
<td>2.679</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/ctrl_inst/isBS0_s2/F</td>
</tr>
<tr>
<td>2.916</td>
<td>0.237</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/ctrl_inst/isBS2_s1/I2</td>
</tr>
<tr>
<td>3.369</td>
<td>0.453</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/ctrl_inst/isBS2_s1/F</td>
</tr>
<tr>
<td>3.606</td>
<td>0.237</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/ctrl_inst/twBtfy_ea_s0/I3</td>
</tr>
<tr>
<td>3.977</td>
<td>0.371</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/ctrl_inst/twBtfy_ea_s0/F</td>
</tr>
<tr>
<td>4.214</td>
<td>0.237</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/ctrl_inst/twBtfy_ea_s/I0</td>
</tr>
<tr>
<td>4.731</td>
<td>0.517</td>
<td>tINS</td>
<td>FF</td>
<td>33</td>
<td>fft_top_inst/tf_inst/ctrl_inst/twBtfy_ea_s/F</td>
</tr>
<tr>
<td>4.968</td>
<td>0.237</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/twBtfyParm_inst/n349_s1/I1</td>
</tr>
<tr>
<td>5.523</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>4</td>
<td>fft_top_inst/tf_inst/twBtfyParm_inst/n349_s1/F</td>
</tr>
<tr>
<td>5.760</td>
<td>0.237</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/twBtfyParm_inst/n347_s2/I1</td>
</tr>
<tr>
<td>6.315</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>3</td>
<td>fft_top_inst/tf_inst/twBtfyParm_inst/n347_s2/F</td>
</tr>
<tr>
<td>6.552</td>
<td>0.237</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/twBtfyParm_inst/n344_s1/I2</td>
</tr>
<tr>
<td>7.005</td>
<td>0.453</td>
<td>tINS</td>
<td>FF</td>
<td>3</td>
<td>fft_top_inst/tf_inst/twBtfyParm_inst/n344_s1/F</td>
</tr>
<tr>
<td>7.242</td>
<td>0.237</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/twBtfyParm_inst/n344_s0/I0</td>
</tr>
<tr>
<td>7.759</td>
<td>0.517</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/twBtfyParm_inst/n344_s0/F</td>
</tr>
<tr>
<td>7.996</td>
<td>0.237</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/twBtfyParm_inst/b_r_9_s1/D</td>
</tr>
</table>
<b>Data Required Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>clk</td>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>10.682</td>
<td>0.683</td>
<td>tINS</td>
<td>RR</td>
<td>223</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>10.863</td>
<td>0.180</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>fft_top_inst/tf_inst/twBtfyParm_inst/b_r_9_s1/CLK</td>
</tr>
<tr>
<td>10.828</td>
<td>-0.035</td>
<td>tSu</td>
<td> </td>
<td>1</td>
<td>fft_top_inst/tf_inst/twBtfyParm_inst/b_r_9_s1</td>
</tr>
</table>
<b>Path Statistics:</b>
<table class="summary_table">
<tr>
<td class="label">Clock Skew:</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Setup Relationship:</td>
<td>10.000</td>
</tr>
<tr>
<td class="label">Logic Level:</td>
<td>10</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay:</td><td> cell: 0.683, 79.130%; route: 0.180, 20.870%</td></tr>
<tr>
<td class="label">Arrival Data Path Delay:</td><td> cell: 4.531, 63.522%; route: 2.370, 33.226%; tC2Q: 0.232, 3.252%</td></tr>
<tr>
<td class="label">Required Clock Path Delay:</td><td> cell: 0.683, 79.130%; route: 0.180, 20.870%</td></tr>
</table>
<br/>
<h3>Path&nbsp3</h3>
<b>Path Summary:</b></br>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>2.876</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>7.951</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>10.828</td>
</tr>
<tr>
<td class="label">From</td>
<td>fft_top_inst/tf_inst/ctrl_inst/btfy_cnt_2_s1</td>
</tr>
<tr>
<td class="label">To</td>
<td>fft_top_inst/tf_inst/twBtfyParm_inst/g_r_9_s1</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>clk[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>clk[R]</td>
</tr>
</table>
<b>Data Arrival Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>clk</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>0.683</td>
<td>0.683</td>
<td>tINS</td>
<td>RR</td>
<td>223</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>0.863</td>
<td>0.180</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>fft_top_inst/tf_inst/ctrl_inst/btfy_cnt_2_s1/CLK</td>
</tr>
<tr>
<td>1.095</td>
<td>0.232</td>
<td>tC2Q</td>
<td>RF</td>
<td>4</td>
<td>fft_top_inst/tf_inst/ctrl_inst/btfy_cnt_2_s1/Q</td>
</tr>
<tr>
<td>1.332</td>
<td>0.237</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/ctrl_inst/isBS0_s4/I1</td>
</tr>
<tr>
<td>1.887</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>4</td>
<td>fft_top_inst/tf_inst/ctrl_inst/isBS0_s4/F</td>
</tr>
<tr>
<td>2.124</td>
<td>0.237</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/ctrl_inst/isBS0_s2/I1</td>
</tr>
<tr>
<td>2.679</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/ctrl_inst/isBS0_s2/F</td>
</tr>
<tr>
<td>2.916</td>
<td>0.237</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/ctrl_inst/isBS2_s1/I2</td>
</tr>
<tr>
<td>3.369</td>
<td>0.453</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/ctrl_inst/isBS2_s1/F</td>
</tr>
<tr>
<td>3.606</td>
<td>0.237</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/ctrl_inst/twBtfy_ea_s0/I3</td>
</tr>
<tr>
<td>3.977</td>
<td>0.371</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/ctrl_inst/twBtfy_ea_s0/F</td>
</tr>
<tr>
<td>4.214</td>
<td>0.237</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/ctrl_inst/twBtfy_ea_s/I0</td>
</tr>
<tr>
<td>4.731</td>
<td>0.517</td>
<td>tINS</td>
<td>FF</td>
<td>33</td>
<td>fft_top_inst/tf_inst/ctrl_inst/twBtfy_ea_s/F</td>
</tr>
<tr>
<td>4.968</td>
<td>0.237</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/twBtfyParm_inst/n221_s1/I1</td>
</tr>
<tr>
<td>5.523</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>12</td>
<td>fft_top_inst/tf_inst/twBtfyParm_inst/n221_s1/F</td>
</tr>
<tr>
<td>5.760</td>
<td>0.237</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/twBtfyParm_inst/n223_s1/I3</td>
</tr>
<tr>
<td>6.131</td>
<td>0.371</td>
<td>tINS</td>
<td>FF</td>
<td>3</td>
<td>fft_top_inst/tf_inst/twBtfyParm_inst/n223_s1/F</td>
</tr>
<tr>
<td>6.368</td>
<td>0.237</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/twBtfyParm_inst/n217_s1/I1</td>
</tr>
<tr>
<td>6.923</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>2</td>
<td>fft_top_inst/tf_inst/twBtfyParm_inst/n217_s1/F</td>
</tr>
<tr>
<td>7.160</td>
<td>0.237</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/twBtfyParm_inst/n217_s0/I1</td>
</tr>
<tr>
<td>7.714</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/twBtfyParm_inst/n217_s0/F</td>
</tr>
<tr>
<td>7.951</td>
<td>0.237</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/twBtfyParm_inst/g_r_9_s1/D</td>
</tr>
</table>
<b>Data Required Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>clk</td>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>10.682</td>
<td>0.683</td>
<td>tINS</td>
<td>RR</td>
<td>223</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>10.863</td>
<td>0.180</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>fft_top_inst/tf_inst/twBtfyParm_inst/g_r_9_s1/CLK</td>
</tr>
<tr>
<td>10.828</td>
<td>-0.035</td>
<td>tSu</td>
<td> </td>
<td>1</td>
<td>fft_top_inst/tf_inst/twBtfyParm_inst/g_r_9_s1</td>
</tr>
</table>
<b>Path Statistics:</b>
<table class="summary_table">
<tr>
<td class="label">Clock Skew:</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Setup Relationship:</td>
<td>10.000</td>
</tr>
<tr>
<td class="label">Logic Level:</td>
<td>10</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay:</td><td> cell: 0.683, 79.130%; route: 0.180, 20.870%</td></tr>
<tr>
<td class="label">Arrival Data Path Delay:</td><td> cell: 4.487, 63.295%; route: 2.370, 33.432%; tC2Q: 0.232, 3.273%</td></tr>
<tr>
<td class="label">Required Clock Path Delay:</td><td> cell: 0.683, 79.130%; route: 0.180, 20.870%</td></tr>
</table>
<br/>
<h3>Path&nbsp4</h3>
<b>Path Summary:</b></br>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>2.978</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>7.850</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>10.828</td>
</tr>
<tr>
<td class="label">From</td>
<td>fft_top_inst/tf_inst/ctrl_inst/btfy_cnt_2_s1</td>
</tr>
<tr>
<td class="label">To</td>
<td>fft_top_inst/tf_inst/twBtfyParm_inst/b_r_7_s1</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>clk[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>clk[R]</td>
</tr>
</table>
<b>Data Arrival Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>clk</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>0.683</td>
<td>0.683</td>
<td>tINS</td>
<td>RR</td>
<td>223</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>0.863</td>
<td>0.180</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>fft_top_inst/tf_inst/ctrl_inst/btfy_cnt_2_s1/CLK</td>
</tr>
<tr>
<td>1.095</td>
<td>0.232</td>
<td>tC2Q</td>
<td>RF</td>
<td>4</td>
<td>fft_top_inst/tf_inst/ctrl_inst/btfy_cnt_2_s1/Q</td>
</tr>
<tr>
<td>1.332</td>
<td>0.237</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/ctrl_inst/isBS0_s4/I1</td>
</tr>
<tr>
<td>1.887</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>4</td>
<td>fft_top_inst/tf_inst/ctrl_inst/isBS0_s4/F</td>
</tr>
<tr>
<td>2.124</td>
<td>0.237</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/ctrl_inst/isBS0_s2/I1</td>
</tr>
<tr>
<td>2.679</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/ctrl_inst/isBS0_s2/F</td>
</tr>
<tr>
<td>2.916</td>
<td>0.237</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/ctrl_inst/isBS2_s1/I2</td>
</tr>
<tr>
<td>3.369</td>
<td>0.453</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/ctrl_inst/isBS2_s1/F</td>
</tr>
<tr>
<td>3.606</td>
<td>0.237</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/ctrl_inst/twBtfy_ea_s0/I3</td>
</tr>
<tr>
<td>3.977</td>
<td>0.371</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/ctrl_inst/twBtfy_ea_s0/F</td>
</tr>
<tr>
<td>4.214</td>
<td>0.237</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/ctrl_inst/twBtfy_ea_s/I0</td>
</tr>
<tr>
<td>4.731</td>
<td>0.517</td>
<td>tINS</td>
<td>FF</td>
<td>33</td>
<td>fft_top_inst/tf_inst/ctrl_inst/twBtfy_ea_s/F</td>
</tr>
<tr>
<td>4.968</td>
<td>0.237</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/twBtfyParm_inst/n349_s1/I1</td>
</tr>
<tr>
<td>5.523</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>4</td>
<td>fft_top_inst/tf_inst/twBtfyParm_inst/n349_s1/F</td>
</tr>
<tr>
<td>5.760</td>
<td>0.237</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/twBtfyParm_inst/n347_s2/I1</td>
</tr>
<tr>
<td>6.315</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>3</td>
<td>fft_top_inst/tf_inst/twBtfyParm_inst/n347_s2/F</td>
</tr>
<tr>
<td>6.552</td>
<td>0.237</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/twBtfyParm_inst/n344_s1/I2</td>
</tr>
<tr>
<td>7.005</td>
<td>0.453</td>
<td>tINS</td>
<td>FF</td>
<td>3</td>
<td>fft_top_inst/tf_inst/twBtfyParm_inst/n344_s1/F</td>
</tr>
<tr>
<td>7.242</td>
<td>0.237</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/twBtfyParm_inst/n346_s1/I3</td>
</tr>
<tr>
<td>7.613</td>
<td>0.371</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/twBtfyParm_inst/n346_s1/F</td>
</tr>
<tr>
<td>7.850</td>
<td>0.237</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/twBtfyParm_inst/b_r_7_s1/D</td>
</tr>
</table>
<b>Data Required Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>clk</td>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>10.682</td>
<td>0.683</td>
<td>tINS</td>
<td>RR</td>
<td>223</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>10.863</td>
<td>0.180</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>fft_top_inst/tf_inst/twBtfyParm_inst/b_r_7_s1/CLK</td>
</tr>
<tr>
<td>10.828</td>
<td>-0.035</td>
<td>tSu</td>
<td> </td>
<td>1</td>
<td>fft_top_inst/tf_inst/twBtfyParm_inst/b_r_7_s1</td>
</tr>
</table>
<b>Path Statistics:</b>
<table class="summary_table">
<tr>
<td class="label">Clock Skew:</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Setup Relationship:</td>
<td>10.000</td>
</tr>
<tr>
<td class="label">Logic Level:</td>
<td>10</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay:</td><td> cell: 0.683, 79.130%; route: 0.180, 20.870%</td></tr>
<tr>
<td class="label">Arrival Data Path Delay:</td><td> cell: 4.385, 62.760%; route: 2.370, 33.920%; tC2Q: 0.232, 3.320%</td></tr>
<tr>
<td class="label">Required Clock Path Delay:</td><td> cell: 0.683, 79.130%; route: 0.180, 20.870%</td></tr>
</table>
<br/>
<h3>Path&nbsp5</h3>
<b>Path Summary:</b></br>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>2.978</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>7.850</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>10.828</td>
</tr>
<tr>
<td class="label">From</td>
<td>fft_top_inst/tf_inst/ctrl_inst/btfy_cnt_2_s1</td>
</tr>
<tr>
<td class="label">To</td>
<td>fft_top_inst/tf_inst/twBtfyParm_inst/g_r_8_s1</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>clk[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>clk[R]</td>
</tr>
</table>
<b>Data Arrival Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>clk</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>0.683</td>
<td>0.683</td>
<td>tINS</td>
<td>RR</td>
<td>223</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>0.863</td>
<td>0.180</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>fft_top_inst/tf_inst/ctrl_inst/btfy_cnt_2_s1/CLK</td>
</tr>
<tr>
<td>1.095</td>
<td>0.232</td>
<td>tC2Q</td>
<td>RF</td>
<td>4</td>
<td>fft_top_inst/tf_inst/ctrl_inst/btfy_cnt_2_s1/Q</td>
</tr>
<tr>
<td>1.332</td>
<td>0.237</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/ctrl_inst/isBS0_s4/I1</td>
</tr>
<tr>
<td>1.887</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>4</td>
<td>fft_top_inst/tf_inst/ctrl_inst/isBS0_s4/F</td>
</tr>
<tr>
<td>2.124</td>
<td>0.237</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/ctrl_inst/isBS0_s2/I1</td>
</tr>
<tr>
<td>2.679</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/ctrl_inst/isBS0_s2/F</td>
</tr>
<tr>
<td>2.916</td>
<td>0.237</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/ctrl_inst/isBS2_s1/I2</td>
</tr>
<tr>
<td>3.369</td>
<td>0.453</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/ctrl_inst/isBS2_s1/F</td>
</tr>
<tr>
<td>3.606</td>
<td>0.237</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/ctrl_inst/twBtfy_ea_s0/I3</td>
</tr>
<tr>
<td>3.977</td>
<td>0.371</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/ctrl_inst/twBtfy_ea_s0/F</td>
</tr>
<tr>
<td>4.214</td>
<td>0.237</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/ctrl_inst/twBtfy_ea_s/I0</td>
</tr>
<tr>
<td>4.731</td>
<td>0.517</td>
<td>tINS</td>
<td>FF</td>
<td>33</td>
<td>fft_top_inst/tf_inst/ctrl_inst/twBtfy_ea_s/F</td>
</tr>
<tr>
<td>4.968</td>
<td>0.237</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/twBtfyParm_inst/n221_s1/I1</td>
</tr>
<tr>
<td>5.523</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>12</td>
<td>fft_top_inst/tf_inst/twBtfyParm_inst/n221_s1/F</td>
</tr>
<tr>
<td>5.760</td>
<td>0.237</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/twBtfyParm_inst/n223_s1/I3</td>
</tr>
<tr>
<td>6.131</td>
<td>0.371</td>
<td>tINS</td>
<td>FF</td>
<td>3</td>
<td>fft_top_inst/tf_inst/twBtfyParm_inst/n223_s1/F</td>
</tr>
<tr>
<td>6.368</td>
<td>0.237</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/twBtfyParm_inst/n217_s1/I1</td>
</tr>
<tr>
<td>6.923</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>2</td>
<td>fft_top_inst/tf_inst/twBtfyParm_inst/n217_s1/F</td>
</tr>
<tr>
<td>7.160</td>
<td>0.237</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/twBtfyParm_inst/n218_s0/I2</td>
</tr>
<tr>
<td>7.613</td>
<td>0.453</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/twBtfyParm_inst/n218_s0/F</td>
</tr>
<tr>
<td>7.850</td>
<td>0.237</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>fft_top_inst/tf_inst/twBtfyParm_inst/g_r_8_s1/D</td>
</tr>
</table>
<b>Data Required Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>clk</td>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>10.682</td>
<td>0.683</td>
<td>tINS</td>
<td>RR</td>
<td>223</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>10.863</td>
<td>0.180</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>fft_top_inst/tf_inst/twBtfyParm_inst/g_r_8_s1/CLK</td>
</tr>
<tr>
<td>10.828</td>
<td>-0.035</td>
<td>tSu</td>
<td> </td>
<td>1</td>
<td>fft_top_inst/tf_inst/twBtfyParm_inst/g_r_8_s1</td>
</tr>
</table>
<b>Path Statistics:</b>
<table class="summary_table">
<tr>
<td class="label">Clock Skew:</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Setup Relationship:</td>
<td>10.000</td>
</tr>
<tr>
<td class="label">Logic Level:</td>
<td>10</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay:</td><td> cell: 0.683, 79.130%; route: 0.180, 20.870%</td></tr>
<tr>
<td class="label">Arrival Data Path Delay:</td><td> cell: 4.385, 62.760%; route: 2.370, 33.920%; tC2Q: 0.232, 3.320%</td></tr>
<tr>
<td class="label">Required Clock Path Delay:</td><td> cell: 0.683, 79.130%; route: 0.180, 20.870%</td></tr>
</table>
<br/>
</div><!-- content -->
</div><!-- main_wrapper -->
</body>
</html>
