<?xml version="1" encoding="UTF-8"?>
<GAO_CONFIG>
    <Version>3.0</Version>
    <Mode>Standard</Mode>
    <AoCore index="0" sample_clock="video_clk" trig_type="0" storage_depth="16384" window_num="1" capture_amount="16384" trigger_pos="10" module_name="top" force_trigger_by_falling_edge="false">
        <SignalList>
            <Signal>cmos_vsync</Signal>
            <Signal>cmos_href</Signal>
            <Signal>video_timing_data_m0/video_vs</Signal>
            <Signal>video_timing_data_m0/video_de</Signal>
            <Bus name="video_timing_data_m0/color_bar_m0/active_y[9:0]">
                <Signal>video_timing_data_m0/color_bar_m0/active_y[9]</Signal>
                <Signal>video_timing_data_m0/color_bar_m0/active_y[8]</Signal>
                <Signal>video_timing_data_m0/color_bar_m0/active_y[7]</Signal>
                <Signal>video_timing_data_m0/color_bar_m0/active_y[6]</Signal>
                <Signal>video_timing_data_m0/color_bar_m0/active_y[5]</Signal>
                <Signal>video_timing_data_m0/color_bar_m0/active_y[4]</Signal>
                <Signal>video_timing_data_m0/color_bar_m0/active_y[3]</Signal>
                <Signal>video_timing_data_m0/color_bar_m0/active_y[2]</Signal>
                <Signal>video_timing_data_m0/color_bar_m0/active_y[1]</Signal>
                <Signal>video_timing_data_m0/color_bar_m0/active_y[0]</Signal>
            </Bus>
        </SignalList>
        <Triggers>
            <Trigger index="0">
                <SignalList>
                    <Signal>cmos_vsync</Signal>
                </SignalList>
            </Trigger>
            <Trigger index="1"/>
            <Trigger index="2"/>
            <Trigger index="3"/>
            <Trigger index="4"/>
            <Trigger index="5"/>
            <Trigger index="6"/>
            <Trigger index="7"/>
            <Trigger index="8"/>
            <Trigger index="9"/>
            <Trigger index="10"/>
            <Trigger index="11"/>
            <Trigger index="12"/>
            <Trigger index="13"/>
            <Trigger index="14"/>
            <Trigger index="15"/>
        </Triggers>
        <MatchUnits>
            <MatchUnit index="0" enabled="1" match_type="0" counter_enable="0" counter_width="2" counter="2" countinuous="0" func="0" value0="1" value1="0" trigger="0"/>
            <MatchUnit index="1" enabled="0" match_type="0" counter_enable="0" counter_width="2" counter="2" countinuous="0" func="0" value0="" value1=""/>
            <MatchUnit index="2" enabled="0" match_type="0" counter_enable="0" counter_width="2" counter="2" countinuous="0" func="0" value0="" value1=""/>
            <MatchUnit index="3" enabled="0" match_type="0" counter_enable="0" counter_width="2" counter="2" countinuous="0" func="0" value0="" value1=""/>
            <MatchUnit index="4" enabled="0" match_type="0" counter_enable="0" counter_width="2" counter="2" countinuous="0" func="0" value0="" value1=""/>
            <MatchUnit index="5" enabled="0" match_type="0" counter_enable="0" counter_width="2" counter="2" countinuous="0" func="0" value0="" value1=""/>
            <MatchUnit index="6" enabled="0" match_type="0" counter_enable="0" counter_width="2" counter="2" countinuous="0" func="0" value0="" value1=""/>
            <MatchUnit index="7" enabled="0" match_type="0" counter_enable="0" counter_width="2" counter="2" countinuous="0" func="0" value0="" value1=""/>
            <MatchUnit index="8" enabled="0" match_type="0" counter_enable="0" counter_width="2" counter="2" countinuous="0" func="0" value0="" value1=""/>
            <MatchUnit index="9" enabled="0" match_type="0" counter_enable="0" counter_width="2" counter="2" countinuous="0" func="0" value0="" value1=""/>
            <MatchUnit index="10" enabled="0" match_type="0" counter_enable="0" counter_width="2" counter="2" countinuous="0" func="0" value0="" value1=""/>
            <MatchUnit index="11" enabled="0" match_type="0" counter_enable="0" counter_width="2" counter="2" countinuous="0" func="0" value0="" value1=""/>
            <MatchUnit index="12" enabled="0" match_type="0" counter_enable="0" counter_width="2" counter="2" countinuous="0" func="0" value0="" value1=""/>
            <MatchUnit index="13" enabled="0" match_type="0" counter_enable="0" counter_width="2" counter="2" countinuous="0" func="0" value0="" value1=""/>
            <MatchUnit index="14" enabled="0" match_type="0" counter_enable="0" counter_width="2" counter="2" countinuous="0" func="0" value0="" value1=""/>
            <MatchUnit index="15" enabled="0" match_type="0" counter_enable="0" counter_width="2" counter="2" countinuous="0" func="0" value0="" value1=""/>
        </MatchUnits>
        <Expressions type="Static">
            <Expression>M0</Expression>
        </Expressions>
    </AoCore>
    <GAO_ID>1000000011000100</GAO_ID>
</GAO_CONFIG>
