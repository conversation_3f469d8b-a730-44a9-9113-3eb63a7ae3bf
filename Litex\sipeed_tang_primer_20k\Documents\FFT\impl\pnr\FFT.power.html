<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//ENhttp://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>Power Analysis Report</title>
<style type="text/css">
body { font-family: Verdana, Arial, sans-serif; font-size: 12px; }
div#main_wrapper { width: 100%; }
div#content { margin-left: 350px; margin-right: 30px; }
div#catalog_wrapper {position: fixed; top: 30px; width: 350px; float: left; }
div#catalog ul { list-style-type: none; }
div#catalog li { text-align: left; list-style-type:circle; color: #0084ff; margin-top: 3px; margin-bottom: 3px; }
div#catalog a { display:inline-block; text-decoration: none; color: #0084ff; font-weight: bold; padding: 3px; }
div#catalog a:visited { color: #0084ff; }
div#catalog a:hover { color: #fff; background: #0084ff; }
hr { margin-top: 30px; margin-bottom: 30px; }
h1, h3 { text-align: center; }
h1 {margin-top: 50px; }
table, th, td {white-space:pre;  border: 1px solid #aaa; }
table { border-collapse:collapse; margin-top: 10px; margin-bottom: 20px; width: 100%; }
th, td { padding: 5px 5px 5px 5px; }
th { color: #fff; font-weight: bold; background-color: #0084ff; }
table.summary_table td.label { width: 24%; min-width: 200px; background-color: #dee8f4; }
table.thermal_table td.label { width: 24%; min-width: 200px; background-color: #dee8f4; }
table.Configure_table td.label { width: 24%; min-width: 200px; background-color: #dee8f4; }
table.detail_table th.label {  min-width: 8%; width: 8%; }
</style>
</head>
<body>
<div id="main_wrapper">
<div id="catalog_wrapper">
<div id="catalog">
<ul>
<li><a href="#Message" style=" font-size: 16px;">Power Messages</a>
<ul>
<li><a href="#Configure_Info" style=" font-size: 14px;">Configure Information</a></li>
</ul>
</li>
<li><a href="#Summary" style=" font-size: 16px;">Power Summary</a>
<ul>
<li><a href="#Power_Info" style=" font-size: 14px;">Power Information</a></li>
<li><a href="#Thermal_Info" style=" font-size: 14px;">Thermal Information</a></li>
<li><a href="#Supply_Summary" style=" font-size: 14px;">Supply Information</a></li>
</ul>
</li>
<li><a href="#Detail" style=" font-size: 16px;">Power Details</a>
<ul>
<li><a href="#By_Block_Type" style=" font-size: 14px;">Power By Block Type</a></li>
<li><a href="#By_Hierarchy" style=" font-size: 14px;">Power By Hierarchy</a></li>
<li><a href="#By_Clock_Domain" style=" font-size: 14px;">Power By Clock Domain</a></li>
</ul>
</li>
</ul>
</div><!-- catalog -->
</div><!-- catalog_wrapper -->
<div id="content">
<h1><a name="Message">Power Messages</a></h1>
<table class="summary_table">
<tr>
<td class="label">Report Title</td>
<td>Power Analysis Report</td>
</tr>
<tr>
<td class="label">Design File</td>
<td>G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\Documents\FFT\impl\gwsynthesis\FFT.vg</td>
</tr>
<tr>
<td class="label">Physical Constraints File</td>
<td>G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\Documents\FFT\src\FFT.cst</td>
</tr>
<tr>
<td class="label">Timing Constraints File</td>
<td>G:\Gowin\workspace\TangPrimer-20K-example-main\Litex\sipeed_tang_primer_20k\Documents\FFT\src\FFT.sdc</td>
</tr>
<tr>
<td class="label">Tool Version</td>
<td>V1.9.11.03 Education</td>
</tr>
<tr>
<td class="label">Part Number</td>
<td>GW2A-LV18PG256C8/I7</td>
</tr>
<tr>
<td class="label">Device</td>
<td>GW2A-18</td>
</tr>
<tr>
<td class="label">Device Version</td>
<td>C</td>
</tr>
<tr>
<td class="label">Created Time</td>
<td>Wed Jul 30 11:38:53 2025
</td>
</tr>
<tr>
<td class="label">Legal Announcement</td>
<td>Copyright (C)2014-2025 Gowin Semiconductor Corporation. All rights reserved.</td>
</tr>
</table>
<h2><a name="Configure_Info">Configure Information:</a></h2>
<table class="summary_table">
<tr>
<td class="label">Grade</td>
<td>Commercial</td>
</tr>
<tr>
<td class="label">Process</td>
<td>Typical</td>
</tr>
<tr>
<td class="label">Ambient Temperature</td>
<td>25.000
</tr>
<tr>
<td class="label">Use Custom Theta JA</td>
<td>false</td>
</tr>
<tr>
<td class="label">Heat Sink</td>
<td>None</td>
</tr>
<tr>
<td class="label">Air Flow</td>
<td>LFM_0</td>
</tr>
<tr>
<td class="label">Use Custom Theta SA</td>
<td>false</td>
</tr>
<tr>
<td class="label">Board Thermal Model</td>
<td>None</td>
</tr>
<tr>
<td class="label">Use Custom Theta JB</td>
<td>false</td>
</tr>
<tr>
<td class="label">Related Vcd File</td>
<td></td>
</tr>
<tr>
<td class="label">Related Saif File</td>
<td></td>
</tr>
<tr>
<td class="label">Filter Glitches</td>
<td>false</td>
</tr>
<tr>
<td class="label">Default IO Toggle Rate</td>
<td>0.125</td>
</tr>
<tr>
<td class="label">Default Remain Toggle Rate</td>
<td>0.125</td>
</tr>
</table>
<h1><a name="Summary">Power Summary</a></h1>
<h2><a name="Power_Info">Power Information:</a></h2>
<table class="summary_table">
<tr>
<td class="label">Total Power (mW)</td>
<td>131.908</td>
</tr>
<tr>
<td class="label">Quiescent Power (mW)</td>
<td>121.126</td>
</tr>
<tr>
<td class="label">Dynamic Power (mW)</td>
<td>10.782</td>
</tr>
</table>
<h2><a name="Thermal_Info">Thermal Information:</a></h2>
<table class="summary_table">
<tr>
<td class="label">Junction Temperature</td>
<td>29.224</td>
</tr>
<tr>
<td class="label">Theta JA</td>
<td>32.020</td>
</tr>
<tr>
<td class="label">Max Allowed Ambient Temperature</td>
<td>80.776</td>
</tr>
</table>
<h2><a name="Supply_Summary">Supply Information:</a></h2>
<table class="summary_table">
<tr>
<th class="label">Voltage Source</th>
<th class="label">Voltage</th>
<th class="label">Dynamic Current(mA)</th>
<th class="label">Quiescent Current(mA)</th>
<th class="label">Power(mW)</th>
</tr>
<tr>
<td>VCC</td>
<td>1.000</td>
<td>6.342</td>
<td>69.921</td>
<td>76.263</td>
</tr>
<tr>
<td>VCCX</td>
<td>3.300</td>
<td>0.873</td>
<td>15.000</td>
<td>52.382</td>
</tr>
<tr>
<td>VCCIO18</td>
<td>1.800</td>
<td>0.865</td>
<td>0.947</td>
<td>3.263</td>
</tr>
</table>
<h1><a name="Detail">Power Details</a></h1>
<h2><a name="By_Block_Type">Power By Block Type:</a></h2>
<table class="detail_table">
<tr>
<th class="label">Block Type</th>
<th class="label">Total Power(mW)</th>
<th class="label">Static Power(mW)</th>
<th class="label">Average Toggle Rate(millions of transitions/sec)</th>
</tr>
<tr>
<td>Logic</td>
<td>0.286</td>
<td>NA</td>
<td>1.905</td>
</tr>
<tr>
<td>IO</td>
<td>19.257
<td>13.944
<td>3.451
</tr>
<tr>
<td>BSRAM</td>
<td>3.423
<td>NA</td>
<td>NA</td>
</tr>
<tr>
<td>PLL</td>
<td>1.046
<td>NA</td>
<td>NA</td>
</tr>
<tr>
<td>DSP</td>
<td>0.697
<td>NA</td>
<td>0.711
</tr>
</table>
<h2><a name="By_Hierarchy">Power By Hierarchy:</a></h2>
<table class="detail_table">
<tr>
<th class="label">Hierarchy Entity</th>
<th class="label">Total Power(mW)</th>
<th class="label">Block Dynamic Power(mW)</th>
</tr>
<tr>
<td>testfft_top</td>
<td>5.452</td>
<td>5.452(5.451)</td>
<tr>
<td>testfft_top/cntInst2/</td>
<td>0.139</td>
<td>0.139(0.000)</td>
<tr>
<td>testfft_top/deUstb_inst/</td>
<td>0.001</td>
<td>0.001(0.000)</td>
<tr>
<td>testfft_top/deUstb_rst/</td>
<td>0.001</td>
<td>0.001(0.000)</td>
<tr>
<td>testfft_top/myfft/</td>
<td>4.111</td>
<td>4.111(4.111)</td>
<tr>
<td>testfft_top/myfft/fft_top_inst/</td>
<td>4.111</td>
<td>4.111(4.103)</td>
<tr>
<td>testfft_top/myfft/fft_top_inst/ld_inst/</td>
<td>0.003</td>
<td>0.003(0.000)</td>
<tr>
<td>testfft_top/myfft/fft_top_inst/mem_inst/</td>
<td>3.008</td>
<td>3.008(3.008)</td>
<tr>
<td>testfft_top/myfft/fft_top_inst/mem_inst/pmem/</td>
<td>1.504</td>
<td>1.504(1.504)</td>
<tr>
<td>testfft_top/myfft/fft_top_inst/mem_inst/pmem/imMem/</td>
<td>0.002</td>
<td>0.002(0.000)</td>
<tr>
<td>testfft_top/myfft/fft_top_inst/mem_inst/pmem/reMem/</td>
<td>1.503</td>
<td>1.503(0.000)</td>
<tr>
<td>testfft_top/myfft/fft_top_inst/mem_inst/qmem/</td>
<td>1.504</td>
<td>1.504(1.504)</td>
<tr>
<td>testfft_top/myfft/fft_top_inst/mem_inst/qmem/imMem/</td>
<td>0.002</td>
<td>0.002(0.000)</td>
<tr>
<td>testfft_top/myfft/fft_top_inst/mem_inst/qmem/reMem/</td>
<td>1.503</td>
<td>1.503(0.000)</td>
<tr>
<td>testfft_top/myfft/fft_top_inst/tf_inst/</td>
<td>1.085</td>
<td>1.085(1.085)</td>
<tr>
<td>testfft_top/myfft/fft_top_inst/tf_inst/btfy_inst/</td>
<td>0.750</td>
<td>0.750(0.697)</td>
<tr>
<td>testfft_top/myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.multX/</td>
<td>0.349</td>
<td>0.349(0.000)</td>
<tr>
<td>testfft_top/myfft/fft_top_inst/tf_inst/btfy_inst/ipdWder.multY/</td>
<td>0.349</td>
<td>0.349(0.000)</td>
<tr>
<td>testfft_top/myfft/fft_top_inst/tf_inst/ctrl_inst/</td>
<td>0.019</td>
<td>0.019(0.000)</td>
<tr>
<td>testfft_top/myfft/fft_top_inst/tf_inst/twBtfyParm_inst/</td>
<td>0.036</td>
<td>0.036(0.000)</td>
<tr>
<td>testfft_top/myfft/fft_top_inst/tf_inst/tw_inst/</td>
<td>0.281</td>
<td>0.281(0.000)</td>
<tr>
<td>testfft_top/myfft/fft_top_inst/udxk_inst/</td>
<td>0.006</td>
<td>0.006(0.000)</td>
<tr>
<td>testfft_top/outCrt/</td>
<td>0.006</td>
<td>0.006(0.000)</td>
<tr>
<td>testfft_top/pll_fft/</td>
<td>1.046</td>
<td>1.046(0.000)</td>
<tr>
<td>testfft_top/u_testfft/</td>
<td>0.148</td>
<td>0.148(0.140)</td>
<tr>
<td>testfft_top/u_testfft/U_GW_ROM/</td>
<td>0.140</td>
<td>0.140(0.000)</td>
</table>
<h2><a name="By_Clock_Domain">Power By Clock Domain:</a></h2>
<table class="detail_table">
<tr>
<th class="label">Clock Domain</th>
<th class="label">Clock Frequency(Mhz)</th>
<th class="label">Total Dynamic Power(mW)</th>
</tr>
<tr>
<td>pll_fft/rpll_inst/CLKOUT.default_gen_clk</td>
<td>10.000</td>
<td>4.264</td>
</tr>
<tr>
<td>clk_in</td>
<td>50.000</td>
<td>1.188</td>
</tr>
<tr>
<td>myclk</td>
<td>100.000</td>
<td>0.018</td>
</tr>
<tr>
<td>NO CLOCK DOMAIN</td>
<td>0.000</td>
<td>0.000</td>
</tr>
</table>
</div><!-- content -->
</div><!-- main_wrapper -->
</body>
</html>
