//Copyright (C)2014-2022 Gowin Semiconductor Corporation.
//All rights reserved.
//File Title: Post-PnR Simulation Model file
//GOWIN Version: V1.9.8.07 Education
//Created Time: Tue Aug 23 21:35:09 2022

`timescale 100 ps/100 ps
module video_fifo(
	Data,
	Reset,
	WrClk,
	RdClk,
	WrEn,
	RdEn,
	Rnum,
	Q,
	Empty,
	Full
);
input [15:0] Data;
input Reset;
input WrClk;
input RdClk;
input WrEn;
input RdEn;
output [12:0] Rnum;
output [15:0] Q;
output Empty;
output Full;
wire [15:0] Data;
wire Empty;
wire Full;
wire GND;
wire [15:0] Q;
wire RdClk;
wire RdEn;
wire Reset;
wire [12:0] Rnum;
wire VCC;
wire WrClk;
wire WrEn;
wire \fifo_inst/n31_5 ;
wire \fifo_inst/n35_3 ;
wire \fifo_inst/n200_3 ;
wire \fifo_inst/wfull_val ;
wire \fifo_inst/n485_3 ;
wire \fifo_inst/n698_3 ;
wire \fifo_inst/wfull_val1 ;
wire \fifo_inst/Equal.wbinnext_0_7 ;
wire \fifo_inst/Equal.rgraynext_3_4 ;
wire \fifo_inst/Equal.rgraynext_6_4 ;
wire \fifo_inst/Equal.rgraynext_8_4 ;
wire \fifo_inst/Equal.rgraynext_10_4 ;
wire \fifo_inst/Equal.rgraynext_11_4 ;
wire \fifo_inst/Equal.wcount_r_11_4 ;
wire \fifo_inst/Equal.wcount_r_9_4 ;
wire \fifo_inst/Equal.wcount_r_7_4 ;
wire \fifo_inst/Equal.wcount_r_3_4 ;
wire \fifo_inst/Equal.wcount_r_1_4 ;
wire \fifo_inst/Equal.wgraynext_5_4 ;
wire \fifo_inst/Equal.wgraynext_8_4 ;
wire \fifo_inst/Equal.wgraynext_11_4 ;
wire \fifo_inst/wfull_val_4 ;
wire \fifo_inst/wfull_val_5 ;
wire \fifo_inst/rbin_num_next_2_8 ;
wire \fifo_inst/Equal.rgraynext_6_5 ;
wire \fifo_inst/Equal.wcount_r_5_5 ;
wire \fifo_inst/Equal.wgraynext_2_5 ;
wire \fifo_inst/Equal.wgraynext_8_5 ;
wire \fifo_inst/Equal.wgraynext_11_5 ;
wire \fifo_inst/wfull_val_6 ;
wire \fifo_inst/wfull_val_7 ;
wire \fifo_inst/wfull_val_8 ;
wire \fifo_inst/wfull_val_9 ;
wire \fifo_inst/wfull_val_10 ;
wire \fifo_inst/wfull_val_11 ;
wire \fifo_inst/Equal.wgraynext_7_6 ;
wire \fifo_inst/Equal.wgraynext_6_7 ;
wire \fifo_inst/Equal.wgraynext_10_6 ;
wire \fifo_inst/Equal.wgraynext_9_7 ;
wire \fifo_inst/Equal.rgraynext_9_6 ;
wire \fifo_inst/Equal.wgraynext_2_7 ;
wire \fifo_inst/rbin_num_next_0_9 ;
wire \fifo_inst/Equal.rq2_wptr_12_11 ;
wire \fifo_inst/rempty_val ;
wire \fifo_inst/rempty_val1 ;
wire \fifo_inst/wfull_val1_2 ;
wire \fifo_inst/wfull_val1_3 ;
wire \fifo_inst/Full_1 ;
wire \fifo_inst/Full_2 ;
wire \fifo_inst/Equal.rq1_wptr_0_5 ;
wire \fifo_inst/Equal.rq1_wptr_0_9 ;
wire \fifo_inst/Equal.rq1_wptr_0_10 ;
wire \fifo_inst/wfull_val1_9 ;
wire \fifo_inst/rcnt_sub_0_3 ;
wire \fifo_inst/rcnt_sub_1_3 ;
wire \fifo_inst/rcnt_sub_2_3 ;
wire \fifo_inst/rcnt_sub_3_3 ;
wire \fifo_inst/rcnt_sub_4_3 ;
wire \fifo_inst/rcnt_sub_5_3 ;
wire \fifo_inst/rcnt_sub_6_3 ;
wire \fifo_inst/rcnt_sub_7_3 ;
wire \fifo_inst/rcnt_sub_8_3 ;
wire \fifo_inst/rcnt_sub_9_3 ;
wire \fifo_inst/rcnt_sub_10_3 ;
wire \fifo_inst/rcnt_sub_11_3 ;
wire \fifo_inst/rcnt_sub_12_0_COUT ;
wire \fifo_inst/n144_1_SUM ;
wire \fifo_inst/n144_3 ;
wire \fifo_inst/n145_1_SUM ;
wire \fifo_inst/n145_3 ;
wire \fifo_inst/n146_1_SUM ;
wire \fifo_inst/n146_3 ;
wire \fifo_inst/n147_1_SUM ;
wire \fifo_inst/n147_3 ;
wire \fifo_inst/n148_1_SUM ;
wire \fifo_inst/n148_3 ;
wire \fifo_inst/n149_1_SUM ;
wire \fifo_inst/n149_3 ;
wire \fifo_inst/n150_1_SUM ;
wire \fifo_inst/n150_3 ;
wire \fifo_inst/n151_1_SUM ;
wire \fifo_inst/n151_3 ;
wire \fifo_inst/n152_1_SUM ;
wire \fifo_inst/n152_3 ;
wire \fifo_inst/n153_1_SUM ;
wire \fifo_inst/n153_3 ;
wire \fifo_inst/n154_1_SUM ;
wire \fifo_inst/n154_3 ;
wire \fifo_inst/n155_1_SUM ;
wire \fifo_inst/n155_3 ;
wire \fifo_inst/Equal.rq1_wptr_0_24 ;
wire \fifo_inst/wfull_val1_14 ;
wire [11:0] \fifo_inst/Equal.rgraynext ;
wire [11:0] \fifo_inst/Equal.wcount_r ;
wire [11:0] \fifo_inst/Equal.wgraynext ;
wire [12:1] \fifo_inst/rbin_num_next ;
wire [12:1] \fifo_inst/Equal.wbinnext ;
wire [12:0] \fifo_inst/rbin_num ;
wire [11:0] \fifo_inst/rptr ;
wire [12:0] \fifo_inst/wptr ;
wire [11:0] \fifo_inst/Equal.wbin ;
wire [12:0] \fifo_inst/Equal.wcount_r_d ;
wire [1:0] \fifo_inst/reset_r ;
wire [1:0] \fifo_inst/reset_w ;
wire [12:0] \fifo_inst/Equal.rq2_wptr ;
wire [12:0] \fifo_inst/rcnt_sub ;
wire [31:4] \fifo_inst/DO ;
wire [31:4] \fifo_inst/DO_0 ;
wire [31:4] \fifo_inst/DO_1 ;
wire [31:4] \fifo_inst/DO_2 ;
wire [3:1] \fifo_inst/DO_3 ;
VCC VCC_cZ (
  .V(VCC)
);
GND GND_cZ (
  .G(GND)
);
GSR GSR (
	.GSRI(VCC)
);
LUT4 \fifo_inst/n31_s1  (
	.I0(\fifo_inst/Full_1 ),
	.I1(\fifo_inst/Full_2 ),
	.I2(\fifo_inst/wfull_val1_9 ),
	.I3(WrEn),
	.F(\fifo_inst/n31_5 )
);
defparam \fifo_inst/n31_s1 .INIT=16'h3500;
LUT2 \fifo_inst/n35_s0  (
	.I0(Empty),
	.I1(RdEn),
	.F(\fifo_inst/n35_3 )
);
defparam \fifo_inst/n35_s0 .INIT=4'h4;
LUT3 \fifo_inst/Equal.rgraynext_3_s0  (
	.I0(\fifo_inst/rbin_num [4]),
	.I1(\fifo_inst/rbin_num_next [3]),
	.I2(\fifo_inst/Equal.rgraynext_3_4 ),
	.F(\fifo_inst/Equal.rgraynext [3])
);
defparam \fifo_inst/Equal.rgraynext_3_s0 .INIT=8'h96;
LUT3 \fifo_inst/Equal.rgraynext_4_s0  (
	.I0(\fifo_inst/rbin_num [4]),
	.I1(\fifo_inst/Equal.rgraynext_3_4 ),
	.I2(\fifo_inst/rbin_num [5]),
	.F(\fifo_inst/Equal.rgraynext [4])
);
defparam \fifo_inst/Equal.rgraynext_4_s0 .INIT=8'h1E;
LUT4 \fifo_inst/Equal.rgraynext_5_s0  (
	.I0(\fifo_inst/rbin_num [4]),
	.I1(\fifo_inst/Equal.rgraynext_3_4 ),
	.I2(\fifo_inst/rbin_num [5]),
	.I3(\fifo_inst/rbin_num [6]),
	.F(\fifo_inst/Equal.rgraynext [5])
);
defparam \fifo_inst/Equal.rgraynext_5_s0 .INIT=16'h07F8;
LUT4 \fifo_inst/Equal.rgraynext_7_s0  (
	.I0(\fifo_inst/rbin_num [6]),
	.I1(\fifo_inst/Equal.rgraynext_6_4 ),
	.I2(\fifo_inst/rbin_num [7]),
	.I3(\fifo_inst/rbin_num [8]),
	.F(\fifo_inst/Equal.rgraynext [7])
);
defparam \fifo_inst/Equal.rgraynext_7_s0 .INIT=16'h07F8;
LUT4 \fifo_inst/Equal.rgraynext_8_s0  (
	.I0(\fifo_inst/Equal.rgraynext_6_4 ),
	.I1(\fifo_inst/Equal.rgraynext_8_4 ),
	.I2(\fifo_inst/rbin_num [8]),
	.I3(\fifo_inst/rbin_num [9]),
	.F(\fifo_inst/Equal.rgraynext [8])
);
defparam \fifo_inst/Equal.rgraynext_8_s0 .INIT=16'h07F8;
LUT4 \fifo_inst/Equal.rgraynext_9_s0  (
	.I0(\fifo_inst/Equal.rgraynext_6_4 ),
	.I1(\fifo_inst/Equal.rgraynext_9_6 ),
	.I2(\fifo_inst/rbin_num [9]),
	.I3(\fifo_inst/rbin_num [10]),
	.F(\fifo_inst/Equal.rgraynext [9])
);
defparam \fifo_inst/Equal.rgraynext_9_s0 .INIT=16'h07F8;
LUT4 \fifo_inst/Equal.rgraynext_10_s0  (
	.I0(\fifo_inst/Equal.rgraynext_6_4 ),
	.I1(\fifo_inst/Equal.rgraynext_10_4 ),
	.I2(\fifo_inst/rbin_num [10]),
	.I3(\fifo_inst/rbin_num [11]),
	.F(\fifo_inst/Equal.rgraynext [10])
);
defparam \fifo_inst/Equal.rgraynext_10_s0 .INIT=16'h07F8;
LUT3 \fifo_inst/Equal.rgraynext_11_s0  (
	.I0(\fifo_inst/rbin_num [11]),
	.I1(\fifo_inst/Equal.rgraynext_11_4 ),
	.I2(\fifo_inst/rbin_num [12]),
	.F(\fifo_inst/Equal.rgraynext [11])
);
defparam \fifo_inst/Equal.rgraynext_11_s0 .INIT=8'h1E;
LUT4 \fifo_inst/Equal.wcount_r_10_s0  (
	.I0(\fifo_inst/Equal.rq2_wptr [12]),
	.I1(\fifo_inst/Equal.rq2_wptr [11]),
	.I2(\fifo_inst/Equal.rq2_wptr [10]),
	.I3(\fifo_inst/Equal.wcount_r_11_4 ),
	.F(\fifo_inst/Equal.wcount_r [10])
);
defparam \fifo_inst/Equal.wcount_r_10_s0 .INIT=16'h9600;
LUT4 \fifo_inst/Equal.wcount_r_0_s0  (
	.I0(\fifo_inst/Equal.rq2_wptr [0]),
	.I1(\fifo_inst/Equal.wcount_r_3_4 ),
	.I2(\fifo_inst/Equal.wcount_r_1_4 ),
	.I3(\fifo_inst/Equal.wcount_r_11_4 ),
	.F(\fifo_inst/Equal.wcount_r [0])
);
defparam \fifo_inst/Equal.wcount_r_0_s0 .INIT=16'h9600;
LUT2 \fifo_inst/n200_s0  (
	.I0(\fifo_inst/rbin_num [12]),
	.I1(\fifo_inst/Equal.wcount_r_d [12]),
	.F(\fifo_inst/n200_3 )
);
defparam \fifo_inst/n200_s0 .INIT=4'h6;
LUT3 \fifo_inst/Equal.wgraynext_2_s0  (
	.I0(\fifo_inst/Equal.wbin [3]),
	.I1(\fifo_inst/Equal.wbinnext [2]),
	.I2(\fifo_inst/Equal.wgraynext_2_7 ),
	.F(\fifo_inst/Equal.wgraynext [2])
);
defparam \fifo_inst/Equal.wgraynext_2_s0 .INIT=8'h96;
LUT3 \fifo_inst/Equal.wgraynext_3_s0  (
	.I0(\fifo_inst/Equal.wbin [3]),
	.I1(\fifo_inst/Equal.wgraynext_2_7 ),
	.I2(\fifo_inst/Equal.wbin [4]),
	.F(\fifo_inst/Equal.wgraynext [3])
);
defparam \fifo_inst/Equal.wgraynext_3_s0 .INIT=8'h1E;
LUT4 \fifo_inst/Equal.wgraynext_4_s0  (
	.I0(\fifo_inst/Equal.wbin [3]),
	.I1(\fifo_inst/Equal.wgraynext_2_7 ),
	.I2(\fifo_inst/Equal.wbin [4]),
	.I3(\fifo_inst/Equal.wbin [5]),
	.F(\fifo_inst/Equal.wgraynext [4])
);
defparam \fifo_inst/Equal.wgraynext_4_s0 .INIT=16'h07F8;
LUT4 \fifo_inst/Equal.wgraynext_5_s0  (
	.I0(\fifo_inst/Equal.wgraynext_2_7 ),
	.I1(\fifo_inst/Equal.wgraynext_5_4 ),
	.I2(\fifo_inst/Equal.wbin [5]),
	.I3(\fifo_inst/Equal.wbin [6]),
	.F(\fifo_inst/Equal.wgraynext [5])
);
defparam \fifo_inst/Equal.wgraynext_5_s0 .INIT=16'h07F8;
LUT4 \fifo_inst/Equal.wgraynext_6_s0  (
	.I0(\fifo_inst/Equal.wgraynext_2_7 ),
	.I1(\fifo_inst/Equal.wgraynext_6_7 ),
	.I2(\fifo_inst/Equal.wbin [6]),
	.I3(\fifo_inst/Equal.wbin [7]),
	.F(\fifo_inst/Equal.wgraynext [6])
);
defparam \fifo_inst/Equal.wgraynext_6_s0 .INIT=16'h07F8;
LUT4 \fifo_inst/Equal.wgraynext_7_s0  (
	.I0(\fifo_inst/Equal.wgraynext_2_7 ),
	.I1(\fifo_inst/Equal.wgraynext_7_6 ),
	.I2(\fifo_inst/Equal.wbin [7]),
	.I3(\fifo_inst/Equal.wbin [8]),
	.F(\fifo_inst/Equal.wgraynext [7])
);
defparam \fifo_inst/Equal.wgraynext_7_s0 .INIT=16'h07F8;
LUT4 \fifo_inst/Equal.wgraynext_8_s0  (
	.I0(\fifo_inst/Equal.wgraynext_2_7 ),
	.I1(\fifo_inst/Equal.wgraynext_8_4 ),
	.I2(\fifo_inst/Equal.wbin [8]),
	.I3(\fifo_inst/Equal.wbin [9]),
	.F(\fifo_inst/Equal.wgraynext [8])
);
defparam \fifo_inst/Equal.wgraynext_8_s0 .INIT=16'h07F8;
LUT4 \fifo_inst/Equal.wgraynext_9_s0  (
	.I0(\fifo_inst/Equal.wgraynext_2_7 ),
	.I1(\fifo_inst/Equal.wgraynext_9_7 ),
	.I2(\fifo_inst/Equal.wbin [9]),
	.I3(\fifo_inst/Equal.wbin [10]),
	.F(\fifo_inst/Equal.wgraynext [9])
);
defparam \fifo_inst/Equal.wgraynext_9_s0 .INIT=16'h07F8;
LUT4 \fifo_inst/Equal.wgraynext_10_s0  (
	.I0(\fifo_inst/Equal.wgraynext_2_7 ),
	.I1(\fifo_inst/Equal.wgraynext_10_6 ),
	.I2(\fifo_inst/Equal.wbin [10]),
	.I3(\fifo_inst/Equal.wbin [11]),
	.F(\fifo_inst/Equal.wgraynext [10])
);
defparam \fifo_inst/Equal.wgraynext_10_s0 .INIT=16'h07F8;
LUT3 \fifo_inst/Equal.wgraynext_11_s0  (
	.I0(\fifo_inst/Equal.wbin [11]),
	.I1(\fifo_inst/Equal.wgraynext_11_4 ),
	.I2(\fifo_inst/wptr [12]),
	.F(\fifo_inst/Equal.wgraynext [11])
);
defparam \fifo_inst/Equal.wgraynext_11_s0 .INIT=8'h1E;
LUT2 \fifo_inst/wfull_val_s0  (
	.I0(\fifo_inst/wfull_val_4 ),
	.I1(\fifo_inst/wfull_val_5 ),
	.F(\fifo_inst/wfull_val )
);
defparam \fifo_inst/wfull_val_s0 .INIT=4'h8;
LUT2 \fifo_inst/n485_s0  (
	.I0(\fifo_inst/rempty_val ),
	.I1(\fifo_inst/reset_r [1]),
	.F(\fifo_inst/n485_3 )
);
defparam \fifo_inst/n485_s0 .INIT=4'hE;
LUT3 \fifo_inst/n698_s0  (
	.I0(\fifo_inst/reset_w [1]),
	.I1(\fifo_inst/wfull_val_4 ),
	.I2(\fifo_inst/wfull_val_5 ),
	.F(\fifo_inst/n698_3 )
);
defparam \fifo_inst/n698_s0 .INIT=8'h40;
LUT3 \fifo_inst/wfull_val1_s6  (
	.I0(\fifo_inst/wfull_val1_3 ),
	.I1(\fifo_inst/wfull_val1_2 ),
	.I2(\fifo_inst/wfull_val1_9 ),
	.F(\fifo_inst/wfull_val1 )
);
defparam \fifo_inst/wfull_val1_s6 .INIT=8'hAC;
LUT3 \fifo_inst/Full_d_s  (
	.I0(\fifo_inst/Full_1 ),
	.I1(\fifo_inst/Full_2 ),
	.I2(\fifo_inst/wfull_val1_9 ),
	.F(Full)
);
defparam \fifo_inst/Full_d_s .INIT=8'hCA;
LUT2 \fifo_inst/rbin_num_next_2_s3  (
	.I0(\fifo_inst/rbin_num [2]),
	.I1(\fifo_inst/rbin_num_next_2_8 ),
	.F(\fifo_inst/rbin_num_next [2])
);
defparam \fifo_inst/rbin_num_next_2_s3 .INIT=4'h6;
LUT3 \fifo_inst/rbin_num_next_3_s3  (
	.I0(\fifo_inst/rbin_num [2]),
	.I1(\fifo_inst/rbin_num_next_2_8 ),
	.I2(\fifo_inst/rbin_num [3]),
	.F(\fifo_inst/rbin_num_next [3])
);
defparam \fifo_inst/rbin_num_next_3_s3 .INIT=8'h78;
LUT3 \fifo_inst/rbin_num_next_5_s3  (
	.I0(\fifo_inst/rbin_num [4]),
	.I1(\fifo_inst/Equal.rgraynext_3_4 ),
	.I2(\fifo_inst/rbin_num [5]),
	.F(\fifo_inst/rbin_num_next [5])
);
defparam \fifo_inst/rbin_num_next_5_s3 .INIT=8'h78;
LUT2 \fifo_inst/rbin_num_next_11_s3  (
	.I0(\fifo_inst/rbin_num [11]),
	.I1(\fifo_inst/Equal.rgraynext_11_4 ),
	.F(\fifo_inst/rbin_num_next [11])
);
defparam \fifo_inst/rbin_num_next_11_s3 .INIT=4'h6;
LUT3 \fifo_inst/rbin_num_next_12_s2  (
	.I0(\fifo_inst/rbin_num [11]),
	.I1(\fifo_inst/Equal.rgraynext_11_4 ),
	.I2(\fifo_inst/rbin_num [12]),
	.F(\fifo_inst/rbin_num_next [12])
);
defparam \fifo_inst/rbin_num_next_12_s2 .INIT=8'h78;
LUT2 \fifo_inst/Equal.wbinnext_0_s3  (
	.I0(\fifo_inst/Equal.wbin [0]),
	.I1(\fifo_inst/n31_5 ),
	.F(\fifo_inst/Equal.wbinnext_0_7 )
);
defparam \fifo_inst/Equal.wbinnext_0_s3 .INIT=4'h6;
LUT3 \fifo_inst/Equal.wbinnext_1_s3  (
	.I0(\fifo_inst/Equal.wbin [0]),
	.I1(\fifo_inst/n31_5 ),
	.I2(\fifo_inst/Equal.wbin [1]),
	.F(\fifo_inst/Equal.wbinnext [1])
);
defparam \fifo_inst/Equal.wbinnext_1_s3 .INIT=8'h78;
LUT4 \fifo_inst/Equal.wbinnext_2_s3  (
	.I0(\fifo_inst/Equal.wbin [0]),
	.I1(\fifo_inst/Equal.wbin [1]),
	.I2(\fifo_inst/n31_5 ),
	.I3(\fifo_inst/Equal.wbin [2]),
	.F(\fifo_inst/Equal.wbinnext [2])
);
defparam \fifo_inst/Equal.wbinnext_2_s3 .INIT=16'h7F80;
LUT2 \fifo_inst/Equal.wbinnext_3_s3  (
	.I0(\fifo_inst/Equal.wbin [3]),
	.I1(\fifo_inst/Equal.wgraynext_2_7 ),
	.F(\fifo_inst/Equal.wbinnext [3])
);
defparam \fifo_inst/Equal.wbinnext_3_s3 .INIT=4'h6;
LUT3 \fifo_inst/Equal.wbinnext_4_s3  (
	.I0(\fifo_inst/Equal.wbin [3]),
	.I1(\fifo_inst/Equal.wgraynext_2_7 ),
	.I2(\fifo_inst/Equal.wbin [4]),
	.F(\fifo_inst/Equal.wbinnext [4])
);
defparam \fifo_inst/Equal.wbinnext_4_s3 .INIT=8'h78;
LUT3 \fifo_inst/Equal.wbinnext_6_s3  (
	.I0(\fifo_inst/Equal.wgraynext_2_7 ),
	.I1(\fifo_inst/Equal.wgraynext_6_7 ),
	.I2(\fifo_inst/Equal.wbin [6]),
	.F(\fifo_inst/Equal.wbinnext [6])
);
defparam \fifo_inst/Equal.wbinnext_6_s3 .INIT=8'h78;
LUT3 \fifo_inst/Equal.wbinnext_7_s3  (
	.I0(\fifo_inst/Equal.wgraynext_2_7 ),
	.I1(\fifo_inst/Equal.wgraynext_7_6 ),
	.I2(\fifo_inst/Equal.wbin [7]),
	.F(\fifo_inst/Equal.wbinnext [7])
);
defparam \fifo_inst/Equal.wbinnext_7_s3 .INIT=8'h78;
LUT3 \fifo_inst/Equal.wbinnext_9_s3  (
	.I0(\fifo_inst/Equal.wgraynext_2_7 ),
	.I1(\fifo_inst/Equal.wgraynext_9_7 ),
	.I2(\fifo_inst/Equal.wbin [9]),
	.F(\fifo_inst/Equal.wbinnext [9])
);
defparam \fifo_inst/Equal.wbinnext_9_s3 .INIT=8'h78;
LUT3 \fifo_inst/Equal.wbinnext_10_s3  (
	.I0(\fifo_inst/Equal.wgraynext_2_7 ),
	.I1(\fifo_inst/Equal.wgraynext_10_6 ),
	.I2(\fifo_inst/Equal.wbin [10]),
	.F(\fifo_inst/Equal.wbinnext [10])
);
defparam \fifo_inst/Equal.wbinnext_10_s3 .INIT=8'h78;
LUT2 \fifo_inst/Equal.wbinnext_11_s3  (
	.I0(\fifo_inst/Equal.wbin [11]),
	.I1(\fifo_inst/Equal.wgraynext_11_4 ),
	.F(\fifo_inst/Equal.wbinnext [11])
);
defparam \fifo_inst/Equal.wbinnext_11_s3 .INIT=4'h6;
LUT3 \fifo_inst/Equal.wbinnext_12_s2  (
	.I0(\fifo_inst/Equal.wbin [11]),
	.I1(\fifo_inst/Equal.wgraynext_11_4 ),
	.I2(\fifo_inst/wptr [12]),
	.F(\fifo_inst/Equal.wbinnext [12])
);
defparam \fifo_inst/Equal.wbinnext_12_s2 .INIT=8'h78;
LUT3 \fifo_inst/Equal.rgraynext_3_s1  (
	.I0(\fifo_inst/rbin_num [2]),
	.I1(\fifo_inst/rbin_num [3]),
	.I2(\fifo_inst/rbin_num_next_2_8 ),
	.F(\fifo_inst/Equal.rgraynext_3_4 )
);
defparam \fifo_inst/Equal.rgraynext_3_s1 .INIT=8'h80;
LUT2 \fifo_inst/Equal.rgraynext_6_s1  (
	.I0(\fifo_inst/rbin_num_next_2_8 ),
	.I1(\fifo_inst/Equal.rgraynext_6_5 ),
	.F(\fifo_inst/Equal.rgraynext_6_4 )
);
defparam \fifo_inst/Equal.rgraynext_6_s1 .INIT=4'h8;
LUT2 \fifo_inst/Equal.rgraynext_8_s1  (
	.I0(\fifo_inst/rbin_num [6]),
	.I1(\fifo_inst/rbin_num [7]),
	.F(\fifo_inst/Equal.rgraynext_8_4 )
);
defparam \fifo_inst/Equal.rgraynext_8_s1 .INIT=4'h8;
LUT4 \fifo_inst/Equal.rgraynext_10_s1  (
	.I0(\fifo_inst/rbin_num [6]),
	.I1(\fifo_inst/rbin_num [7]),
	.I2(\fifo_inst/rbin_num [8]),
	.I3(\fifo_inst/rbin_num [9]),
	.F(\fifo_inst/Equal.rgraynext_10_4 )
);
defparam \fifo_inst/Equal.rgraynext_10_s1 .INIT=16'h8000;
LUT4 \fifo_inst/Equal.rgraynext_11_s1  (
	.I0(\fifo_inst/rbin_num [10]),
	.I1(\fifo_inst/rbin_num_next_2_8 ),
	.I2(\fifo_inst/Equal.rgraynext_6_5 ),
	.I3(\fifo_inst/Equal.rgraynext_10_4 ),
	.F(\fifo_inst/Equal.rgraynext_11_4 )
);
defparam \fifo_inst/Equal.rgraynext_11_s1 .INIT=16'h8000;
LUT2 \fifo_inst/Equal.wcount_r_11_s1  (
	.I0(\fifo_inst/Equal.rq1_wptr_0_9 ),
	.I1(\fifo_inst/Equal.rq1_wptr_0_10 ),
	.F(\fifo_inst/Equal.wcount_r_11_4 )
);
defparam \fifo_inst/Equal.wcount_r_11_s1 .INIT=4'h8;
LUT4 \fifo_inst/Equal.wcount_r_9_s1  (
	.I0(\fifo_inst/Equal.rq2_wptr [12]),
	.I1(\fifo_inst/Equal.rq2_wptr [11]),
	.I2(\fifo_inst/Equal.rq2_wptr [10]),
	.I3(\fifo_inst/Equal.rq2_wptr [9]),
	.F(\fifo_inst/Equal.wcount_r_9_4 )
);
defparam \fifo_inst/Equal.wcount_r_9_s1 .INIT=16'h6996;
LUT3 \fifo_inst/Equal.wcount_r_7_s1  (
	.I0(\fifo_inst/Equal.rq2_wptr [8]),
	.I1(\fifo_inst/Equal.rq2_wptr [7]),
	.I2(\fifo_inst/Equal.wcount_r_9_4 ),
	.F(\fifo_inst/Equal.wcount_r_7_4 )
);
defparam \fifo_inst/Equal.wcount_r_7_s1 .INIT=8'h96;
LUT4 \fifo_inst/Equal.wcount_r_3_s1  (
	.I0(\fifo_inst/Equal.rq2_wptr [4]),
	.I1(\fifo_inst/Equal.rq2_wptr [3]),
	.I2(\fifo_inst/Equal.wcount_r_9_4 ),
	.I3(\fifo_inst/Equal.wcount_r_5_5 ),
	.F(\fifo_inst/Equal.wcount_r_3_4 )
);
defparam \fifo_inst/Equal.wcount_r_3_s1 .INIT=16'h6996;
LUT2 \fifo_inst/Equal.wcount_r_1_s1  (
	.I0(\fifo_inst/Equal.rq2_wptr [2]),
	.I1(\fifo_inst/Equal.rq2_wptr [1]),
	.F(\fifo_inst/Equal.wcount_r_1_4 )
);
defparam \fifo_inst/Equal.wcount_r_1_s1 .INIT=4'h6;
LUT2 \fifo_inst/Equal.wgraynext_5_s1  (
	.I0(\fifo_inst/Equal.wbin [3]),
	.I1(\fifo_inst/Equal.wbin [4]),
	.F(\fifo_inst/Equal.wgraynext_5_4 )
);
defparam \fifo_inst/Equal.wgraynext_5_s1 .INIT=4'h8;
LUT2 \fifo_inst/Equal.wgraynext_8_s1  (
	.I0(\fifo_inst/Equal.wbin [3]),
	.I1(\fifo_inst/Equal.wgraynext_8_5 ),
	.F(\fifo_inst/Equal.wgraynext_8_4 )
);
defparam \fifo_inst/Equal.wgraynext_8_s1 .INIT=4'h8;
LUT4 \fifo_inst/Equal.wgraynext_11_s1  (
	.I0(Full),
	.I1(\fifo_inst/Equal.wgraynext_2_5 ),
	.I2(\fifo_inst/Equal.wgraynext_8_5 ),
	.I3(\fifo_inst/Equal.wgraynext_11_5 ),
	.F(\fifo_inst/Equal.wgraynext_11_4 )
);
defparam \fifo_inst/Equal.wgraynext_11_s1 .INIT=16'h4000;
LUT4 \fifo_inst/wfull_val_s1  (
	.I0(\fifo_inst/wptr [1]),
	.I1(\fifo_inst/rptr [1]),
	.I2(\fifo_inst/wfull_val_6 ),
	.I3(\fifo_inst/wfull_val_7 ),
	.F(\fifo_inst/wfull_val_4 )
);
defparam \fifo_inst/wfull_val_s1 .INIT=16'h9000;
LUT4 \fifo_inst/wfull_val_s2  (
	.I0(\fifo_inst/wfull_val_8 ),
	.I1(\fifo_inst/wfull_val_9 ),
	.I2(\fifo_inst/wfull_val_10 ),
	.I3(\fifo_inst/wfull_val_11 ),
	.F(\fifo_inst/wfull_val_5 )
);
defparam \fifo_inst/wfull_val_s2 .INIT=16'h8000;
LUT4 \fifo_inst/rbin_num_next_2_s4  (
	.I0(Empty),
	.I1(RdEn),
	.I2(\fifo_inst/rbin_num [0]),
	.I3(\fifo_inst/rbin_num [1]),
	.F(\fifo_inst/rbin_num_next_2_8 )
);
defparam \fifo_inst/rbin_num_next_2_s4 .INIT=16'h4000;
LUT4 \fifo_inst/Equal.rgraynext_6_s2  (
	.I0(\fifo_inst/rbin_num [2]),
	.I1(\fifo_inst/rbin_num [3]),
	.I2(\fifo_inst/rbin_num [4]),
	.I3(\fifo_inst/rbin_num [5]),
	.F(\fifo_inst/Equal.rgraynext_6_5 )
);
defparam \fifo_inst/Equal.rgraynext_6_s2 .INIT=16'h8000;
LUT4 \fifo_inst/Equal.wcount_r_5_s2  (
	.I0(\fifo_inst/Equal.rq2_wptr [8]),
	.I1(\fifo_inst/Equal.rq2_wptr [7]),
	.I2(\fifo_inst/Equal.rq2_wptr [6]),
	.I3(\fifo_inst/Equal.rq2_wptr [5]),
	.F(\fifo_inst/Equal.wcount_r_5_5 )
);
defparam \fifo_inst/Equal.wcount_r_5_s2 .INIT=16'h6996;
LUT4 \fifo_inst/Equal.wgraynext_2_s2  (
	.I0(WrEn),
	.I1(\fifo_inst/Equal.wbin [0]),
	.I2(\fifo_inst/Equal.wbin [1]),
	.I3(\fifo_inst/Equal.wbin [2]),
	.F(\fifo_inst/Equal.wgraynext_2_5 )
);
defparam \fifo_inst/Equal.wgraynext_2_s2 .INIT=16'h8000;
LUT4 \fifo_inst/Equal.wgraynext_8_s2  (
	.I0(\fifo_inst/Equal.wbin [4]),
	.I1(\fifo_inst/Equal.wbin [5]),
	.I2(\fifo_inst/Equal.wbin [6]),
	.I3(\fifo_inst/Equal.wbin [7]),
	.F(\fifo_inst/Equal.wgraynext_8_5 )
);
defparam \fifo_inst/Equal.wgraynext_8_s2 .INIT=16'h8000;
LUT4 \fifo_inst/Equal.wgraynext_11_s2  (
	.I0(\fifo_inst/Equal.wbin [3]),
	.I1(\fifo_inst/Equal.wbin [8]),
	.I2(\fifo_inst/Equal.wbin [9]),
	.I3(\fifo_inst/Equal.wbin [10]),
	.F(\fifo_inst/Equal.wgraynext_11_5 )
);
defparam \fifo_inst/Equal.wgraynext_11_s2 .INIT=16'h8000;
LUT4 \fifo_inst/wfull_val_s3  (
	.I0(\fifo_inst/wptr [0]),
	.I1(\fifo_inst/rptr [0]),
	.I2(\fifo_inst/wptr [4]),
	.I3(\fifo_inst/rptr [4]),
	.F(\fifo_inst/wfull_val_6 )
);
defparam \fifo_inst/wfull_val_s3 .INIT=16'h9009;
LUT4 \fifo_inst/wfull_val_s4  (
	.I0(\fifo_inst/wptr [2]),
	.I1(\fifo_inst/rptr [2]),
	.I2(\fifo_inst/wptr [8]),
	.I3(\fifo_inst/rptr [8]),
	.F(\fifo_inst/wfull_val_7 )
);
defparam \fifo_inst/wfull_val_s4 .INIT=16'h9009;
LUT4 \fifo_inst/wfull_val_s5  (
	.I0(\fifo_inst/wptr [3]),
	.I1(\fifo_inst/rptr [3]),
	.I2(\fifo_inst/wptr [6]),
	.I3(\fifo_inst/rptr [6]),
	.F(\fifo_inst/wfull_val_8 )
);
defparam \fifo_inst/wfull_val_s5 .INIT=16'h9009;
LUT4 \fifo_inst/wfull_val_s6  (
	.I0(\fifo_inst/wptr [11]),
	.I1(\fifo_inst/wptr [12]),
	.I2(\fifo_inst/rbin_num [12]),
	.I3(\fifo_inst/rptr [11]),
	.F(\fifo_inst/wfull_val_9 )
);
defparam \fifo_inst/wfull_val_s6 .INIT=16'h1428;
LUT4 \fifo_inst/wfull_val_s7  (
	.I0(\fifo_inst/wptr [7]),
	.I1(\fifo_inst/rptr [7]),
	.I2(\fifo_inst/wptr [10]),
	.I3(\fifo_inst/rptr [10]),
	.F(\fifo_inst/wfull_val_10 )
);
defparam \fifo_inst/wfull_val_s7 .INIT=16'h9009;
LUT4 \fifo_inst/wfull_val_s8  (
	.I0(\fifo_inst/wptr [5]),
	.I1(\fifo_inst/rptr [5]),
	.I2(\fifo_inst/wptr [9]),
	.I3(\fifo_inst/rptr [9]),
	.F(\fifo_inst/wfull_val_11 )
);
defparam \fifo_inst/wfull_val_s8 .INIT=16'h9009;
LUT3 \fifo_inst/Equal.wgraynext_0_s1  (
	.I0(\fifo_inst/Equal.wbinnext [1]),
	.I1(\fifo_inst/Equal.wbin [0]),
	.I2(\fifo_inst/n31_5 ),
	.F(\fifo_inst/Equal.wgraynext [0])
);
defparam \fifo_inst/Equal.wgraynext_0_s1 .INIT=8'h96;
LUT4 \fifo_inst/Equal.wcount_r_1_s2  (
	.I0(\fifo_inst/Equal.wcount_r_3_4 ),
	.I1(\fifo_inst/Equal.rq2_wptr [2]),
	.I2(\fifo_inst/Equal.rq2_wptr [1]),
	.I3(\fifo_inst/Equal.wcount_r_11_4 ),
	.F(\fifo_inst/Equal.wcount_r [1])
);
defparam \fifo_inst/Equal.wcount_r_1_s2 .INIT=16'h9600;
LUT4 \fifo_inst/Equal.wcount_r_4_s1  (
	.I0(\fifo_inst/Equal.rq2_wptr [4]),
	.I1(\fifo_inst/Equal.wcount_r_9_4 ),
	.I2(\fifo_inst/Equal.wcount_r_5_5 ),
	.I3(\fifo_inst/Equal.wcount_r_11_4 ),
	.F(\fifo_inst/Equal.wcount_r [4])
);
defparam \fifo_inst/Equal.wcount_r_4_s1 .INIT=16'h9600;
LUT4 \fifo_inst/Equal.wcount_r_7_s2  (
	.I0(\fifo_inst/Equal.rq2_wptr [8]),
	.I1(\fifo_inst/Equal.rq2_wptr [7]),
	.I2(\fifo_inst/Equal.wcount_r_9_4 ),
	.I3(\fifo_inst/Equal.wcount_r_11_4 ),
	.F(\fifo_inst/Equal.wcount_r [7])
);
defparam \fifo_inst/Equal.wcount_r_7_s2 .INIT=16'h9600;
LUT4 \fifo_inst/Equal.wbinnext_5_s4  (
	.I0(\fifo_inst/Equal.wgraynext_2_7 ),
	.I1(\fifo_inst/Equal.wbin [3]),
	.I2(\fifo_inst/Equal.wbin [4]),
	.I3(\fifo_inst/Equal.wbin [5]),
	.F(\fifo_inst/Equal.wbinnext [5])
);
defparam \fifo_inst/Equal.wbinnext_5_s4 .INIT=16'h7F80;
LUT4 \fifo_inst/Equal.wgraynext_7_s2  (
	.I0(\fifo_inst/Equal.wbin [3]),
	.I1(\fifo_inst/Equal.wbin [6]),
	.I2(\fifo_inst/Equal.wbin [4]),
	.I3(\fifo_inst/Equal.wbin [5]),
	.F(\fifo_inst/Equal.wgraynext_7_6 )
);
defparam \fifo_inst/Equal.wgraynext_7_s2 .INIT=16'h8000;
LUT3 \fifo_inst/Equal.wgraynext_6_s3  (
	.I0(\fifo_inst/Equal.wbin [3]),
	.I1(\fifo_inst/Equal.wbin [4]),
	.I2(\fifo_inst/Equal.wbin [5]),
	.F(\fifo_inst/Equal.wgraynext_6_7 )
);
defparam \fifo_inst/Equal.wgraynext_6_s3 .INIT=8'h80;
LUT4 \fifo_inst/Equal.wbinnext_8_s4  (
	.I0(\fifo_inst/Equal.wgraynext_2_7 ),
	.I1(\fifo_inst/Equal.wbin [3]),
	.I2(\fifo_inst/Equal.wgraynext_8_5 ),
	.I3(\fifo_inst/Equal.wbin [8]),
	.F(\fifo_inst/Equal.wbinnext [8])
);
defparam \fifo_inst/Equal.wbinnext_8_s4 .INIT=16'h7F80;
LUT4 \fifo_inst/Equal.wgraynext_10_s2  (
	.I0(\fifo_inst/Equal.wbin [9]),
	.I1(\fifo_inst/Equal.wgraynext_8_5 ),
	.I2(\fifo_inst/Equal.wbin [3]),
	.I3(\fifo_inst/Equal.wbin [8]),
	.F(\fifo_inst/Equal.wgraynext_10_6 )
);
defparam \fifo_inst/Equal.wgraynext_10_s2 .INIT=16'h8000;
LUT3 \fifo_inst/Equal.wgraynext_9_s3  (
	.I0(\fifo_inst/Equal.wgraynext_8_5 ),
	.I1(\fifo_inst/Equal.wbin [3]),
	.I2(\fifo_inst/Equal.wbin [8]),
	.F(\fifo_inst/Equal.wgraynext_9_7 )
);
defparam \fifo_inst/Equal.wgraynext_9_s3 .INIT=8'h80;
LUT3 \fifo_inst/Equal.rgraynext_9_s2  (
	.I0(\fifo_inst/rbin_num [8]),
	.I1(\fifo_inst/rbin_num [6]),
	.I2(\fifo_inst/rbin_num [7]),
	.F(\fifo_inst/Equal.rgraynext_9_6 )
);
defparam \fifo_inst/Equal.rgraynext_9_s2 .INIT=8'h80;
LUT4 \fifo_inst/rbin_num_next_8_s4  (
	.I0(\fifo_inst/Equal.rgraynext_6_4 ),
	.I1(\fifo_inst/rbin_num [6]),
	.I2(\fifo_inst/rbin_num [7]),
	.I3(\fifo_inst/rbin_num [8]),
	.F(\fifo_inst/rbin_num_next [8])
);
defparam \fifo_inst/rbin_num_next_8_s4 .INIT=16'h7F80;
LUT4 \fifo_inst/Equal.wgraynext_1_s1  (
	.I0(\fifo_inst/Equal.wbin [0]),
	.I1(\fifo_inst/n31_5 ),
	.I2(\fifo_inst/Equal.wbin [1]),
	.I3(\fifo_inst/Equal.wbinnext [2]),
	.F(\fifo_inst/Equal.wgraynext [1])
);
defparam \fifo_inst/Equal.wgraynext_1_s1 .INIT=16'h8778;
LUT4 \fifo_inst/Equal.wgraynext_2_s3  (
	.I0(\fifo_inst/Full_1 ),
	.I1(\fifo_inst/Full_2 ),
	.I2(\fifo_inst/wfull_val1_9 ),
	.I3(\fifo_inst/Equal.wgraynext_2_5 ),
	.F(\fifo_inst/Equal.wgraynext_2_7 )
);
defparam \fifo_inst/Equal.wgraynext_2_s3 .INIT=16'h3500;
LUT3 \fifo_inst/Equal.rgraynext_2_s1  (
	.I0(\fifo_inst/rbin_num [2]),
	.I1(\fifo_inst/rbin_num_next_2_8 ),
	.I2(\fifo_inst/rbin_num_next [3]),
	.F(\fifo_inst/Equal.rgraynext [2])
);
defparam \fifo_inst/Equal.rgraynext_2_s1 .INIT=8'h96;
LUT3 \fifo_inst/Equal.rgraynext_1_s1  (
	.I0(\fifo_inst/rbin_num_next [1]),
	.I1(\fifo_inst/rbin_num [2]),
	.I2(\fifo_inst/rbin_num_next_2_8 ),
	.F(\fifo_inst/Equal.rgraynext [1])
);
defparam \fifo_inst/Equal.rgraynext_1_s1 .INIT=8'h96;
LUT4 \fifo_inst/rbin_num_next_4_s4  (
	.I0(\fifo_inst/rbin_num [4]),
	.I1(\fifo_inst/rbin_num [2]),
	.I2(\fifo_inst/rbin_num [3]),
	.I3(\fifo_inst/rbin_num_next_2_8 ),
	.F(\fifo_inst/rbin_num_next [4])
);
defparam \fifo_inst/rbin_num_next_4_s4 .INIT=16'h6AAA;
LUT4 \fifo_inst/rbin_num_next_10_s4  (
	.I0(\fifo_inst/rbin_num_next_2_8 ),
	.I1(\fifo_inst/Equal.rgraynext_6_5 ),
	.I2(\fifo_inst/Equal.rgraynext_10_4 ),
	.I3(\fifo_inst/rbin_num [10]),
	.F(\fifo_inst/rbin_num_next [10])
);
defparam \fifo_inst/rbin_num_next_10_s4 .INIT=16'h7F80;
LUT4 \fifo_inst/rbin_num_next_9_s4  (
	.I0(\fifo_inst/rbin_num_next_2_8 ),
	.I1(\fifo_inst/Equal.rgraynext_6_5 ),
	.I2(\fifo_inst/Equal.rgraynext_9_6 ),
	.I3(\fifo_inst/rbin_num [9]),
	.F(\fifo_inst/rbin_num_next [9])
);
defparam \fifo_inst/rbin_num_next_9_s4 .INIT=16'h7F80;
LUT4 \fifo_inst/rbin_num_next_7_s4  (
	.I0(\fifo_inst/rbin_num [6]),
	.I1(\fifo_inst/rbin_num_next_2_8 ),
	.I2(\fifo_inst/Equal.rgraynext_6_5 ),
	.I3(\fifo_inst/rbin_num [7]),
	.F(\fifo_inst/rbin_num_next [7])
);
defparam \fifo_inst/rbin_num_next_7_s4 .INIT=16'h7F80;
LUT3 \fifo_inst/rbin_num_next_6_s4  (
	.I0(\fifo_inst/rbin_num [6]),
	.I1(\fifo_inst/rbin_num_next_2_8 ),
	.I2(\fifo_inst/Equal.rgraynext_6_5 ),
	.F(\fifo_inst/rbin_num_next [6])
);
defparam \fifo_inst/rbin_num_next_6_s4 .INIT=8'h6A;
LUT4 \fifo_inst/Equal.rgraynext_6_s3  (
	.I0(\fifo_inst/rbin_num [6]),
	.I1(\fifo_inst/rbin_num_next_2_8 ),
	.I2(\fifo_inst/Equal.rgraynext_6_5 ),
	.I3(\fifo_inst/rbin_num [7]),
	.F(\fifo_inst/Equal.rgraynext [6])
);
defparam \fifo_inst/Equal.rgraynext_6_s3 .INIT=16'h15EA;
LUT4 \fifo_inst/rbin_num_next_1_s4  (
	.I0(\fifo_inst/rbin_num [0]),
	.I1(Empty),
	.I2(RdEn),
	.I3(\fifo_inst/rbin_num [1]),
	.F(\fifo_inst/rbin_num_next [1])
);
defparam \fifo_inst/rbin_num_next_1_s4 .INIT=16'hDF20;
LUT3 \fifo_inst/rbin_num_next_0_s4  (
	.I0(\fifo_inst/rbin_num [0]),
	.I1(Empty),
	.I2(RdEn),
	.F(\fifo_inst/rbin_num_next_0_9 )
);
defparam \fifo_inst/rbin_num_next_0_s4 .INIT=8'h9A;
LUT4 \fifo_inst/Equal.wcount_r_5_s4  (
	.I0(\fifo_inst/Equal.wcount_r_9_4 ),
	.I1(\fifo_inst/Equal.wcount_r_5_5 ),
	.I2(\fifo_inst/Equal.rq1_wptr_0_9 ),
	.I3(\fifo_inst/Equal.rq1_wptr_0_10 ),
	.F(\fifo_inst/Equal.wcount_r [5])
);
defparam \fifo_inst/Equal.wcount_r_5_s4 .INIT=16'h6000;
LUT3 \fifo_inst/Equal.rq2_wptr_12_s4  (
	.I0(\fifo_inst/Equal.rq2_wptr [12]),
	.I1(\fifo_inst/Equal.rq1_wptr_0_9 ),
	.I2(\fifo_inst/Equal.rq1_wptr_0_10 ),
	.F(\fifo_inst/Equal.rq2_wptr_12_11 )
);
defparam \fifo_inst/Equal.rq2_wptr_12_s4 .INIT=8'h80;
LUT4 \fifo_inst/Equal.wcount_r_2_s1  (
	.I0(\fifo_inst/Equal.rq2_wptr [2]),
	.I1(\fifo_inst/Equal.wcount_r_3_4 ),
	.I2(\fifo_inst/Equal.rq1_wptr_0_9 ),
	.I3(\fifo_inst/Equal.rq1_wptr_0_10 ),
	.F(\fifo_inst/Equal.wcount_r [2])
);
defparam \fifo_inst/Equal.wcount_r_2_s1 .INIT=16'h6000;
LUT3 \fifo_inst/Equal.wcount_r_3_s2  (
	.I0(\fifo_inst/Equal.wcount_r_3_4 ),
	.I1(\fifo_inst/Equal.rq1_wptr_0_9 ),
	.I2(\fifo_inst/Equal.rq1_wptr_0_10 ),
	.F(\fifo_inst/Equal.wcount_r [3])
);
defparam \fifo_inst/Equal.wcount_r_3_s2 .INIT=8'h80;
LUT4 \fifo_inst/Equal.wcount_r_6_s1  (
	.I0(\fifo_inst/Equal.rq2_wptr [6]),
	.I1(\fifo_inst/Equal.wcount_r_7_4 ),
	.I2(\fifo_inst/Equal.rq1_wptr_0_9 ),
	.I3(\fifo_inst/Equal.rq1_wptr_0_10 ),
	.F(\fifo_inst/Equal.wcount_r [6])
);
defparam \fifo_inst/Equal.wcount_r_6_s1 .INIT=16'h6000;
LUT4 \fifo_inst/Equal.wcount_r_8_s1  (
	.I0(\fifo_inst/Equal.rq2_wptr [8]),
	.I1(\fifo_inst/Equal.wcount_r_9_4 ),
	.I2(\fifo_inst/Equal.rq1_wptr_0_9 ),
	.I3(\fifo_inst/Equal.rq1_wptr_0_10 ),
	.F(\fifo_inst/Equal.wcount_r [8])
);
defparam \fifo_inst/Equal.wcount_r_8_s1 .INIT=16'h6000;
LUT3 \fifo_inst/Equal.wcount_r_9_s2  (
	.I0(\fifo_inst/Equal.wcount_r_9_4 ),
	.I1(\fifo_inst/Equal.rq1_wptr_0_9 ),
	.I2(\fifo_inst/Equal.rq1_wptr_0_10 ),
	.F(\fifo_inst/Equal.wcount_r [9])
);
defparam \fifo_inst/Equal.wcount_r_9_s2 .INIT=8'h80;
LUT4 \fifo_inst/Equal.wcount_r_11_s2  (
	.I0(\fifo_inst/Equal.rq2_wptr [12]),
	.I1(\fifo_inst/Equal.rq2_wptr [11]),
	.I2(\fifo_inst/Equal.rq1_wptr_0_9 ),
	.I3(\fifo_inst/Equal.rq1_wptr_0_10 ),
	.F(\fifo_inst/Equal.wcount_r [11])
);
defparam \fifo_inst/Equal.wcount_r_11_s2 .INIT=16'h6000;
LUT3 \fifo_inst/rempty_val_s2  (
	.I0(\fifo_inst/wptr [12]),
	.I1(\fifo_inst/rbin_num [12]),
	.I2(\fifo_inst/n155_3 ),
	.F(\fifo_inst/rempty_val )
);
defparam \fifo_inst/rempty_val_s2 .INIT=8'h09;
LUT4 \fifo_inst/Equal.rgraynext_0_s1  (
	.I0(\fifo_inst/rbin_num_next [1]),
	.I1(\fifo_inst/rbin_num [0]),
	.I2(Empty),
	.I3(RdEn),
	.F(\fifo_inst/Equal.rgraynext [0])
);
defparam \fifo_inst/Equal.rgraynext_0_s1 .INIT=16'h6966;
DFFC \fifo_inst/rbin_num_12_s0  (
	.D(\fifo_inst/rbin_num_next [12]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(\fifo_inst/rbin_num [12])
);
defparam \fifo_inst/rbin_num_12_s0 .INIT=1'b0;
DFFC \fifo_inst/rbin_num_11_s0  (
	.D(\fifo_inst/rbin_num_next [11]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(\fifo_inst/rbin_num [11])
);
defparam \fifo_inst/rbin_num_11_s0 .INIT=1'b0;
DFFC \fifo_inst/rbin_num_10_s0  (
	.D(\fifo_inst/rbin_num_next [10]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(\fifo_inst/rbin_num [10])
);
defparam \fifo_inst/rbin_num_10_s0 .INIT=1'b0;
DFFC \fifo_inst/rbin_num_9_s0  (
	.D(\fifo_inst/rbin_num_next [9]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(\fifo_inst/rbin_num [9])
);
defparam \fifo_inst/rbin_num_9_s0 .INIT=1'b0;
DFFC \fifo_inst/rbin_num_8_s0  (
	.D(\fifo_inst/rbin_num_next [8]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(\fifo_inst/rbin_num [8])
);
defparam \fifo_inst/rbin_num_8_s0 .INIT=1'b0;
DFFC \fifo_inst/rbin_num_7_s0  (
	.D(\fifo_inst/rbin_num_next [7]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(\fifo_inst/rbin_num [7])
);
defparam \fifo_inst/rbin_num_7_s0 .INIT=1'b0;
DFFC \fifo_inst/rbin_num_6_s0  (
	.D(\fifo_inst/rbin_num_next [6]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(\fifo_inst/rbin_num [6])
);
defparam \fifo_inst/rbin_num_6_s0 .INIT=1'b0;
DFFC \fifo_inst/rbin_num_5_s0  (
	.D(\fifo_inst/rbin_num_next [5]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(\fifo_inst/rbin_num [5])
);
defparam \fifo_inst/rbin_num_5_s0 .INIT=1'b0;
DFFC \fifo_inst/rbin_num_4_s0  (
	.D(\fifo_inst/rbin_num_next [4]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(\fifo_inst/rbin_num [4])
);
defparam \fifo_inst/rbin_num_4_s0 .INIT=1'b0;
DFFC \fifo_inst/rbin_num_3_s0  (
	.D(\fifo_inst/rbin_num_next [3]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(\fifo_inst/rbin_num [3])
);
defparam \fifo_inst/rbin_num_3_s0 .INIT=1'b0;
DFFC \fifo_inst/rbin_num_2_s0  (
	.D(\fifo_inst/rbin_num_next [2]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(\fifo_inst/rbin_num [2])
);
defparam \fifo_inst/rbin_num_2_s0 .INIT=1'b0;
DFFC \fifo_inst/rbin_num_1_s0  (
	.D(\fifo_inst/rbin_num_next [1]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(\fifo_inst/rbin_num [1])
);
defparam \fifo_inst/rbin_num_1_s0 .INIT=1'b0;
DFFC \fifo_inst/rbin_num_0_s0  (
	.D(\fifo_inst/rbin_num_next_0_9 ),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(\fifo_inst/rbin_num [0])
);
defparam \fifo_inst/rbin_num_0_s0 .INIT=1'b0;
DFFC \fifo_inst/rptr_11_s0  (
	.D(\fifo_inst/Equal.rgraynext [11]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(\fifo_inst/rptr [11])
);
defparam \fifo_inst/rptr_11_s0 .INIT=1'b0;
DFFC \fifo_inst/rptr_10_s0  (
	.D(\fifo_inst/Equal.rgraynext [10]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(\fifo_inst/rptr [10])
);
defparam \fifo_inst/rptr_10_s0 .INIT=1'b0;
DFFC \fifo_inst/rptr_9_s0  (
	.D(\fifo_inst/Equal.rgraynext [9]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(\fifo_inst/rptr [9])
);
defparam \fifo_inst/rptr_9_s0 .INIT=1'b0;
DFFC \fifo_inst/rptr_8_s0  (
	.D(\fifo_inst/Equal.rgraynext [8]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(\fifo_inst/rptr [8])
);
defparam \fifo_inst/rptr_8_s0 .INIT=1'b0;
DFFC \fifo_inst/rptr_7_s0  (
	.D(\fifo_inst/Equal.rgraynext [7]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(\fifo_inst/rptr [7])
);
defparam \fifo_inst/rptr_7_s0 .INIT=1'b0;
DFFC \fifo_inst/rptr_6_s0  (
	.D(\fifo_inst/Equal.rgraynext [6]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(\fifo_inst/rptr [6])
);
defparam \fifo_inst/rptr_6_s0 .INIT=1'b0;
DFFC \fifo_inst/rptr_5_s0  (
	.D(\fifo_inst/Equal.rgraynext [5]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(\fifo_inst/rptr [5])
);
defparam \fifo_inst/rptr_5_s0 .INIT=1'b0;
DFFC \fifo_inst/rptr_4_s0  (
	.D(\fifo_inst/Equal.rgraynext [4]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(\fifo_inst/rptr [4])
);
defparam \fifo_inst/rptr_4_s0 .INIT=1'b0;
DFFC \fifo_inst/rptr_3_s0  (
	.D(\fifo_inst/Equal.rgraynext [3]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(\fifo_inst/rptr [3])
);
defparam \fifo_inst/rptr_3_s0 .INIT=1'b0;
DFFC \fifo_inst/rptr_2_s0  (
	.D(\fifo_inst/Equal.rgraynext [2]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(\fifo_inst/rptr [2])
);
defparam \fifo_inst/rptr_2_s0 .INIT=1'b0;
DFFC \fifo_inst/rptr_1_s0  (
	.D(\fifo_inst/Equal.rgraynext [1]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(\fifo_inst/rptr [1])
);
defparam \fifo_inst/rptr_1_s0 .INIT=1'b0;
DFFC \fifo_inst/rptr_0_s0  (
	.D(\fifo_inst/Equal.rgraynext [0]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(\fifo_inst/rptr [0])
);
defparam \fifo_inst/rptr_0_s0 .INIT=1'b0;
DFFC \fifo_inst/wptr_12_s0  (
	.D(\fifo_inst/Equal.wbinnext [12]),
	.CLK(WrClk),
	.CLEAR(\fifo_inst/reset_w [1]),
	.Q(\fifo_inst/wptr [12])
);
defparam \fifo_inst/wptr_12_s0 .INIT=1'b0;
DFFC \fifo_inst/wptr_11_s0  (
	.D(\fifo_inst/Equal.wgraynext [11]),
	.CLK(WrClk),
	.CLEAR(\fifo_inst/reset_w [1]),
	.Q(\fifo_inst/wptr [11])
);
defparam \fifo_inst/wptr_11_s0 .INIT=1'b0;
DFFC \fifo_inst/wptr_10_s0  (
	.D(\fifo_inst/Equal.wgraynext [10]),
	.CLK(WrClk),
	.CLEAR(\fifo_inst/reset_w [1]),
	.Q(\fifo_inst/wptr [10])
);
defparam \fifo_inst/wptr_10_s0 .INIT=1'b0;
DFFC \fifo_inst/wptr_9_s0  (
	.D(\fifo_inst/Equal.wgraynext [9]),
	.CLK(WrClk),
	.CLEAR(\fifo_inst/reset_w [1]),
	.Q(\fifo_inst/wptr [9])
);
defparam \fifo_inst/wptr_9_s0 .INIT=1'b0;
DFFC \fifo_inst/wptr_8_s0  (
	.D(\fifo_inst/Equal.wgraynext [8]),
	.CLK(WrClk),
	.CLEAR(\fifo_inst/reset_w [1]),
	.Q(\fifo_inst/wptr [8])
);
defparam \fifo_inst/wptr_8_s0 .INIT=1'b0;
DFFC \fifo_inst/wptr_7_s0  (
	.D(\fifo_inst/Equal.wgraynext [7]),
	.CLK(WrClk),
	.CLEAR(\fifo_inst/reset_w [1]),
	.Q(\fifo_inst/wptr [7])
);
defparam \fifo_inst/wptr_7_s0 .INIT=1'b0;
DFFC \fifo_inst/wptr_6_s0  (
	.D(\fifo_inst/Equal.wgraynext [6]),
	.CLK(WrClk),
	.CLEAR(\fifo_inst/reset_w [1]),
	.Q(\fifo_inst/wptr [6])
);
defparam \fifo_inst/wptr_6_s0 .INIT=1'b0;
DFFC \fifo_inst/wptr_5_s0  (
	.D(\fifo_inst/Equal.wgraynext [5]),
	.CLK(WrClk),
	.CLEAR(\fifo_inst/reset_w [1]),
	.Q(\fifo_inst/wptr [5])
);
defparam \fifo_inst/wptr_5_s0 .INIT=1'b0;
DFFC \fifo_inst/wptr_4_s0  (
	.D(\fifo_inst/Equal.wgraynext [4]),
	.CLK(WrClk),
	.CLEAR(\fifo_inst/reset_w [1]),
	.Q(\fifo_inst/wptr [4])
);
defparam \fifo_inst/wptr_4_s0 .INIT=1'b0;
DFFC \fifo_inst/wptr_3_s0  (
	.D(\fifo_inst/Equal.wgraynext [3]),
	.CLK(WrClk),
	.CLEAR(\fifo_inst/reset_w [1]),
	.Q(\fifo_inst/wptr [3])
);
defparam \fifo_inst/wptr_3_s0 .INIT=1'b0;
DFFC \fifo_inst/wptr_2_s0  (
	.D(\fifo_inst/Equal.wgraynext [2]),
	.CLK(WrClk),
	.CLEAR(\fifo_inst/reset_w [1]),
	.Q(\fifo_inst/wptr [2])
);
defparam \fifo_inst/wptr_2_s0 .INIT=1'b0;
DFFC \fifo_inst/wptr_1_s0  (
	.D(\fifo_inst/Equal.wgraynext [1]),
	.CLK(WrClk),
	.CLEAR(\fifo_inst/reset_w [1]),
	.Q(\fifo_inst/wptr [1])
);
defparam \fifo_inst/wptr_1_s0 .INIT=1'b0;
DFFC \fifo_inst/wptr_0_s0  (
	.D(\fifo_inst/Equal.wgraynext [0]),
	.CLK(WrClk),
	.CLEAR(\fifo_inst/reset_w [1]),
	.Q(\fifo_inst/wptr [0])
);
defparam \fifo_inst/wptr_0_s0 .INIT=1'b0;
DFFC \fifo_inst/Equal.wbin_11_s0  (
	.D(\fifo_inst/Equal.wbinnext [11]),
	.CLK(WrClk),
	.CLEAR(\fifo_inst/reset_w [1]),
	.Q(\fifo_inst/Equal.wbin [11])
);
defparam \fifo_inst/Equal.wbin_11_s0 .INIT=1'b0;
DFFC \fifo_inst/Equal.wbin_10_s0  (
	.D(\fifo_inst/Equal.wbinnext [10]),
	.CLK(WrClk),
	.CLEAR(\fifo_inst/reset_w [1]),
	.Q(\fifo_inst/Equal.wbin [10])
);
defparam \fifo_inst/Equal.wbin_10_s0 .INIT=1'b0;
DFFC \fifo_inst/Equal.wbin_9_s0  (
	.D(\fifo_inst/Equal.wbinnext [9]),
	.CLK(WrClk),
	.CLEAR(\fifo_inst/reset_w [1]),
	.Q(\fifo_inst/Equal.wbin [9])
);
defparam \fifo_inst/Equal.wbin_9_s0 .INIT=1'b0;
DFFC \fifo_inst/Equal.wbin_8_s0  (
	.D(\fifo_inst/Equal.wbinnext [8]),
	.CLK(WrClk),
	.CLEAR(\fifo_inst/reset_w [1]),
	.Q(\fifo_inst/Equal.wbin [8])
);
defparam \fifo_inst/Equal.wbin_8_s0 .INIT=1'b0;
DFFC \fifo_inst/Equal.wbin_7_s0  (
	.D(\fifo_inst/Equal.wbinnext [7]),
	.CLK(WrClk),
	.CLEAR(\fifo_inst/reset_w [1]),
	.Q(\fifo_inst/Equal.wbin [7])
);
defparam \fifo_inst/Equal.wbin_7_s0 .INIT=1'b0;
DFFC \fifo_inst/Equal.wbin_6_s0  (
	.D(\fifo_inst/Equal.wbinnext [6]),
	.CLK(WrClk),
	.CLEAR(\fifo_inst/reset_w [1]),
	.Q(\fifo_inst/Equal.wbin [6])
);
defparam \fifo_inst/Equal.wbin_6_s0 .INIT=1'b0;
DFFC \fifo_inst/Equal.wbin_5_s0  (
	.D(\fifo_inst/Equal.wbinnext [5]),
	.CLK(WrClk),
	.CLEAR(\fifo_inst/reset_w [1]),
	.Q(\fifo_inst/Equal.wbin [5])
);
defparam \fifo_inst/Equal.wbin_5_s0 .INIT=1'b0;
DFFC \fifo_inst/Equal.wbin_4_s0  (
	.D(\fifo_inst/Equal.wbinnext [4]),
	.CLK(WrClk),
	.CLEAR(\fifo_inst/reset_w [1]),
	.Q(\fifo_inst/Equal.wbin [4])
);
defparam \fifo_inst/Equal.wbin_4_s0 .INIT=1'b0;
DFFC \fifo_inst/Equal.wbin_3_s0  (
	.D(\fifo_inst/Equal.wbinnext [3]),
	.CLK(WrClk),
	.CLEAR(\fifo_inst/reset_w [1]),
	.Q(\fifo_inst/Equal.wbin [3])
);
defparam \fifo_inst/Equal.wbin_3_s0 .INIT=1'b0;
DFFC \fifo_inst/Equal.wbin_2_s0  (
	.D(\fifo_inst/Equal.wbinnext [2]),
	.CLK(WrClk),
	.CLEAR(\fifo_inst/reset_w [1]),
	.Q(\fifo_inst/Equal.wbin [2])
);
defparam \fifo_inst/Equal.wbin_2_s0 .INIT=1'b0;
DFFC \fifo_inst/Equal.wbin_1_s0  (
	.D(\fifo_inst/Equal.wbinnext [1]),
	.CLK(WrClk),
	.CLEAR(\fifo_inst/reset_w [1]),
	.Q(\fifo_inst/Equal.wbin [1])
);
defparam \fifo_inst/Equal.wbin_1_s0 .INIT=1'b0;
DFFC \fifo_inst/Equal.wbin_0_s0  (
	.D(\fifo_inst/Equal.wbinnext_0_7 ),
	.CLK(WrClk),
	.CLEAR(\fifo_inst/reset_w [1]),
	.Q(\fifo_inst/Equal.wbin [0])
);
defparam \fifo_inst/Equal.wbin_0_s0 .INIT=1'b0;
DFFC \fifo_inst/Equal.wcount_r_d_12_s0  (
	.D(\fifo_inst/Equal.rq2_wptr_12_11 ),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(\fifo_inst/Equal.wcount_r_d [12])
);
defparam \fifo_inst/Equal.wcount_r_d_12_s0 .INIT=1'b0;
DFFC \fifo_inst/Equal.wcount_r_d_11_s0  (
	.D(\fifo_inst/Equal.wcount_r [11]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(\fifo_inst/Equal.wcount_r_d [11])
);
defparam \fifo_inst/Equal.wcount_r_d_11_s0 .INIT=1'b0;
DFFC \fifo_inst/Equal.wcount_r_d_10_s0  (
	.D(\fifo_inst/Equal.wcount_r [10]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(\fifo_inst/Equal.wcount_r_d [10])
);
defparam \fifo_inst/Equal.wcount_r_d_10_s0 .INIT=1'b0;
DFFC \fifo_inst/Equal.wcount_r_d_9_s0  (
	.D(\fifo_inst/Equal.wcount_r [9]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(\fifo_inst/Equal.wcount_r_d [9])
);
defparam \fifo_inst/Equal.wcount_r_d_9_s0 .INIT=1'b0;
DFFC \fifo_inst/Equal.wcount_r_d_8_s0  (
	.D(\fifo_inst/Equal.wcount_r [8]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(\fifo_inst/Equal.wcount_r_d [8])
);
defparam \fifo_inst/Equal.wcount_r_d_8_s0 .INIT=1'b0;
DFFC \fifo_inst/Equal.wcount_r_d_7_s0  (
	.D(\fifo_inst/Equal.wcount_r [7]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(\fifo_inst/Equal.wcount_r_d [7])
);
defparam \fifo_inst/Equal.wcount_r_d_7_s0 .INIT=1'b0;
DFFC \fifo_inst/Equal.wcount_r_d_6_s0  (
	.D(\fifo_inst/Equal.wcount_r [6]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(\fifo_inst/Equal.wcount_r_d [6])
);
defparam \fifo_inst/Equal.wcount_r_d_6_s0 .INIT=1'b0;
DFFC \fifo_inst/Equal.wcount_r_d_5_s0  (
	.D(\fifo_inst/Equal.wcount_r [5]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(\fifo_inst/Equal.wcount_r_d [5])
);
defparam \fifo_inst/Equal.wcount_r_d_5_s0 .INIT=1'b0;
DFFC \fifo_inst/Equal.wcount_r_d_4_s0  (
	.D(\fifo_inst/Equal.wcount_r [4]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(\fifo_inst/Equal.wcount_r_d [4])
);
defparam \fifo_inst/Equal.wcount_r_d_4_s0 .INIT=1'b0;
DFFC \fifo_inst/Equal.wcount_r_d_3_s0  (
	.D(\fifo_inst/Equal.wcount_r [3]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(\fifo_inst/Equal.wcount_r_d [3])
);
defparam \fifo_inst/Equal.wcount_r_d_3_s0 .INIT=1'b0;
DFFC \fifo_inst/Equal.wcount_r_d_2_s0  (
	.D(\fifo_inst/Equal.wcount_r [2]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(\fifo_inst/Equal.wcount_r_d [2])
);
defparam \fifo_inst/Equal.wcount_r_d_2_s0 .INIT=1'b0;
DFFC \fifo_inst/Equal.wcount_r_d_1_s0  (
	.D(\fifo_inst/Equal.wcount_r [1]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(\fifo_inst/Equal.wcount_r_d [1])
);
defparam \fifo_inst/Equal.wcount_r_d_1_s0 .INIT=1'b0;
DFFC \fifo_inst/Equal.wcount_r_d_0_s0  (
	.D(\fifo_inst/Equal.wcount_r [0]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(\fifo_inst/Equal.wcount_r_d [0])
);
defparam \fifo_inst/Equal.wcount_r_d_0_s0 .INIT=1'b0;
DFFP \fifo_inst/rempty_val1_s0  (
	.D(\fifo_inst/rempty_val ),
	.CLK(RdClk),
	.PRESET(\fifo_inst/n485_3 ),
	.Q(\fifo_inst/rempty_val1 )
);
defparam \fifo_inst/rempty_val1_s0 .INIT=1'b1;
DFFP \fifo_inst/Empty_s0  (
	.D(\fifo_inst/rempty_val1 ),
	.CLK(RdClk),
	.PRESET(\fifo_inst/n485_3 ),
	.Q(Empty)
);
defparam \fifo_inst/Empty_s0 .INIT=1'b1;
DFFC \fifo_inst/Rnum_12_s0  (
	.D(\fifo_inst/rcnt_sub [12]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(Rnum[12])
);
defparam \fifo_inst/Rnum_12_s0 .INIT=1'b0;
DFFC \fifo_inst/Rnum_11_s0  (
	.D(\fifo_inst/rcnt_sub [11]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(Rnum[11])
);
defparam \fifo_inst/Rnum_11_s0 .INIT=1'b0;
DFFC \fifo_inst/Rnum_10_s0  (
	.D(\fifo_inst/rcnt_sub [10]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(Rnum[10])
);
defparam \fifo_inst/Rnum_10_s0 .INIT=1'b0;
DFFC \fifo_inst/Rnum_9_s0  (
	.D(\fifo_inst/rcnt_sub [9]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(Rnum[9])
);
defparam \fifo_inst/Rnum_9_s0 .INIT=1'b0;
DFFC \fifo_inst/Rnum_8_s0  (
	.D(\fifo_inst/rcnt_sub [8]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(Rnum[8])
);
defparam \fifo_inst/Rnum_8_s0 .INIT=1'b0;
DFFC \fifo_inst/Rnum_7_s0  (
	.D(\fifo_inst/rcnt_sub [7]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(Rnum[7])
);
defparam \fifo_inst/Rnum_7_s0 .INIT=1'b0;
DFFC \fifo_inst/Rnum_6_s0  (
	.D(\fifo_inst/rcnt_sub [6]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(Rnum[6])
);
defparam \fifo_inst/Rnum_6_s0 .INIT=1'b0;
DFFC \fifo_inst/Rnum_5_s0  (
	.D(\fifo_inst/rcnt_sub [5]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(Rnum[5])
);
defparam \fifo_inst/Rnum_5_s0 .INIT=1'b0;
DFFC \fifo_inst/Rnum_4_s0  (
	.D(\fifo_inst/rcnt_sub [4]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(Rnum[4])
);
defparam \fifo_inst/Rnum_4_s0 .INIT=1'b0;
DFFC \fifo_inst/Rnum_3_s0  (
	.D(\fifo_inst/rcnt_sub [3]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(Rnum[3])
);
defparam \fifo_inst/Rnum_3_s0 .INIT=1'b0;
DFFC \fifo_inst/Rnum_2_s0  (
	.D(\fifo_inst/rcnt_sub [2]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(Rnum[2])
);
defparam \fifo_inst/Rnum_2_s0 .INIT=1'b0;
DFFC \fifo_inst/Rnum_1_s0  (
	.D(\fifo_inst/rcnt_sub [1]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(Rnum[1])
);
defparam \fifo_inst/Rnum_1_s0 .INIT=1'b0;
DFFC \fifo_inst/Rnum_0_s0  (
	.D(\fifo_inst/rcnt_sub [0]),
	.CLK(RdClk),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(Rnum[0])
);
defparam \fifo_inst/Rnum_0_s0 .INIT=1'b0;
DFFC \fifo_inst/wfull_val1_s0  (
	.D(\fifo_inst/wfull_val ),
	.CLK(WrClk),
	.CLEAR(\fifo_inst/reset_w [1]),
	.Q(\fifo_inst/wfull_val1_2 )
);
defparam \fifo_inst/wfull_val1_s0 .INIT=1'b0;
DFFP \fifo_inst/wfull_val1_s1  (
	.D(\fifo_inst/wfull_val ),
	.CLK(WrClk),
	.PRESET(\fifo_inst/n698_3 ),
	.Q(\fifo_inst/wfull_val1_3 )
);
defparam \fifo_inst/wfull_val1_s1 .INIT=1'b1;
DFFC \fifo_inst/Full_s0  (
	.D(\fifo_inst/wfull_val1 ),
	.CLK(WrClk),
	.CLEAR(\fifo_inst/reset_w [1]),
	.Q(\fifo_inst/Full_1 )
);
defparam \fifo_inst/Full_s0 .INIT=1'b0;
DFFP \fifo_inst/Full_s1  (
	.D(\fifo_inst/wfull_val1 ),
	.CLK(WrClk),
	.PRESET(\fifo_inst/n698_3 ),
	.Q(\fifo_inst/Full_2 )
);
defparam \fifo_inst/Full_s1 .INIT=1'b1;
DFF \fifo_inst/Equal.rq1_wptr_0_s2  (
	.D(\fifo_inst/Equal.rq1_wptr_0_24 ),
	.CLK(RdClk),
	.Q(\fifo_inst/Equal.rq1_wptr_0_5 )
);
defparam \fifo_inst/Equal.rq1_wptr_0_s2 .INIT=1'b0;
DFFCE \fifo_inst/Equal.rq1_wptr_0_s4  (
	.D(VCC),
	.CLK(RdClk),
	.CE(\fifo_inst/Equal.rq1_wptr_0_24 ),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(\fifo_inst/Equal.rq1_wptr_0_9 )
);
defparam \fifo_inst/Equal.rq1_wptr_0_s4 .INIT=1'b0;
DFFCE \fifo_inst/Equal.rq1_wptr_0_s5  (
	.D(VCC),
	.CLK(RdClk),
	.CE(\fifo_inst/Equal.rq1_wptr_0_5 ),
	.CLEAR(\fifo_inst/reset_r [1]),
	.Q(\fifo_inst/Equal.rq1_wptr_0_10 )
);
defparam \fifo_inst/Equal.rq1_wptr_0_s5 .INIT=1'b0;
DLCE \fifo_inst/wfull_val1_s4  (
	.D(\fifo_inst/n698_3 ),
	.G(\fifo_inst/wfull_val ),
	.CLEAR(\fifo_inst/reset_w [1]),
	.CE(\fifo_inst/wfull_val1_14 ),
	.Q(\fifo_inst/wfull_val1_9 )
);
defparam \fifo_inst/wfull_val1_s4 .INIT=1'b0;
DFFNP \fifo_inst/reset_r_0_s1  (
	.D(GND),
	.CLK(RdClk),
	.PRESET(Reset),
	.Q(\fifo_inst/reset_r [0])
);
defparam \fifo_inst/reset_r_0_s1 .INIT=1'b1;
DFFNP \fifo_inst/reset_w_1_s1  (
	.D(\fifo_inst/reset_w [0]),
	.CLK(WrClk),
	.PRESET(Reset),
	.Q(\fifo_inst/reset_w [1])
);
defparam \fifo_inst/reset_w_1_s1 .INIT=1'b1;
DFFNP \fifo_inst/reset_w_0_s1  (
	.D(GND),
	.CLK(WrClk),
	.PRESET(Reset),
	.Q(\fifo_inst/reset_w [0])
);
defparam \fifo_inst/reset_w_0_s1 .INIT=1'b1;
DFFNP \fifo_inst/reset_r_1_s1  (
	.D(\fifo_inst/reset_r [0]),
	.CLK(RdClk),
	.PRESET(Reset),
	.Q(\fifo_inst/reset_r [1])
);
defparam \fifo_inst/reset_r_1_s1 .INIT=1'b1;
SDPB \fifo_inst/Equal.mem_Equal.mem_0_0_s  (
	.CLKA(WrClk),
	.CEA(\fifo_inst/n31_5 ),
	.RESETA(GND),
	.CLKB(RdClk),
	.CEB(\fifo_inst/n35_3 ),
	.RESETB(\fifo_inst/reset_r [1]),
	.OCE(\fifo_inst/n35_3 ),
	.BLKSELA({GND, GND, GND}),
	.BLKSELB({GND, GND, GND}),
	.DI({GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, Data[3:0]}),
	.ADA({\fifo_inst/Equal.wbin [11:0], GND, GND}),
	.ADB({\fifo_inst/rbin_num [11:0], GND, GND}),
	.DO({\fifo_inst/DO [31:4], Q[3:0]})
);
defparam \fifo_inst/Equal.mem_Equal.mem_0_0_s .READ_MODE=1'b1;
defparam \fifo_inst/Equal.mem_Equal.mem_0_0_s .BIT_WIDTH_0=4;
defparam \fifo_inst/Equal.mem_Equal.mem_0_0_s .BIT_WIDTH_1=4;
defparam \fifo_inst/Equal.mem_Equal.mem_0_0_s .RESET_MODE="ASYNC";
defparam \fifo_inst/Equal.mem_Equal.mem_0_0_s .BLK_SEL_0=3'b000;
defparam \fifo_inst/Equal.mem_Equal.mem_0_0_s .BLK_SEL_1=3'b000;
SDPB \fifo_inst/Equal.mem_Equal.mem_0_1_s  (
	.CLKA(WrClk),
	.CEA(\fifo_inst/n31_5 ),
	.RESETA(GND),
	.CLKB(RdClk),
	.CEB(\fifo_inst/n35_3 ),
	.RESETB(\fifo_inst/reset_r [1]),
	.OCE(\fifo_inst/n35_3 ),
	.BLKSELA({GND, GND, GND}),
	.BLKSELB({GND, GND, GND}),
	.DI({GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, Data[7:4]}),
	.ADA({\fifo_inst/Equal.wbin [11:0], GND, GND}),
	.ADB({\fifo_inst/rbin_num [11:0], GND, GND}),
	.DO({\fifo_inst/DO_0 [31:4], Q[7:4]})
);
defparam \fifo_inst/Equal.mem_Equal.mem_0_1_s .READ_MODE=1'b1;
defparam \fifo_inst/Equal.mem_Equal.mem_0_1_s .BIT_WIDTH_0=4;
defparam \fifo_inst/Equal.mem_Equal.mem_0_1_s .BIT_WIDTH_1=4;
defparam \fifo_inst/Equal.mem_Equal.mem_0_1_s .RESET_MODE="ASYNC";
defparam \fifo_inst/Equal.mem_Equal.mem_0_1_s .BLK_SEL_0=3'b000;
defparam \fifo_inst/Equal.mem_Equal.mem_0_1_s .BLK_SEL_1=3'b000;
SDPB \fifo_inst/Equal.mem_Equal.mem_0_2_s  (
	.CLKA(WrClk),
	.CEA(\fifo_inst/n31_5 ),
	.RESETA(GND),
	.CLKB(RdClk),
	.CEB(\fifo_inst/n35_3 ),
	.RESETB(\fifo_inst/reset_r [1]),
	.OCE(\fifo_inst/n35_3 ),
	.BLKSELA({GND, GND, GND}),
	.BLKSELB({GND, GND, GND}),
	.DI({GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, Data[11:8]}),
	.ADA({\fifo_inst/Equal.wbin [11:0], GND, GND}),
	.ADB({\fifo_inst/rbin_num [11:0], GND, GND}),
	.DO({\fifo_inst/DO_1 [31:4], Q[11:8]})
);
defparam \fifo_inst/Equal.mem_Equal.mem_0_2_s .READ_MODE=1'b1;
defparam \fifo_inst/Equal.mem_Equal.mem_0_2_s .BIT_WIDTH_0=4;
defparam \fifo_inst/Equal.mem_Equal.mem_0_2_s .BIT_WIDTH_1=4;
defparam \fifo_inst/Equal.mem_Equal.mem_0_2_s .RESET_MODE="ASYNC";
defparam \fifo_inst/Equal.mem_Equal.mem_0_2_s .BLK_SEL_0=3'b000;
defparam \fifo_inst/Equal.mem_Equal.mem_0_2_s .BLK_SEL_1=3'b000;
SDPB \fifo_inst/Equal.mem_Equal.mem_0_3_s  (
	.CLKA(WrClk),
	.CEA(\fifo_inst/n31_5 ),
	.RESETA(GND),
	.CLKB(RdClk),
	.CEB(\fifo_inst/n35_3 ),
	.RESETB(\fifo_inst/reset_r [1]),
	.OCE(\fifo_inst/n35_3 ),
	.BLKSELA({GND, GND, GND}),
	.BLKSELB({GND, GND, GND}),
	.DI({GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, GND, Data[15:12]}),
	.ADA({\fifo_inst/Equal.wbin [11:0], GND, GND}),
	.ADB({\fifo_inst/rbin_num [11:0], GND, GND}),
	.DO({\fifo_inst/DO_2 [31:4], Q[15:12]})
);
defparam \fifo_inst/Equal.mem_Equal.mem_0_3_s .READ_MODE=1'b1;
defparam \fifo_inst/Equal.mem_Equal.mem_0_3_s .BIT_WIDTH_0=4;
defparam \fifo_inst/Equal.mem_Equal.mem_0_3_s .BIT_WIDTH_1=4;
defparam \fifo_inst/Equal.mem_Equal.mem_0_3_s .RESET_MODE="ASYNC";
defparam \fifo_inst/Equal.mem_Equal.mem_0_3_s .BLK_SEL_0=3'b000;
defparam \fifo_inst/Equal.mem_Equal.mem_0_3_s .BLK_SEL_1=3'b000;
RAM16S4 \fifo_inst/Equal.rq1_wptr_0_s8  (
	.CLK(RdClk),
	.WRE(VCC),
	.AD({GND, GND, GND, \fifo_inst/Equal.rq1_wptr_0_5 }),
	.DI({\fifo_inst/wptr [3:0]}),
	.DO({\fifo_inst/Equal.rq2_wptr [3:0]}));
defparam \fifo_inst/Equal.rq1_wptr_0_s8 .INIT_0=16'h0000;
defparam \fifo_inst/Equal.rq1_wptr_0_s8 .INIT_1=16'h0000;
defparam \fifo_inst/Equal.rq1_wptr_0_s8 .INIT_2=16'h0000;
defparam \fifo_inst/Equal.rq1_wptr_0_s8 .INIT_3=16'h0000;
RAM16S4 \fifo_inst/Equal.rq1_wptr_0_s10  (
	.CLK(RdClk),
	.WRE(VCC),
	.AD({GND, GND, GND, \fifo_inst/Equal.rq1_wptr_0_5 }),
	.DI({\fifo_inst/wptr [7:4]}),
	.DO({\fifo_inst/Equal.rq2_wptr [7:4]}));
defparam \fifo_inst/Equal.rq1_wptr_0_s10 .INIT_0=16'h0000;
defparam \fifo_inst/Equal.rq1_wptr_0_s10 .INIT_1=16'h0000;
defparam \fifo_inst/Equal.rq1_wptr_0_s10 .INIT_2=16'h0000;
defparam \fifo_inst/Equal.rq1_wptr_0_s10 .INIT_3=16'h0000;
RAM16S4 \fifo_inst/Equal.rq1_wptr_0_s12  (
	.CLK(RdClk),
	.WRE(VCC),
	.AD({GND, GND, GND, \fifo_inst/Equal.rq1_wptr_0_5 }),
	.DI({\fifo_inst/wptr [11:8]}),
	.DO({\fifo_inst/Equal.rq2_wptr [11:8]}));
defparam \fifo_inst/Equal.rq1_wptr_0_s12 .INIT_0=16'h0000;
defparam \fifo_inst/Equal.rq1_wptr_0_s12 .INIT_1=16'h0000;
defparam \fifo_inst/Equal.rq1_wptr_0_s12 .INIT_2=16'h0000;
defparam \fifo_inst/Equal.rq1_wptr_0_s12 .INIT_3=16'h0000;
RAM16S4 \fifo_inst/Equal.rq1_wptr_0_s14  (
	.CLK(RdClk),
	.WRE(VCC),
	.AD({GND, GND, GND, \fifo_inst/Equal.rq1_wptr_0_5 }),
	.DI({GND, GND, GND, \fifo_inst/wptr [12]}),
	.DO({\fifo_inst/DO_3 [3:1], \fifo_inst/Equal.rq2_wptr [12]}));
defparam \fifo_inst/Equal.rq1_wptr_0_s14 .INIT_0=16'h0000;
defparam \fifo_inst/Equal.rq1_wptr_0_s14 .INIT_1=16'h0000;
defparam \fifo_inst/Equal.rq1_wptr_0_s14 .INIT_2=16'h0000;
defparam \fifo_inst/Equal.rq1_wptr_0_s14 .INIT_3=16'h0000;
ALU \fifo_inst/rcnt_sub_0_s  (
	.I0(\fifo_inst/Equal.wcount_r_d [0]),
	.I1(\fifo_inst/rbin_num [0]),
	.I3(GND),
	.CIN(VCC),
	.COUT(\fifo_inst/rcnt_sub_0_3 ),
	.SUM(\fifo_inst/rcnt_sub [0])
);
defparam \fifo_inst/rcnt_sub_0_s .ALU_MODE=1;
ALU \fifo_inst/rcnt_sub_1_s  (
	.I0(\fifo_inst/Equal.wcount_r_d [1]),
	.I1(\fifo_inst/rbin_num [1]),
	.I3(GND),
	.CIN(\fifo_inst/rcnt_sub_0_3 ),
	.COUT(\fifo_inst/rcnt_sub_1_3 ),
	.SUM(\fifo_inst/rcnt_sub [1])
);
defparam \fifo_inst/rcnt_sub_1_s .ALU_MODE=1;
ALU \fifo_inst/rcnt_sub_2_s  (
	.I0(\fifo_inst/Equal.wcount_r_d [2]),
	.I1(\fifo_inst/rbin_num [2]),
	.I3(GND),
	.CIN(\fifo_inst/rcnt_sub_1_3 ),
	.COUT(\fifo_inst/rcnt_sub_2_3 ),
	.SUM(\fifo_inst/rcnt_sub [2])
);
defparam \fifo_inst/rcnt_sub_2_s .ALU_MODE=1;
ALU \fifo_inst/rcnt_sub_3_s  (
	.I0(\fifo_inst/Equal.wcount_r_d [3]),
	.I1(\fifo_inst/rbin_num [3]),
	.I3(GND),
	.CIN(\fifo_inst/rcnt_sub_2_3 ),
	.COUT(\fifo_inst/rcnt_sub_3_3 ),
	.SUM(\fifo_inst/rcnt_sub [3])
);
defparam \fifo_inst/rcnt_sub_3_s .ALU_MODE=1;
ALU \fifo_inst/rcnt_sub_4_s  (
	.I0(\fifo_inst/Equal.wcount_r_d [4]),
	.I1(\fifo_inst/rbin_num [4]),
	.I3(GND),
	.CIN(\fifo_inst/rcnt_sub_3_3 ),
	.COUT(\fifo_inst/rcnt_sub_4_3 ),
	.SUM(\fifo_inst/rcnt_sub [4])
);
defparam \fifo_inst/rcnt_sub_4_s .ALU_MODE=1;
ALU \fifo_inst/rcnt_sub_5_s  (
	.I0(\fifo_inst/Equal.wcount_r_d [5]),
	.I1(\fifo_inst/rbin_num [5]),
	.I3(GND),
	.CIN(\fifo_inst/rcnt_sub_4_3 ),
	.COUT(\fifo_inst/rcnt_sub_5_3 ),
	.SUM(\fifo_inst/rcnt_sub [5])
);
defparam \fifo_inst/rcnt_sub_5_s .ALU_MODE=1;
ALU \fifo_inst/rcnt_sub_6_s  (
	.I0(\fifo_inst/Equal.wcount_r_d [6]),
	.I1(\fifo_inst/rbin_num [6]),
	.I3(GND),
	.CIN(\fifo_inst/rcnt_sub_5_3 ),
	.COUT(\fifo_inst/rcnt_sub_6_3 ),
	.SUM(\fifo_inst/rcnt_sub [6])
);
defparam \fifo_inst/rcnt_sub_6_s .ALU_MODE=1;
ALU \fifo_inst/rcnt_sub_7_s  (
	.I0(\fifo_inst/Equal.wcount_r_d [7]),
	.I1(\fifo_inst/rbin_num [7]),
	.I3(GND),
	.CIN(\fifo_inst/rcnt_sub_6_3 ),
	.COUT(\fifo_inst/rcnt_sub_7_3 ),
	.SUM(\fifo_inst/rcnt_sub [7])
);
defparam \fifo_inst/rcnt_sub_7_s .ALU_MODE=1;
ALU \fifo_inst/rcnt_sub_8_s  (
	.I0(\fifo_inst/Equal.wcount_r_d [8]),
	.I1(\fifo_inst/rbin_num [8]),
	.I3(GND),
	.CIN(\fifo_inst/rcnt_sub_7_3 ),
	.COUT(\fifo_inst/rcnt_sub_8_3 ),
	.SUM(\fifo_inst/rcnt_sub [8])
);
defparam \fifo_inst/rcnt_sub_8_s .ALU_MODE=1;
ALU \fifo_inst/rcnt_sub_9_s  (
	.I0(\fifo_inst/Equal.wcount_r_d [9]),
	.I1(\fifo_inst/rbin_num [9]),
	.I3(GND),
	.CIN(\fifo_inst/rcnt_sub_8_3 ),
	.COUT(\fifo_inst/rcnt_sub_9_3 ),
	.SUM(\fifo_inst/rcnt_sub [9])
);
defparam \fifo_inst/rcnt_sub_9_s .ALU_MODE=1;
ALU \fifo_inst/rcnt_sub_10_s  (
	.I0(\fifo_inst/Equal.wcount_r_d [10]),
	.I1(\fifo_inst/rbin_num [10]),
	.I3(GND),
	.CIN(\fifo_inst/rcnt_sub_9_3 ),
	.COUT(\fifo_inst/rcnt_sub_10_3 ),
	.SUM(\fifo_inst/rcnt_sub [10])
);
defparam \fifo_inst/rcnt_sub_10_s .ALU_MODE=1;
ALU \fifo_inst/rcnt_sub_11_s  (
	.I0(\fifo_inst/Equal.wcount_r_d [11]),
	.I1(\fifo_inst/rbin_num [11]),
	.I3(GND),
	.CIN(\fifo_inst/rcnt_sub_10_3 ),
	.COUT(\fifo_inst/rcnt_sub_11_3 ),
	.SUM(\fifo_inst/rcnt_sub [11])
);
defparam \fifo_inst/rcnt_sub_11_s .ALU_MODE=1;
ALU \fifo_inst/rcnt_sub_12_s  (
	.I0(\fifo_inst/n200_3 ),
	.I1(GND),
	.I3(GND),
	.CIN(\fifo_inst/rcnt_sub_11_3 ),
	.COUT(\fifo_inst/rcnt_sub_12_0_COUT ),
	.SUM(\fifo_inst/rcnt_sub [12])
);
defparam \fifo_inst/rcnt_sub_12_s .ALU_MODE=1;
ALU \fifo_inst/n144_s0  (
	.I0(\fifo_inst/wptr [0]),
	.I1(\fifo_inst/rptr [0]),
	.I3(GND),
	.CIN(GND),
	.COUT(\fifo_inst/n144_3 ),
	.SUM(\fifo_inst/n144_1_SUM )
);
defparam \fifo_inst/n144_s0 .ALU_MODE=3;
ALU \fifo_inst/n145_s0  (
	.I0(\fifo_inst/wptr [1]),
	.I1(\fifo_inst/rptr [1]),
	.I3(GND),
	.CIN(\fifo_inst/n144_3 ),
	.COUT(\fifo_inst/n145_3 ),
	.SUM(\fifo_inst/n145_1_SUM )
);
defparam \fifo_inst/n145_s0 .ALU_MODE=3;
ALU \fifo_inst/n146_s0  (
	.I0(\fifo_inst/wptr [2]),
	.I1(\fifo_inst/rptr [2]),
	.I3(GND),
	.CIN(\fifo_inst/n145_3 ),
	.COUT(\fifo_inst/n146_3 ),
	.SUM(\fifo_inst/n146_1_SUM )
);
defparam \fifo_inst/n146_s0 .ALU_MODE=3;
ALU \fifo_inst/n147_s0  (
	.I0(\fifo_inst/wptr [3]),
	.I1(\fifo_inst/rptr [3]),
	.I3(GND),
	.CIN(\fifo_inst/n146_3 ),
	.COUT(\fifo_inst/n147_3 ),
	.SUM(\fifo_inst/n147_1_SUM )
);
defparam \fifo_inst/n147_s0 .ALU_MODE=3;
ALU \fifo_inst/n148_s0  (
	.I0(\fifo_inst/wptr [4]),
	.I1(\fifo_inst/rptr [4]),
	.I3(GND),
	.CIN(\fifo_inst/n147_3 ),
	.COUT(\fifo_inst/n148_3 ),
	.SUM(\fifo_inst/n148_1_SUM )
);
defparam \fifo_inst/n148_s0 .ALU_MODE=3;
ALU \fifo_inst/n149_s0  (
	.I0(\fifo_inst/wptr [5]),
	.I1(\fifo_inst/rptr [5]),
	.I3(GND),
	.CIN(\fifo_inst/n148_3 ),
	.COUT(\fifo_inst/n149_3 ),
	.SUM(\fifo_inst/n149_1_SUM )
);
defparam \fifo_inst/n149_s0 .ALU_MODE=3;
ALU \fifo_inst/n150_s0  (
	.I0(\fifo_inst/wptr [6]),
	.I1(\fifo_inst/rptr [6]),
	.I3(GND),
	.CIN(\fifo_inst/n149_3 ),
	.COUT(\fifo_inst/n150_3 ),
	.SUM(\fifo_inst/n150_1_SUM )
);
defparam \fifo_inst/n150_s0 .ALU_MODE=3;
ALU \fifo_inst/n151_s0  (
	.I0(\fifo_inst/wptr [7]),
	.I1(\fifo_inst/rptr [7]),
	.I3(GND),
	.CIN(\fifo_inst/n150_3 ),
	.COUT(\fifo_inst/n151_3 ),
	.SUM(\fifo_inst/n151_1_SUM )
);
defparam \fifo_inst/n151_s0 .ALU_MODE=3;
ALU \fifo_inst/n152_s0  (
	.I0(\fifo_inst/wptr [8]),
	.I1(\fifo_inst/rptr [8]),
	.I3(GND),
	.CIN(\fifo_inst/n151_3 ),
	.COUT(\fifo_inst/n152_3 ),
	.SUM(\fifo_inst/n152_1_SUM )
);
defparam \fifo_inst/n152_s0 .ALU_MODE=3;
ALU \fifo_inst/n153_s0  (
	.I0(\fifo_inst/wptr [9]),
	.I1(\fifo_inst/rptr [9]),
	.I3(GND),
	.CIN(\fifo_inst/n152_3 ),
	.COUT(\fifo_inst/n153_3 ),
	.SUM(\fifo_inst/n153_1_SUM )
);
defparam \fifo_inst/n153_s0 .ALU_MODE=3;
ALU \fifo_inst/n154_s0  (
	.I0(\fifo_inst/wptr [10]),
	.I1(\fifo_inst/rptr [10]),
	.I3(GND),
	.CIN(\fifo_inst/n153_3 ),
	.COUT(\fifo_inst/n154_3 ),
	.SUM(\fifo_inst/n154_1_SUM )
);
defparam \fifo_inst/n154_s0 .ALU_MODE=3;
ALU \fifo_inst/n155_s0  (
	.I0(\fifo_inst/wptr [11]),
	.I1(\fifo_inst/rptr [11]),
	.I3(GND),
	.CIN(\fifo_inst/n154_3 ),
	.COUT(\fifo_inst/n155_3 ),
	.SUM(\fifo_inst/n155_1_SUM )
);
defparam \fifo_inst/n155_s0 .ALU_MODE=3;
LUT1 \fifo_inst/Equal.rq1_wptr_0_s20  (
	.I0(\fifo_inst/Equal.rq1_wptr_0_5 ),
	.F(\fifo_inst/Equal.rq1_wptr_0_24 )
);
defparam \fifo_inst/Equal.rq1_wptr_0_s20 .INIT=2'h1;
INV \fifo_inst/wfull_val1_s8  (
	.I(\fifo_inst/reset_w [1]),
	.O(\fifo_inst/wfull_val1_14 )
);
endmodule
