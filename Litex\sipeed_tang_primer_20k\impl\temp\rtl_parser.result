[{"InstFile": "G:/Gowin/workspace/TangPrimer-20K-example-main/Litex/sipeed_tang_primer_20k/src/sipeed_tang_primer_20k.v", "InstLine": 21, "InstName": "sipeed_tang_primer_20k", "ModuleFile": "G:/Gowin/workspace/TangPrimer-20K-example-main/Litex/sipeed_tang_primer_20k/src/sipeed_tang_primer_20k.v", "ModuleLine": 21, "ModuleName": "sipeed_tang_primer_20k", "SubInsts": [{"InstFile": "G:/Gowin/workspace/TangPrimer-20K-example-main/Litex/sipeed_tang_primer_20k/src/sipeed_tang_primer_20k.v", "InstLine": 12637, "InstName": "VexRiscv", "ModuleFile": "G:/Gowin/workspace/TangPrimer-20K-example-main/Litex/sipeed_tang_primer_20k/src/VexRiscv.v", "ModuleLine": 7, "ModuleName": "VexRiscv", "SubInsts": [{"InstFile": "G:/Gowin/workspace/TangPrimer-20K-example-main/Litex/sipeed_tang_primer_20k/src/VexRiscv.v", "InstLine": 1568, "InstName": "IBusCachedPlugin_cache", "ModuleFile": "G:/Gowin/workspace/TangPrimer-20K-example-main/Litex/sipeed_tang_primer_20k/src/VexRiscv.v", "ModuleLine": 6021, "ModuleName": "InstructionCache"}, {"InstFile": "G:/Gowin/workspace/TangPrimer-20K-example-main/Litex/sipeed_tang_primer_20k/src/VexRiscv.v", "InstLine": 1610, "InstName": "dataCache_1", "ModuleFile": "G:/Gowin/workspace/TangPrimer-20K-example-main/Litex/sipeed_tang_primer_20k/src/VexRiscv.v", "ModuleLine": 5167, "ModuleName": "DataCache"}]}]}]