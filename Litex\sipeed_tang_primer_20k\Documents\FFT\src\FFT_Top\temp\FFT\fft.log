GowinSynthesis start
Running parser ...
Analyzing Verilog file '/netapp/share/gwsw/gowin_new/tags/Gowin/IDE/ipcore/FFT/data/fft.v'
Analyzing included file 'defile.v'("/netapp/share/gwsw/gowin_new/tags/Gowin/IDE/ipcore/FFT/data/fft.v":2144)
Back to file '/netapp/share/gwsw/gowin_new/tags/Gowin/IDE/ipcore/FFT/data/fft.v'("/netapp/share/gwsw/gowin_new/tags/Gowin/IDE/ipcore/FFT/data/fft.v":2144)
Analyzing included file '/netapp/share/gwsw/gowin_new/tags/Gowin/IDE/ipcore/FFT/data/static_macro_define.v'("/netapp/share/gwsw/gowin_new/tags/Gowin/IDE/ipcore/FFT/data/fft.v":2144)
Back to file '/netapp/share/gwsw/gowin_new/tags/Gowin/IDE/ipcore/FFT/data/fft.v'("/netapp/share/gwsw/gowin_new/tags/Gowin/IDE/ipcore/FFT/data/fft.v":2144)
WARN  (EX3788) : Block identifier is required on this block("/netapp/share/gwsw/gowin_new/tags/Gowin/IDE/ipcore/FFT/data/fft.v":0)
WARN  (EX2830) : Data object '**' is already declared("/netapp/share/gwsw/gowin_new/tags/Gowin/IDE/ipcore/FFT/data/fft.v":0)
Previous declaration of '**' is from here("/netapp/share/gwsw/gowin_new/tags/Gowin/IDE/ipcore/FFT/data/fft.v":0)
WARN  (EX3671) : Second declaration of '**' is ignored("/netapp/share/gwsw/gowin_new/tags/Gowin/IDE/ipcore/FFT/data/fft.v":0)
Analyzing included file 'twRe.v'("/netapp/share/gwsw/gowin_new/tags/Gowin/IDE/ipcore/FFT/data/fft.v":2144)
Back to file '/netapp/share/gwsw/gowin_new/tags/Gowin/IDE/ipcore/FFT/data/fft.v'("/netapp/share/gwsw/gowin_new/tags/Gowin/IDE/ipcore/FFT/data/fft.v":2144)
Analyzing included file 'twIm.v'("/netapp/share/gwsw/gowin_new/tags/Gowin/IDE/ipcore/FFT/data/fft.v":2144)
Back to file '/netapp/share/gwsw/gowin_new/tags/Gowin/IDE/ipcore/FFT/data/fft.v'("/netapp/share/gwsw/gowin_new/tags/Gowin/IDE/ipcore/FFT/data/fft.v":2144)
Analyzing Verilog file '/netapp/share/gwsw/gowin_new/tags/Gowin/IDE/ipcore/FFT/data/interface.v'
Analyzing included file 'defile.v'("/netapp/share/gwsw/gowin_new/tags/Gowin/IDE/ipcore/FFT/data/interface.v":1)
Back to file '/netapp/share/gwsw/gowin_new/tags/Gowin/IDE/ipcore/FFT/data/interface.v'("/netapp/share/gwsw/gowin_new/tags/Gowin/IDE/ipcore/FFT/data/interface.v":1)
Analyzing included file '/netapp/share/gwsw/gowin_new/tags/Gowin/IDE/ipcore/FFT/data/static_macro_define.v'("/netapp/share/gwsw/gowin_new/tags/Gowin/IDE/ipcore/FFT/data/interface.v":2)
Back to file '/netapp/share/gwsw/gowin_new/tags/Gowin/IDE/ipcore/FFT/data/interface.v'("/netapp/share/gwsw/gowin_new/tags/Gowin/IDE/ipcore/FFT/data/interface.v":2)
Compiling module 'MOD_FFT'("/netapp/share/gwsw/gowin_new/tags/Gowin/IDE/ipcore/FFT/data/interface.v":4)
Compiling module '**'("/netapp/share/gwsw/gowin_new/tags/Gowin/IDE/ipcore/FFT/data/fft.v":0)
WARN  (EX3791) : Expression size ** truncated to fit in target size **("/netapp/share/gwsw/gowin_new/tags/Gowin/IDE/ipcore/FFT/data/fft.v":0)
Compiling module '**'("/netapp/share/gwsw/gowin_new/tags/Gowin/IDE/ipcore/FFT/data/fft.v":0)
Compiling module '**'("/netapp/share/gwsw/gowin_new/tags/Gowin/IDE/ipcore/FFT/data/fft.v":0)
Compiling module '**'("/netapp/share/gwsw/gowin_new/tags/Gowin/IDE/ipcore/FFT/data/fft.v":0)
Extracting RAM for identifier '**'("/netapp/share/gwsw/gowin_new/tags/Gowin/IDE/ipcore/FFT/data/fft.v":0)
Compiling module '**'("/netapp/share/gwsw/gowin_new/tags/Gowin/IDE/ipcore/FFT/data/fft.v":0)
WARN  (EX3791) : Expression size ** truncated to fit in target size **("/netapp/share/gwsw/gowin_new/tags/Gowin/IDE/ipcore/FFT/data/fft.v":0)
Compiling module '**'("/netapp/share/gwsw/gowin_new/tags/Gowin/IDE/ipcore/FFT/data/fft.v":0)
Compiling module '**'("/netapp/share/gwsw/gowin_new/tags/Gowin/IDE/ipcore/FFT/data/fft.v":0)
Extracting RAM for identifier '**'("/netapp/share/gwsw/gowin_new/tags/Gowin/IDE/ipcore/FFT/data/fft.v":0)
Extracting RAM for identifier '**'("/netapp/share/gwsw/gowin_new/tags/Gowin/IDE/ipcore/FFT/data/fft.v":0)
Compiling module '**'("/netapp/share/gwsw/gowin_new/tags/Gowin/IDE/ipcore/FFT/data/fft.v":0)
WARN  (EX3073) : Port '**' remains unconnected for this instance("/netapp/share/gwsw/gowin_new/tags/Gowin/IDE/ipcore/FFT/data/fft.v":0)
Compiling module '**'("/netapp/share/gwsw/gowin_new/tags/Gowin/IDE/ipcore/FFT/data/fft.v":0)
WARN  (EX3670) : Actual bit length ** differs from formal bit length ** for port '**'("/netapp/share/gwsw/gowin_new/tags/Gowin/IDE/ipcore/FFT/data/fft.v":0)
Compiling module '**'("/netapp/share/gwsw/gowin_new/tags/Gowin/IDE/ipcore/FFT/data/fft.v":0)
WARN  (EX3670) : Actual bit length ** differs from formal bit length ** for port '**'("/netapp/share/gwsw/gowin_new/tags/Gowin/IDE/ipcore/FFT/data/fft.v":0)
WARN  (EX3791) : Expression size ** truncated to fit in target size **("/netapp/share/gwsw/gowin_new/tags/Gowin/IDE/ipcore/FFT/data/fft.v":0)
Compiling module '**'("/netapp/share/gwsw/gowin_new/tags/Gowin/IDE/ipcore/FFT/data/fft.v":0)
WARN  (EX3791) : Expression size ** truncated to fit in target size **("/netapp/share/gwsw/gowin_new/tags/Gowin/IDE/ipcore/FFT/data/fft.v":0)
Compiling module '**'("/netapp/share/gwsw/gowin_new/tags/Gowin/IDE/ipcore/FFT/data/fft.v":0)
WARN  (EX3791) : Expression size ** truncated to fit in target size **("/netapp/share/gwsw/gowin_new/tags/Gowin/IDE/ipcore/FFT/data/fft.v":0)
Compiling module '**'("/netapp/share/gwsw/gowin_new/tags/Gowin/IDE/ipcore/FFT/data/fft.v":0)
WARN  (EX3791) : Expression size ** truncated to fit in target size **("/netapp/share/gwsw/gowin_new/tags/Gowin/IDE/ipcore/FFT/data/fft.v":0)
NOTE  (EX0101) : Current top module is "MOD_FFT"
[5%] Running netlist conversion ...
Running device independent optimization ...
[10%] Optimizing Phase 0 completed
[15%] Optimizing Phase 1 completed
[25%] Optimizing Phase 2 completed
Running inference ...
[30%] Inferring Phase 0 completed
[40%] Inferring Phase 1 completed
[50%] Inferring Phase 2 completed
[55%] Inferring Phase 3 completed
Running technical mapping ...
[60%] Tech-Mapping Phase 0 completed
[65%] Tech-Mapping Phase 1 completed
[75%] Tech-Mapping Phase 2 completed
[80%] Tech-Mapping Phase 3 completed
[90%] Tech-Mapping Phase 4 completed
[95%] Generate netlist file "/netapp/share/gwsw/sw_pub/swfiles/changhui/IntranetPool/SWQA/IDEtest/exampleProject/project/FFT/src/FFT_Top/temp/FFT/fft.vg" completed
Generate template file "/netapp/share/gwsw/sw_pub/swfiles/changhui/IntranetPool/SWQA/IDEtest/exampleProject/project/FFT/src/FFT_Top/temp/FFT/fft_tmp.v" completed
[100%] Generate report file "/netapp/share/gwsw/sw_pub/swfiles/changhui/IntranetPool/SWQA/IDEtest/exampleProject/project/FFT/src/FFT_Top/temp/FFT/fft_syn.rpt.html" completed
GowinSynthesis finish
